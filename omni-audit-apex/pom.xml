<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.quanzhi.omni.audit</groupId>
        <artifactId>omni-audit-parent</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>omni-audit-apex</artifactId>
    <name>omni-audit-apex</name>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <modules>
        <module>omni-audit-apex-config</module>
        <module>omni-audit-apex-api</module>
        <module>omni-audit-apex-core</module>
        <module>omni-audit-apex-formats</module>
        <module>omni-audit-apex-connectors</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
