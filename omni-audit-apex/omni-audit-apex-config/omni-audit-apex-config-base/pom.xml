<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.quanzhi.omni.audit</groupId>
        <artifactId>omni-audit-apex-config</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>omni-audit-apex-config-base</artifactId>
    <packaging>jar</packaging>

    <name>omni-audit-apex-config-base</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
            <version>1.3.3</version>
        </dependency>
    </dependencies>
    <build>

        <finalName>${project.artifactId}-${project.version}</finalName>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <configuration>
                    <minimizeJar>true</minimizeJar>
                    <createSourcesJar>true</createSourcesJar>
                    <shadeSourcesContent>true</shadeSourcesContent>
                    <shadedArtifactAttached>false</shadedArtifactAttached>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                    <filters>
                        <filter>
                            <artifact>com.typesafe:config</artifact>
                            <includes>
                                <include>**</include>
                            </includes>
                            <excludes>
                                <exclude>META-INF/MANIFEST.MF</exclude>
                                <exclude>META-INF/NOTICE</exclude>
                                <exclude>com/typesafe/config/ConfigParseOptions.class</exclude>
                                <exclude>com/typesafe/config/ConfigMergeable.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigParser.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigParser$1.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigParser$ParseContext.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigNodePath.class</exclude>
                                <exclude>com/typesafe/config/impl/PathParser.class</exclude>
                                <exclude>com/typesafe/config/impl/PathParser$Element.class</exclude>
                                <exclude>com/typesafe/config/impl/Path.class</exclude>
                                <exclude>com/typesafe/config/impl/SimpleConfigObject.class</exclude>
                                <exclude>com/typesafe/config/impl/SimpleConfigObject$1.class</exclude>
                                <exclude>com/typesafe/config/impl/SimpleConfigObject$RenderComparator.class</exclude>
                                <exclude>com/typesafe/config/impl/SimpleConfigObject$ResolveModifier.class</exclude>
                                <exclude>com/typesafe/config/impl/PropertiesParser.class</exclude>
                                <exclude>com/typesafe/config/impl/PropertiesParser$1.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigImpl.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigImpl$1.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigImpl$ClasspathNameSource.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigImpl$ClasspathNameSourceWithClass.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigImpl$DebugHolder.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigImpl$DefaultIncluderHolder.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigImpl$EnvVariablesHolder.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigImpl$FileNameSource.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigImpl$LoaderCache.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigImpl$LoaderCacheHolder.class</exclude>
                                <exclude>com/typesafe/config/impl/ConfigImpl$SystemPropertiesHolder.class</exclude>
                                <exclude>com/typesafe/config/impl/Tokenizer.class</exclude>
                                <exclude>com/typesafe/config/impl/Tokenizer$TokenIterator.class</exclude>
                                <exclude>com/typesafe/config/impl/Tokenizer$ProblemException.class</exclude>
                            </excludes>
                        </filter>
                    </filters>
                    <relocations>
                        <relocation>
                            <pattern>com.typesafe.config</pattern>
                            <shadedPattern>omni.audit.apex.shade.com.typesafe.config</shadedPattern>
                        </relocation>
                    </relocations>
                    <transformers>
                        <transformer implementation="org.apache.maven.plugins.shade.resource.ApacheLicenseResourceTransformer" />
                        <transformer implementation="org.apache.maven.plugins.shade.resource.ApacheNoticeResourceTransformer" />
                    </transformers>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <phase>package</phase>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>compile</id>
                        <goals>
                            <goal>attach-artifact</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <artifacts>
                                <artifact>
                                    <file>${basedir}/target/${project.artifactId}-${project.version}.jar</file>
                                    <type>jar</type>
                                    <classifier>optional</classifier>
                                </artifact>
                            </artifacts>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>






</project>
