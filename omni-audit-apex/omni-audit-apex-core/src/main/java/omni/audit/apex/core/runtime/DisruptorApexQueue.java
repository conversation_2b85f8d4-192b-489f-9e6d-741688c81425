package omni.audit.apex.core.runtime;

import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.WaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.core.config.DisruptorOptions;

import java.util.concurrent.ThreadFactory;

/**
 * <AUTHOR>
 * @class DisruptorQueue
 * @created 2025/6/18 10:40
 * @desc
 **/
@Slf4j
public class DisruptorApexQueue implements ApexQueue<ApexRecord> {

    private ReadonlyConfig envConfig;

    private Disruptor<ApexRecord> disruptor;

    private RingBuffer<ApexRecord> ringBuffer;

    public DisruptorApexQueue(ReadonlyConfig envConfig,
                              Transform<PERSON>hai<PERSON> transformChain,
                              int parallelism,
                              Sink<PERSON>hain sinkChain) {
        String namePrefix = "Disruptor-";
        ThreadFactory threadFactory = r -> new Thread(r, namePrefix);
        this.disruptor = new Disruptor<>(
                () -> new ApexRecord(),
                envConfig.get(DisruptorOptions.RING_BUFFER_SIZE),
                threadFactory,
                ProducerType.SINGLE,
                getWaitStrategy(envConfig)
        );
        this.ringBuffer = disruptor.getRingBuffer();
        ApexRecordHandler[] handlers = new ApexRecordHandler[parallelism];
        for (int i = 0; i < parallelism; i++) {
            handlers[i] = new ApexRecordHandler(transformChain, sinkChain);
        }
        disruptor.handleEventsWithWorkerPool(handlers);
    }

    public WaitStrategy getWaitStrategy(ReadonlyConfig envConfig) {
        String strategy = envConfig.get(DisruptorOptions.WAIT_STRATEGY);
        WaitStrategy waitStrategy = null;
        switch (strategy) {
            // TODO 祁灵 2025/6/20 16:01: wait strategy
            case "BlockingWaitStrategy":
            default:
                waitStrategy = new BlockingWaitStrategy();
        }
        return waitStrategy;
    }

    @SuppressWarnings({"UnusedAssignment", "unused"})
    public void publish(ApexRecord event) {
        long next = ringBuffer.next();
        try {
            ApexRecord apexRecord = ringBuffer.get(next);
            apexRecord.setPartition(event.getPartition());
            apexRecord.setKey(event.getKey());
            apexRecord.setValue(event.getValue());
        } catch (Exception ignored) {

        } finally {
            ringBuffer.publish(next);
        }
    }

    public void start() {
        disruptor.start();
    }

    public void stop() {
        disruptor.shutdown();
    }

}
