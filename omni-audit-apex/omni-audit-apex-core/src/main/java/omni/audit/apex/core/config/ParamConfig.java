package omni.audit.apex.core.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

public class ParamConfig {

    private Map<String, Object> paramConfig = new HashMap<>();

    public ParamConfig() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("config.json");
            if (inputStream != null) {
                paramConfig = mapper.readValue(inputStream, new TypeReference<Map<String, Object>>() {
                });
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to load flow.json configuration", e);
        }
    }

}
