package omni.audit.apex.core.config;

import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;

/**
 * <AUTHOR>
 * @class DisruptorOptions
 * @created 2025/6/20 14:53
 * @desc
 **/
public class DisruptorOptions {

    public static final Option<Integer> RING_BUFFER_SIZE =
            Options.key("disruptor.ring_buffer_size")
                   .intType()
                   .defaultValue(1024)
                   .withDescription("The size of the ring buffer.");

    public static final Option<String> WAIT_STRATEGY =
            Options.key("disruptor.wait_strategy")
                   .stringType()
                   .defaultValue("BlockingWaitStrategy")
                   .withDescription("The size of the ring buffer.");
}
