package omni.audit.apex.core.runtime;

import lombok.Data;
import lombok.Setter;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.core.dag.SinkAction;

import java.util.List;

/**
 * <AUTHOR>
 * @class SinkChain
 * @created 2025/6/18 17:28
 * @desc
 **/
@Data
public class SinkChain {

    @Setter
    private List<SinkAction> sinks;

    public void sink(ApexRecord event) {
        ApexRow value = event.getValue();
        for (SinkAction sink : sinks) {
            if (value == null) {
                break;
            }
            value = (ApexRow) sink.getSink().write(value);
        }
    }
}
