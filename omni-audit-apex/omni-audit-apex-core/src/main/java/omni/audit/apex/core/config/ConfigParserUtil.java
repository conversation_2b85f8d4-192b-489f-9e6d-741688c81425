package omni.audit.apex.core.config;

import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.shade.com.typesafe.config.Config;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public final class ConfigParserUtil {
    private ConfigParserUtil() {}

    public static Option<String> PLUGIN_NAME =
            Options.key("plugin_name")
                   .stringType()
                   .noDefaultValue()
                   .withDescription("Name of the SPI plugin class.");

    public static String getFactoryId(ReadonlyConfig readonlyConfig) {
        String pluginName = readonlyConfig.get(PLUGIN_NAME);
        if (StringUtils.isBlank(pluginName)) {
            throw new RuntimeException(
                    String.format(
                            "The '%s' option is not configured, please configure it.",
                            PLUGIN_NAME.key()));
        }
        return pluginName;
    }

    public static String getFactoryId(Config config) {
        return getFactoryId(ReadonlyConfig.fromConfig(config));
    }

}