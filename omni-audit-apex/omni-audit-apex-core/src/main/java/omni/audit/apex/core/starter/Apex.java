package omni.audit.apex.core.starter;

import omni.audit.apex.api.config.ApexConfig;
import omni.audit.apex.core.dag.Job;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class Apex
 * @created 2025/6/16 18:53
 * @desc endpoint
 **/
public class Apex {

    private List<Job> jobs;

    public Apex() {
    }

    public List<String> start() throws Exception {
        // TODO 祁灵 2025/6/23 10:02: load config
        ApexConfig apexConfig = new ApexConfig();
        List<Job> jobs = ApexServer.run(apexConfig);
        List<String> jobIds = jobs.stream().map(Job::getUid).collect(Collectors.toList());
        return jobIds;
    }

    public void stop() {
        jobs.forEach(Job::stop);
    }

}
