package omni.audit.apex.core.starter;

import omni.audit.apex.api.config.ApexConfig;
import omni.audit.apex.core.dag.Job;
import omni.audit.apex.shade.com.typesafe.config.Config;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static omni.audit.apex.core.config.TypesafeConfigUtils.getConfigList;

/**
 * <AUTHOR>
 * @class Apex
 * @created 2025/6/16 19:48
 * @desc
 **/
public class Apex {

    public static List<Job> run(ApexConfig config) throws Exception {

        List<Job> jobs = new ArrayList<>();
        for (Config jobConfig : config.getJobConfigs()) {
            Job job = parser(jobConfig);
            if (job == null) {
                throw new Exception("Invalid job config");
            }
            jobs.add(job);
        }
        jobs.forEach(Job::start);
        // TODO 祁灵 2025/6/17 19:36: 检查任务状态

        return jobs;
    }

    /**
     * 单个任务
     *
     * @param jobConfig
     */
    private static Job parser(Config jobConfig) {
        // TODO 祁灵 2025/6/16 20:13: 解析配置转换 source sink transform
        Config envConfig = jobConfig.getConfig("env");
        List<? extends Config> sourceConfigs = getConfigList(jobConfig, "source", Collections.emptyList());
        if (sourceConfigs.isEmpty()) {
            throw new RuntimeException("At least one source is required");
        } else if (sourceConfigs.size() > 1) {
            throw new RuntimeException("Only one source is supported");
        }
        List<? extends Config> transformConfigs = getConfigList(jobConfig, "transform", Collections.emptyList());
        List<? extends Config> sinkConfigs = getConfigList(jobConfig, "sink", Collections.emptyList());
        if (sinkConfigs.isEmpty()) {
            throw new RuntimeException("At least one sink is required");
        }

        Job job = new Job(envConfig, sourceConfigs.get(0), transformConfigs, sinkConfigs);
        job.parser();
        return job;

    }

}
