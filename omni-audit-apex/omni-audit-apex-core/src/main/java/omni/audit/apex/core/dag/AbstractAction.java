package omni.audit.apex.core.dag;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @class AbstractAction
 * @created 2025/6/18 15:04
 * @desc
 **/
public class AbstractAction {

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private long uid;

    /**
     * 组件运行模式
     * normal: 正常模式，默认模式
     * debug: 调试模式
     * performance: 性能模式 待实现
     */
    @Getter
    @Setter
    private String mode;
}
