package omni.audit.apex.core.config;

import omni.audit.apex.api.config.EnvCommonOptions;
import omni.audit.apex.api.config.ReadonlyConfig;

/**
 * <AUTHOR>
 * @class EnvConfig
 * @created 2025/6/19 14:46
 * @desc
 **/
public class EnvConfig {

    private final int parallelism;

    public final String jobName;

    public EnvConfig(ReadonlyConfig config) {
        this.parallelism = config.get(EnvCommonOptions.PARALLELISM);
        this.jobName = config.get(EnvCommonOptions.JOB_NAME);
    }
}
