package omni.audit.apex.core.dag;

import omni.audit.apex.api.source.SourceReader;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.core.runtime.ApexRecord;

import java.util.ArrayList;
import java.util.Collection;

/**
 * <AUTHOR>
 * @class SourceAction
 * @created 2025/6/16 20:36
 * @desc
 **/
public class SourceAction extends AbstractAction implements SourceReader {

    private SourceReader reader;

    public SourceAction(SourceReader reader) {
        this.reader = reader;
    }

    @Override
    public void open() {
        reader.open();
    }

    @Override
    public void close() {
        reader.close();
    }

    public Collection<ApexRecord> pollNext() {
        Collection<ApexRow> collection = reader.pollNext();
        Collection<ApexRecord> ars = new ArrayList<>();
        for (ApexRow o : collection) {
            ApexRecord e = new ApexRecord();
            e.setValue(o);
            ars.add(e);
        }
        return ars;
    }

}
