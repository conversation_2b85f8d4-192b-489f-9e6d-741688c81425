package omni.audit.apex.core.starter;

import omni.audit.apex.api.config.ApexConfig;
import omni.audit.apex.shade.com.typesafe.config.Config;
import omni.audit.apex.shade.com.typesafe.config.ConfigFactory;
import org.junit.Test;

import java.io.File;
import java.util.Collections;

public class ApexTest {

    @Test
    public void test() throws Exception {
        File file = new File(this.getClass().getResource("/faker.conf").toURI());
        Config config = ConfigFactory.parseFile(file);
        ApexConfig apexConfig = new ApexConfig();
        apexConfig.setJobConfigs(Collections.singletonList(config));
        ApexServer.run(apexConfig);
    }

    @Test
    public void testFlow() throws Exception {
        File file = new File(this.getClass().getResource("/flow.conf").toURI());
        Config config = ConfigFactory.parseFile(file);
        ApexConfig apexConfig = new ApexConfig();
        apexConfig.setJobConfigs(Collections.singletonList(config));
        ApexServer.run(apexConfig);
    }

}