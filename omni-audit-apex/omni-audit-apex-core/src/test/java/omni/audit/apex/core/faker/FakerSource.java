package omni.audit.apex.core.faker;

import omni.audit.apex.api.source.ApexSource;
import omni.audit.apex.api.source.SourceReader;
import omni.audit.apex.api.source.SourceReaderContext;

/**
 * <AUTHOR>
 * @class FakerSource
 * @created 2025/6/18 15:47
 * @desc
 **/
public class FakerSource implements ApexSource {

    @Override
    public SourceReader createReader(SourceReaderContext context) {
        return new FakerSourceReader();
    }
}
