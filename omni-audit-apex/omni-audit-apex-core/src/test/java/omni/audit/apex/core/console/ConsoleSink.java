package omni.audit.apex.core.console;

import omni.audit.apex.api.sink.ApexSink;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.sink.SinkWriterContext;

/**
 * <AUTHOR>
 * @class ConsoleSink
 * @created 2025/6/18 15:56
 * @desc
 **/
public class ConsoleSink implements ApexSink {
    @Override
    public SinkWriter createWriter(SinkWriterContext context) {
        return new ConsoleSinkWriter();
    }
}
