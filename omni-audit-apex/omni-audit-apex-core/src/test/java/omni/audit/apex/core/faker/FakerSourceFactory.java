package omni.audit.apex.core.faker;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexSourceFactory;
import omni.audit.apex.api.factory.ApexSourceFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.source.ApexSource;

/**
 * <AUTHOR>
 * @class FakerSourceFactory
 * @created 2025/6/18 15:49
 * @desc
 **/
@AutoService(Factory.class)
public class FakerSourceFactory implements ApexSourceFactory {
    @Override
    public ApexSource createSource(ApexSourceFactoryContext context) {
        return new FakerSource();
    }

    @Override
    public String factoryIdentifier() {
        return "faker";
    }
}
