package omni.audit.apex.core.faker;

import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.source.SourceReader;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.api.type.DefaultApexRow;

import java.time.Instant;
import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @class FakerSourceReader
 * @created 2025/6/18 15:47
 * @desc
 **/
@Slf4j
public class FakerSourceReader implements SourceReader {
    @Override
    public void open() {

    }

    @Override
    public void close() {

    }

    @Override
    public Collection pollNext() {
        log.debug("poll next");
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (InterruptedException e) {

        }
        ApexRow apexRow = new DefaultApexRow();
        apexRow.set("time", Instant.now().toEpochMilli());
        return Collections.singleton(apexRow);
    }
}
