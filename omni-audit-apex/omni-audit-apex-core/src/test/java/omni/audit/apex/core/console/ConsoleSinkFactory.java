package omni.audit.apex.core.console;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexSinkFactory;
import omni.audit.apex.api.factory.ApexSinkFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.sink.ApexSink;

/**
 * <AUTHOR>
 * @class ConsoleSinkFactory
 * @created 2025/6/18 15:58
 * @desc
 **/
@AutoService(Factory.class)
public class ConsoleSinkFactory implements ApexSinkFactory {
    @Override
    public ApexSink createSink(ApexSinkFactoryContext context) {
        return new ConsoleSink();
    }

    @Override
    public String factoryIdentifier() {
        return "console";
    }
}
