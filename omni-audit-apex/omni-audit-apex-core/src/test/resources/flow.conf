env {
  parallelism = 1
  job.mode = "STREAMING"
  common {
    output_schema = {
      timestamp = bigint
      type = string
      metaAppName = string
      metaServerVersion = string
      netSrcIp = string
      netSrcPort = int
      netDstIp = string
      netDstPort = int
      netFlowSource = string
      mac = string
      eventId = string
      reqDbName = string
      reqDbUser = string
      reqDbPassword = string
      reqSqlCmdType = string
      reqSql = string
      rspStatus = int
      rspStartTime = bigint
      rspCloseTime = bigint
      rspRowCount = int
      rspResult = string
      srvAddress = string
      srvName = string
      srvType = string
      srvLevel = string
      srvRiskLevel = string
      accessDomains = "array<int>"
      deployDomains = "array<int>"
      srvAccessDomains = "array<int>"
      srvDeployDomains = "array<int>"
      reqDataLabels = "array<int>"
      rspDataLabels = "array<int>"
      srvReqDataLabels = "array<int>"
      srvRspDataLabels = "array<int>"
      srvLifeFlag = "array<int>"
      srvDiscoverTime = bigint
      srvActiveTime = bigint
      srvBizSystem = string
      dbVersion = string
      dbType = string
      dbLevel = string
      dbRiskLevel = string
      dbAccessDomains = "array<int>"
      dbDeployDomains = "array<int>"
      dbReqDataLabels = "array<int>"
      dbRspDataLabels = "array<int>"
      dbBizSystem = string
      dbLifeFlag = "array<int>"
      dbDiscoverTime = bigint
      dbActiveTime = bigint
      eventTypes = "array<int>"
      optionMethod = string
      rspTime = bigint
      tables = "map<int, {\"name\":\"string\",\"dbType\":\"string\",\"dbVersion\":\"string\",\"level\":\"string\",\"riskLevel\":\"string\",\"dbName\":\"string\",\"srvAddress\":\"string\",\"srvName\":\"string\",\"fieldCount\":\"int\",\"bizSystem\":\"string\",\"accessDomains\":\"array<string>\",\"deployDomains\":\"array<string>\",\"reqDataLabels\":\"array<string>\",\"rspDataLabels\":\"array<string>\",\"lifeFlag\":\"array<string>\",\"discoverTime\":\"bigint\",\"activeTime\":\"bigint\",\"featureLabels\":\"array<int>\",\"tableId\":\"string\"}>"
      weaknesses = "map<int,{\"name\":\"string\",\"type\":\"string\",\"level\":\"int\",\"state\":\"string\",\"discoverTime\":\"bigint\",\"activeTime\":\"bigint\",\"srvId\":\"string\",\"dbId\":\"string\",\"tableId\":\"string\",\"weaknessId\":\"int\",\"operationId\":\"string\"}>"
      srvId = string
      dbId = string
      sample ="map<int,{\"eventId\":\"string\",\"timestamp\":\"bigint\",\"netSrcIp\":\"string\",\"netSrcPort\":\"int\",\"netDstIp\":\"string\",\"netDstPort\":\"int\",\"netFlowSource\":\"string\",\"mac\":\"string\",\"reqDbName\":\"string\",\"reqDbUser\":\"string\",\"reqDbPassword\":\"string\",\"reqSql\":\"string\",\"rspStatus\":\"int\",\"rspStartTime\":\"bigint\",\"rspCloseTime\":\"bigint\",\"rspRowCount\":\"int\",\"rspResult\":\"string\",\"srvId\":\"string\",\"dbId\":\"string\",\"accessDomains\":\"array<int>\",\"deployDomains\":\"array<int>\",\"reqDataLabels\":\"array<int>\",\"rspDataLabels\":\"array<int>\"}>"
    }
  }
}

source {
  Kafka {

    topic = "DBEvent"
    bootstrap.servers = "*************:9094"
    kafka.config = {
      max.poll.records = 100
      auto.offset.reset = "earliest"
    }
    plugin_output = "db_event"
    format = json
    schema = {
      fields = {
        meta = {
          tm = bigint
          type = string
          app_name = string
          server_version = string
        }
        net = {
          src_ip = string
          src_port = int
          dst_ip = string
          dst_port = int
          flow_source = string
        }
        mac = {
          mac = string
        }
        unique_id = {
          event_id = string
        }
        req = {
          db_name = string
          db_user = string
          db_password = string
          sql_cmd_type = string
          sql = string
        }
        rsp = {
          status = int
          start_time = bigint
          close_time = bigint
          row_count = int
          result = string
        }

      }
    }
  }
}

transform {

  Normalizer {
    plugin_input = "db_event"
    plugin_output = "db_event_flatten"

    fields = {
      timestamp = "meta.tm"
      type = "meta.type"
      metaAppName = "meta.app_name"
      metaServerVersion = "meta.server_version"
      netSrcIp = "net.src_ip"
      netSrcPort = "net.src_port"
      netDstIp = "net.dst_ip"
      netDstPort = "net.dst_port"
      netFlowSource = "net.flow_source"
      mac = "mac.mac"
      eventId = "unique_id.event_id"
      reqDbName = "req.db_name"
      reqDbUser = "req.db_user"
      reqDbPassword = "req.db_password"
      reqSqlCmdType = "req.sql_cmd_type"
      reqSql = "req.sql"
      rspStatus = "rsp.status"
      rspStartTime = "rsp.start_time"
      rspCloseTime = "rsp.close_time"
      rspRowCount = "rsp.row_count"
      rspResult = "rsp.result"
    }

    output_schema = {
      timestamp = bigint
      type = string
      metaAppName = string
      metaServerVersion = string
      netSrcIp = string
      netSrcPort = int
      netDstIp = string
      netDstPort = int
      netFlowSource = string
      mac = string
      eventId = string
      reqDbName = string
      reqDbUser = string
      reqDbPassword = string
      reqSqlCmdType = string
      reqSql = string
      rspStatus = int
      rspStartTime = bigint
      rspCloseTime = bigint
      rspRowCount = int
      rspResult = string
      srvAddress = string
      srvName = string
      srvType = string
      srvLevel = string
      srvRiskLevel = string
      accessDomains = "array<int>"
      deployDomains = "array<int>"
      srvAccessDomains = "array<int>"
      srvDeployDomains = "array<int>"
      reqDataLabels = "array<int>"
      rspDataLabels = "array<int>"
      srvReqDataLabels = "array<int>"
      srvRspDataLabels = "array<int>"
      srvLifeFlag = "array<int>"
      srvDiscoverTime = bigint
      srvActiveTime = bigint
      srvBizSystem = string
      dbVersion = string
      dbType = string
      dbLevel = string
      dbRiskLevel = string
      dbAccessDomains = "array<int>"
      dbDeployDomains = "array<int>"
      dbReqDataLabels = "array<int>"
      dbRspDataLabels = "array<int>"
      dbBizSystem = string
      dbLifeFlag = "array<int>"
      dbDiscoverTime = bigint
      dbActiveTime = bigint
      eventTypes = "array<int>"
      optionMethod = string
      rspTime = bigint
      tables = "map<int, {\"name\":\"string\",\"dbType\":\"string\",\"dbVersion\":\"string\",\"level\":\"string\",\"riskLevel\":\"string\",\"dbName\":\"string\",\"srvAddress\":\"string\",\"srvName\":\"string\",\"fieldCount\":\"int\",\"bizSystem\":\"string\",\"accessDomains\":\"array<string>\",\"deployDomains\":\"array<string>\",\"reqDataLabels\":\"array<string>\",\"rspDataLabels\":\"array<string>\",\"lifeFlag\":\"array<string>\",\"discoverTime\":\"bigint\",\"activeTime\":\"bigint\",\"featureLabels\":\"array<int>\",\"tableId\":\"string\"}>"
      weaknesses = "map<int,{\"name\":\"string\",\"type\":\"string\",\"level\":\"int\",\"state\":\"string\",\"discoverTime\":\"bigint\",\"activeTime\":\"bigint\",\"srvId\":\"string\",\"dbId\":\"string\",\"tableId\":\"string\",\"weaknessId\":\"int\",\"operationId\":\"string\"}>"
      srvId = string
      dbId = string
      sample ="map<int,{\"eventId\":\"string\",\"timestamp\":\"bigint\",\"netSrcIp\":\"string\",\"netSrcPort\":\"int\",\"netDstIp\":\"string\",\"netDstPort\":\"int\",\"netFlowSource\":\"string\",\"mac\":\"string\",\"reqDbName\":\"string\",\"reqDbUser\":\"string\",\"reqDbPassword\":\"string\",\"reqSql\":\"string\",\"rspStatus\":\"int\",\"rspStartTime\":\"bigint\",\"rspCloseTime\":\"bigint\",\"rspRowCount\":\"int\",\"rspResult\":\"string\",\"srvId\":\"string\",\"dbId\":\"string\",\"accessDomains\":\"array<int>\",\"deployDomains\":\"array<int>\",\"reqDataLabels\":\"array<int>\",\"rspDataLabels\":\"array<int>\"}>"
    }


  }

  EventFilter {
    plugin_input = "db_event_flatten"
    plugin_output = "EventFilter"
    id = "eventId"
  }
  SamplingLimit {
    plugin_input = "EventFilter"
    plugin_output = "SamplingLimit_out"
    sampling_keys = ["reqDbName"]
  }
  SqlParse {
    plugin_input = "SamplingLimit_out"
    plugin_output = "SqlParse"
    sql = "reqSql"
    dbType = "type"
    tables = "tables"
    tableScheme = {
      name = "string"
      dbType = "string"
      dbVersion = "string"
      level = "string"
      riskLevel = "string"
      dbName = "string"
      srvAddress = "string"
      srvName = "string"
      fieldCount = "int"
      bizSystem = "string"
      accessDomains = "array<string>"
      deployDomains = "array<string>"
      reqDataLabels = "array<string>"
      rspDataLabels = "array<string>"
      lifeFlag = "array<string>"
      discoverTime = "bigint"
      activeTime = "bigint"
      featureLabels = "array<int>"
      tableId = "String"
    }
    tableInfo = {
      name = "name"
    }
  }
  Asset {
    plugin_input = "SqlParse"
    plugin_output = "asset_event"
    asset = {
      assignment = [
        {
          sourceKey = "${netSrcIp}:${netSrcPort}"
          targetKey = "srvAddress"
        }
        {
          sourceKey = "${type}"
          targetKey = "srvType"
        }
        {
          sourceKey = "${type}"
          targetKey = "dbType"
        }
        {
          sourceKey = "${metaServerVersion}"
          targetKey = "dbVersion"
        }
      ]
      map = [
        {
          eventKey = "tables"
          eventSchema = {
            name = "string"
            dbType = "string"
            dbVersion = "string"
            level = "string"
            riskLevel = "string"
            dbName = "string"
            srvAddress = "string"
            srvName = "string"
            fieldCount = "int"
            bizSystem = "string"
            accessDomains = "array<string>"
            deployDomains = "array<string>"
            reqDataLabels = "array<string>"
            rspDataLabels = "array<string>"
            lifeFlag = "array<string>"
            discoverTime = "bigint"
            activeTime = "bigint"
            featureLabels = "array<int>"
            tableId = "string"
          }
        }
      ]
      time = {
        timestamp = "timestamp"
        dbActiveTime = "dbActiveTime"
        dbDiscoverTime = "dbDiscoverTime"
        srvActiveTime = "srvActiveTime"
        srvDiscoverTime = "srvDiscoverTime"
        discoverTime = "tables.?.discoverTime"
        activeTime = "tables.?.activeTime"
      }
      level = [
        {
          assetLevelPath = "srvLevel"
          levelType = "srcLevel"
        }
        {
          assetLevelPath = "dbLevel"
          levelType = "dbLevel"
        }
        {
          assetLevelPath = "tables.?.level"
          levelType = "tableLevel"
        }
      ]
      id = {
        db_srv = {
          assetIdPath = "srvAddress"
          assetIdName = "srv_id"
          assetIdEvent = "srvId"
          sqlKeyAndEventKey = {
          }
        }
        db_assets = {
          assetIdPath = "srvAddress,reqDbName"
          assetIdName = "db_id"
          assetIdEvent = "dbId"
          sqlKeyAndEventKey = {
          }
        }
        db_table = {
          assetIdPath = "srvAddress,reqDbName,tables.?[name]"
          assetIdName = "table_id"
          assetIdEvent = "tableId"
          sqlKeyAndEventKey = {
          }
        }
      }
      handle = [
        AssignmentHandler
        ActiveTimeHandler
        DiscoverTimeHandler
        LevelHandler
        MajorKeyHandler
      ]
    }
  }
  Label {
    plugin_input = "asset_event"
    plugin_output = "label_event"
    label = [
      {
      labelType = "tableLabel"
      labelPath = "tables.?.featureLabels"
      }
      {
        labelType = "dbLabel"
        labelPath = "eventTypes"
      }
    ]
    table {
      map = [
        {
          eventKey = "tables"
          eventSchema = {
            name = "string"
            dbType = "string"
            dbVersion = "string"
            level = "string"
            riskLevel = "string"
            dbName = "string"
            srvAddress = "string"
            srvName = "string"
            fieldCount = "int"
            bizSystem = "string"
            accessDomains = "array<int>"
            deployDomains = "array<int>"
            reqDataLabels = "array<string>"
            rspDataLabels = "array<string>"
            lifeFlag = "array<string>"
            discoverTime = "bigint"
            activeTime = "bigint"
            featureLabels = "array<int>"
            tableId = "string"
          }
        }
      ]
    }
  }
  NetworkSegment {
    plugin_input = "label_event"
    plugin_output = "net_event"
    net = {
      netSrcIp = "netSrcIp"
      netDstIp = "netDstIp"
      visitDomains = "accessDomains"
      deployDomains = "deployDomains"

    }
  }

  Weakness {
    plugin_input = "label_event"
    plugin_output = "weakness_output"

    weakness_config = {
      WEAKNESS_RULE_TABLE = "weakness_rule"
      PRIMARY_KEYS=""
    }
    output_field_map = {
      "tableId" = "tableId",
      "srvId" = "srvId",
      "dbId" = "dbId"
    }

    field_map = {
      "weaknessId" = "weakness_id",
      "accessDomains" = "access_domains",
      "deployDomains" = "deploy_domains",
      "operationId" = "operation_id"
    }
    weakness_scheme = {
      name = "string"
      type = "string"
      level = "int"
      state = "string"
      discoverTime = "bigint"
      activeTime = "bigint"
      srvId = "string"
      dbId = "string"
      tableId = "string"
      weaknessId="int"
      operationId="string"
    }
  }

  SamplingStorage {
    plugin_input = "weakness_output"
    plugin_output = "samplingStorage_out"
    sample_config = {
      sample_unique_key="dbId"
    }
    sample_scheme = {
      eventId = string
      timestamp = bigint
      netSrcIp = string
      netSrcPort = int
      netDstIp = string
      netDstPort = int
      netFlowSource = string
      mac = string
      reqDbName = string
      reqDbUser = string
      reqDbPassword = string
      reqSql = string
      rspStatus = int
      rspStartTime = bigint
      rspCloseTime = bigint
      rspRowCount = int
      rspResult = string
      srvId = string
      dbId = string
      accessDomains = "array<int>"
      deployDomains = "array<int>"
      reqDataLabels = "array<int>"
      rspDataLabels = "array<int>"
    }
  }



  MapExpand {
    plugin_input = "weakness_output"
    plugin_output = "weakness_sink"
    inputFieldName = "weaknesses"
    output_scheme = {
      name = "string"
      type = "string"
      level = "int"
      state = "string"
      discoverTime = "bigint"
      activeTime = "bigint"
      srvId = "string"
      dbId = "string"
      tableId = "string"
      weaknessId="int"
      operationId="string"
    }
  }

  MapExpand {
    plugin_input = "samplingStorage_out"
    plugin_output = "sample_sink"
    inputFieldName = "sample"
    output_scheme = {
      eventId = string
      timestamp = bigint
      netSrcIp = string
      netSrcPort = int
      netDstIp = string
      netDstPort = int
      netFlowSource = string
      mac = string
      reqDbName = string
      reqDbUser = string
      reqDbPassword = string
      reqSql = string
      rspStatus = int
      rspStartTime = bigint
      rspCloseTime = bigint
      rspRowCount = int
      rspResult = string
      srvId = string
      dbId = string
      accessDomains = "array<int>"
      deployDomains = "array<int>"
      reqDataLabels = "array<int>"
      rspDataLabels = "array<int>"
    }
  }

  Insert {
    plugin_input = "net_event"
    plugin_output = "table_sink"
    inputFieldName = "tables"
    isFirst = "false"
    tableField = {
      name = "string"
      dbType = "string"
      dbVersion = "string"
      level = "string"
      riskLevel = "string"
      dbName = "string"
      srvAddress = "string"
      srvName = "string"
      fieldCount = "int"
      bizSystem = "string"
      accessDomains = "array<int>"
      deployDomains = "array<int>"
      reqDataLabels = "array<string>"
      rspDataLabels = "array<string>"
      lifeFlag = "array<string>"
      discoverTime = "bigint"
      activeTime = "bigint"
      featureLabels = "array<int>"
      tableId = "string"
    }
    field = [
      {
        sqlName = "name"
        eventName = "name"
        sqlType = "string"
      }
      {
        sqlName = "db_type"
        eventName = "dbType"
        sqlType = "string"
      }
      {
        sqlName = "db_version"
        eventName = "dbVersion"
        sqlType = "string"
      }
      {
        sqlName = "sensi_level"
        eventName = "level"
        sqlType = "string"
      }
      {
        sqlName = "risk_level"
        eventName = "riskLevel"
        sqlType = "string"
      }
      {
        sqlName = "rel_srv_addr"
        eventName = "srvAddress"
        sqlType = "string"
      }
      {
        sqlName = "rel_srv_name"
        eventName = "srvName"
        sqlType = "string"
      }
      {
        sqlName = "column_cnt"
        eventName = "fieldCount"
        sqlType = "string"
      }
      {
        sqlName = "biz_system"
        eventName = "bizSystem"
        sqlType = "string"
      }
      {
        sqlName = "access_domains"
        eventName = "accessDomains"
        sqlType = "array<int>"
      }
      {
        sqlName = "deploy_domains"
        eventName = "deployDomains"
        sqlType = "array<int>"
      }
      {
        sqlName = "req_labels"
        eventName = "reqDataLabels"
        sqlType = "array<int>"
      }
      {
        sqlName = "rsp_labels"
        eventName = "rspDataLabels"
        sqlType = "array<int>"
      }
      {
        sqlName = "status"
        eventName = "lifeFlag"
        sqlType = "string"
      }
      {
        sqlName = "first_at"
        eventName = "discoverTime"
        sqlType = "bigint"
        eventType = "long"
      }
      {
        sqlName = "last_at"
        eventName = "activeTime"
        sqlType = "bigint"
        eventType = "long"
      }
      {
        sqlName = "table_id"
        eventName = "tableId"
        sqlType = "string"
      }
    ]
  }
  Insert {
    plugin_input = "net_event"
    plugin_output = "db_sink"
    isFirst = "true"
    field = [
      {
        sqlName = "db_id"
        eventName = "dbId"
        sqlType = "string"
      }
      {
        sqlName = "db_name"
        eventName = "reqDbName"
        sqlType = "string"
      }
      {
        sqlName = "db_version"
        eventName = "dbVersion"
        sqlType = "string"
      }
      {
        sqlName = "db_type"
        eventName = "dbType"
        sqlType = "string"
      }
      {
        sqlName = "sensi_level"
        eventName = "dbLevel"
        sqlType = "string"
      }
      {
        sqlName = "risk_level"
        eventName = "dbRiskLevel"
        sqlType = "string"
      }
      {
        sqlName = "rel_db_srv"
        eventName = "srvAddress"
        sqlType = "string"
      }
      {
        sqlName = "access_domains"
        eventName = "accessDomains"
        sqlType = "array<int>"
      }
      {
        sqlName = "deploy_domains"
        eventName = "deployDomains"
        sqlType = "array<int>"
      }
      {
        sqlName = "req_labels"
        eventName = "dbReqDataLabels"
        sqlType = "array<int>"
      }
      {
        sqlName = "rsp_labels"
        eventName = "dbRspDataLabels"
        sqlType = "array<int>"
      }
      {
        sqlName = "biz_system"
        eventName = "dbBizSystem"
        sqlType = "string"
      }
      {
        sqlName = "first_at"
        eventName = "dbDiscoverTime"
        sqlType = "bigint"
      }
      {
        sqlName = "last_at"
        eventName = "dbActiveTime"
        sqlType = "bigint"
      }
    ]

  }
  Insert {
    plugin_input = "net_event"
    plugin_output = "srv_sink"
    isFirst = "true"
    field = [
      {
        sqlName = "srv_id"
        eventName = "srvId"
        sqlType = "string"
      }
      {
        sqlName = "srv_addr"
        eventName = "srvAddress"
        sqlType = "string"
      }
      {
        sqlName = "srv_name"
        eventName = "srvName"
        sqlType = "string"
      }
      {
        sqlName = "srv_type"
        eventName = "srvType"
        sqlType = "string"
      }
      {
        sqlName = "sensi_level"
        eventName = "srvLevel"
        sqlType = "string"
      }
      {
        sqlName = "risk_level"
        eventName = "srvRiskLevel"
        sqlType = "string"
      }
      {
        sqlName = "access_domains"
        eventName = "accessDomains"
        sqlType = "array<int>"
      }
      {
        sqlName = "deploy_domains"
        eventName = "deployDomains"
        sqlType = "array<int>"
      }
      {
        sqlName = "req_labels"
        eventName = "srvReqDataLabels"
        sqlType = "array<int>"
      }
      {
        sqlName = "rsp_labels"
        eventName = "srvRspDataLabels"
        sqlType = "array<int>"
      }
      {
        sqlName = "flow_sources"
        eventName = "netFlowSource"
        sqlType = "string"
      }
      {
        sqlName = "biz_system"
        eventName = "srvBizSystem"
        sqlType = "string"
      }
      {
        sqlName = "first_at"
        eventName = "srvDiscoverTime"
        sqlType = "bigint"
      }
      {
        sqlName = "last_at"
        eventName = "srvActiveTime"
        sqlType = "bigint"
      }
    ]
  }

}

sink {
  console {
    plugin_input = "weakness_output"
  }
  kafka {
    plugin_input = "net_event"
    topic = "db_normal_event"
    bootstrap.servers = "*************:9094"
    format = json
  }
  jdbc {
    plugin_input = "table_sink"
    url = "jdbc:postgresql://*************:5432/postgres"
    driver = "org.postgresql.Driver"
    user = postgres
    password = password123
    query ="""
   INSERT INTO db_table (
        name, db_type, db_version, sensi_level, risk_level,
        rel_srv_addr, rel_srv_name, column_cnt, biz_system, access_domains,
        deploy_domains, req_labels,rsp_labels,status,
        first_at,last_at,table_id
      ) VALUES (
        ?, ?, ?, ?, ?,
        ?, ?, ?, ?, ?,
        ?, ?, ?, ?,
        ?, ?, ?
      )
    """
  }

  jdbc {
    plugin_input = "db_sink"
    url = "jdbc:postgresql://*************:5432/postgres"
    driver = "org.postgresql.Driver"
    user = postgres
    password = password123
    query ="""
   INSERT INTO db_assets (
        db_id, db_name, db_version, db_type, sensi_level,
        risk_level,rel_db_srv, access_domains, deploy_domains, req_labels,
        rsp_labels, biz_system, first_at,last_at
      ) VALUES (
        ?, ?, ?, ?, ?,
        ?, ?, ?, ?, ?,
        ?, ?, ?, ?
      )
    """
  }

  jdbc {
    plugin_input = "srv_sink"
    url = "jdbc:postgresql://*************:5432/postgres"
    driver = "org.postgresql.Driver"
    user = postgres
    password = password123
    query ="""
   INSERT INTO db_srv (
        srv_id, srv_addr, srv_name, srv_type, sensi_level,
        risk_level, access_domains, deploy_domains, req_labels,rsp_labels,
        flow_sources,biz_system, first_at,last_at
      ) VALUES (
        ?, ?, ?, ?, ?,
        ?, ?, ?, ?, ?,
        ?, ?, ?, ?
      )
    """
  }



  jdbc {
    plugin_input = "weakness_sink"
    url = "jdbc:postgresql://*************:5432/postgres"
    driver = "org.postgresql.Driver"
    user = postgres
    password = password123
    field_mapping = {
      "name" = "name",
      "type" = "type",
      "level" = "level",
      "state" = "state",
      "discoverTime" = "first_at",
      "activeTime" = "last_at",
      "srvId" = "srv_id",
      "dbId" = "db_id",
      "tableId" = "table_id",
      "weaknessId" = "weakness_id",
      "operationId" = "operation_id"
    }
    query ="""
   INSERT INTO weakness (
        name, type, level, state,
        first_at,last_at,srv_id,db_id,
        table_id,weakness_id,operation_id
      ) VALUES (
        ?, ?, ?, ?,
        ?, ?, ?, ?,
        ?, ?, ?
      )
        ON CONFLICT (operation_id) DO UPDATE SET
        name = EXCLUDED.name,
        type = EXCLUDED.type,
        level = EXCLUDED.level,
        state = EXCLUDED.state,
        first_at = EXCLUDED.first_at,
        last_at = EXCLUDED.last_at
    """
  }

  jdbc {
    plugin_input = "sample_sink"
    url = "jdbc:postgresql://*************:5432/postgres"
    driver = "org.postgresql.Driver"
    user = postgres
    password = password123
    field_mapping = {
      "eventId" = "event_id",
      "timestamp" = "timestamp",
      "netSrcIp" = "net_src_ip",
      "netSrcPort" = "net_src_port",
      "netDstIp" = "net_dst_ip",
      "netDstPort" = "net_dst_port",
      "netFlowSource" = "net_flow_source",
      "mac" = "mac",
      "reqDbName" = "req_db_name",
      "reqDbUser" = "req_db_user",
      "reqDbPassword" = "req_db_password",
      "reqSql" = "req_sql",
      "rspStatus" = "rsp_status",
      "rspStartTime" = "rsp_start_time",
      "rspCloseTime" = "rsp_close_time",
      "rspRowCount" = "rsp_row_count",
      "rspResult" = "rsp_result",
      "srvId" = "srv_id",
      "dbId" = "db_id",
      "accessDomains" = "access_domains",
      "deployDomains" = "deploy_domains",
      "reqDataLabels" = "req_data_labels",
      "rspDataLabels" = "rsp_data_labels"
    }
    query ="""
     INSERT INTO sample (
    event_id, timestamp, net_src_ip, net_src_port, net_dst_ip,
    net_dst_port, net_flow_source, mac, req_db_name, req_db_user,
    req_db_password, req_sql, rsp_status, rsp_start_time, rsp_close_time,
    rsp_row_count, rsp_result, srv_id, db_id, access_domains,
    deploy_domains, req_data_labels, rsp_data_labels
) VALUES (
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?
)
    """
  }



}
