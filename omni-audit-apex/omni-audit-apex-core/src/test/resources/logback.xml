<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <contextName>handler</contextName>

    <property name="log_dir" value="/tmp/logs"/>

    <property name="console_log_pattern" value="%date{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} %msg%n"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${console_log_pattern}</pattern>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="console"/>
    </root>
</configuration>
