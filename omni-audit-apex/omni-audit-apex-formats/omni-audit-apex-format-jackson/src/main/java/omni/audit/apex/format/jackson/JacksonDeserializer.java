package omni.audit.apex.format.jackson;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.serializer.Deserializer;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.api.util.ApexRowUtil;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @class JacksonDeserializer
 * @created 2025/7/23 20:04
 * @desc
 **/
@Slf4j
public class JacksonDeserializer implements Deserializer {

    private final ObjectMapper objectMapper;

    public JacksonDeserializer() {
        objectMapper = new ObjectMapper();
        // objectMapper.enable(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS);
        objectMapper.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
    }

    @Override
    public ApexRow deserialize(byte[] bytes) {
        try {
            Map<String, Object> map;
            String json;
            try {
                json = new String(bytes, StandardCharsets.UTF_8);
                map = objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {

                });
            } catch (JsonProcessingException e) {
                json = new String(bytes, StandardCharsets.ISO_8859_1);
                map = objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {

                });
            }

            return ApexRowUtil.create(map);
        } catch (Exception e) {
            log.error("deserialize error", e);
        }
        return null;
    }

}
