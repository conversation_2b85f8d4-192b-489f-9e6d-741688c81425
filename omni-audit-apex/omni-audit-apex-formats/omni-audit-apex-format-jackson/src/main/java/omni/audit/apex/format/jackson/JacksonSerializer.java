package omni.audit.apex.format.jackson;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.serializer.Serializer;
import omni.audit.apex.api.type.ApexRow;

/**
 * <AUTHOR>
 * @class JacksonSerialization
 * @created 2025/7/23 20:04
 * @desc
 **/
@Slf4j
public class JacksonSerializer implements Serializer {

    private final ObjectMapper objectMapper;

    public JacksonSerializer() {
        objectMapper = new ObjectMapper();
        // objectMapper.enable(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS);
        objectMapper.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
    }

    @Override
    public byte[] serialize(ApexRow row) {
        if (row == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsBytes(row.toMap());
        } catch (JsonProcessingException e) {
            log.error("serialize error", e);
        }
        return null;
    }

}
