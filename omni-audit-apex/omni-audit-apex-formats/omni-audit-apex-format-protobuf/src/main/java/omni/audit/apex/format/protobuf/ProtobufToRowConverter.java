package omni.audit.apex.format.protobuf;

import com.google.protobuf.ByteString;
import com.google.protobuf.Descriptors;
import com.google.protobuf.DynamicMessage;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.api.util.ApexRowUtil;

import java.io.IOException;
import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static omni.audit.apex.api.type.SqlType.*;

public class ProtobufToRowConverter implements Serializable {

    private static final long serialVersionUID = 1L;

    private Descriptors.Descriptor descriptor = null;

    private String protoContent;

    private String messageName;

    public ProtobufToRowConverter(String protoContent, String messageName) {
        this.protoContent = protoContent;
        this.messageName = messageName;
    }

    public Descriptors.Descriptor getDescriptor() {
        if (descriptor == null) {
            try {
                descriptor = createDescriptor();
            } catch (IOException
                     | Descriptors.DescriptorValidationException
                     | InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return descriptor;
    }

    private Descriptors.Descriptor createDescriptor()
            throws IOException, InterruptedException, Descriptors.DescriptorValidationException {

        return CompileDescriptor.compileDescriptorTempFile(protoContent, messageName);
    }

    public ApexRow converter(
            Descriptors.Descriptor descriptor,
            DynamicMessage dynamicMessage
    ) {
        Map<String, Object> map = new LinkedHashMap<>();

        // 按照 Protobuf 描述符中定义的字段顺序遍历
        for (Descriptors.FieldDescriptor fd : descriptor.getFields()) {
            String fieldName = fd.getName();
            Object fieldValue = dynamicMessage.getField(fd);

            // 直接处理字段值的基本转换
            Object convertedValue = convertBasicField(fieldValue, fd);

            map.put(fieldName, convertedValue);
        }

        return ApexRowUtil.create(map);
    }

    private Object convertBasicField(Object val, Descriptors.FieldDescriptor fieldDescriptor) {
        if (val == null) {
            return null;
        }

        switch (fieldDescriptor.getType()) {
            case STRING:
                return val.toString();
            case BOOL:
                return val;
            case INT32:
            case INT64:
            case FLOAT:
            case DOUBLE:
                return val;
            case BYTES:
                if (val instanceof ByteString) {
                    return ((ByteString) val).toByteArray();
                }
                return val;
            case MESSAGE:
                // 对于嵌套消息类型，递归处理
                if (val instanceof DynamicMessage) {
                    DynamicMessage nestedMessage = (DynamicMessage) val;
                    return converterToMap(fieldDescriptor.getMessageType(), nestedMessage);
                } else if (val instanceof List) {
                    // 处理重复的消息类型字段
                    List<?> list = (List<?>) val;
                    return list.stream()
                               .filter(item -> item instanceof DynamicMessage)
                               .map(item -> converterToMap(fieldDescriptor.getMessageType(), (DynamicMessage) item))
                               .collect(Collectors.toList());
                }
                return val;
            case ENUM:
                return val.toString();
            default:
                return val;
        }
    }

    private Map<String, Object> converterToMap(Descriptors.Descriptor descriptor, DynamicMessage dynamicMessage) {
        Map<String, Object> map = new LinkedHashMap<>();

        for (Descriptors.FieldDescriptor fd : descriptor.getFields()) {
            String fieldName = fd.getName();
            Object fieldValue = dynamicMessage.getField(fd);
            Object convertedValue = convertBasicField(fieldValue, fd);
            map.put(fieldName, convertedValue);
        }

        return map;
    }


    // private Object convertField(
    //         Descriptors.Descriptor descriptor,
    //         DynamicMessage dynamicMessage,
    //         SeaTunnelDataType<?> dataType,
    //         Object val,
    //         String fieldName
    // ) {
    //     switch (dataType.getSqlType()) {
    //         case STRING:
    //             return val.toString();
    //         case BOOLEAN:
    //         case INT:
    //         case BIGINT:
    //         case FLOAT:
    //         case DOUBLE:
    //         case NULL:
    //         case DATE:
    //         case DECIMAL:
    //         case TIMESTAMP:
    //             return val;
    //         case BYTES:
    //             return ((ByteString) val).toByteArray();
    //         case SMALLINT:
    //             return ((Integer) val).shortValue();
    //         case TINYINT:
    //             Class<?> typeClass = dataType.getTypeClass();
    //             if (typeClass == Byte.class) {
    //                 Integer integer = (Integer) val;
    //                 return integer.byteValue();
    //             }
    //             return val;
    //         case MAP:
    //             MapType<?, ?> mapType = (MapType<?, ?>) dataType;
    //             Map<Object, Object> res =
    //                     ((List<DynamicMessage>) val)
    //                             .stream()
    //                             .collect(
    //                                     Collectors.toMap(
    //                                             dm ->
    //                                                     convertField(
    //                                                             descriptor,
    //                                                             dm,
    //                                                             mapType.getKeyType(),
    //                                                             getFieldValue(dm, "key"),
    //                                                             null),
    //                                             dm ->
    //                                                     convertField(
    //                                                             descriptor,
    //                                                             dm,
    //                                                             mapType.getValueType(),
    //                                                             getFieldValue(dm, "value"),
    //                                                             null)));
    //
    //             return res;
    //         case ROW:
    //             Descriptors.Descriptor nestedTypeByName =
    //                     descriptor.findNestedTypeByName(fieldName);
    //             DynamicMessage s =
    //                     (DynamicMessage)
    //                             dynamicMessage.getField(
    //                                     descriptor.findFieldByName(fieldName.toLowerCase()));
    //             return converter(nestedTypeByName, s);
    //         case ARRAY:
    //             SeaTunnelDataType<?> basicType = ((ArrayType<?, ?>) dataType).getElementType();
    //             List<Object> list = (List<Object>) val;
    //             return convertArray(list, basicType);
    //         default:
    //             String errorMsg =
    //                     String.format(
    //                             "SeaTunnel avro format is not supported for this data type [%s]",
    //                             dataType.getSqlType());
    //             throw new RuntimeException(errorMsg);
    //     }
    // }

    // private Object getFieldValue(DynamicMessage dm, String fieldName) {
    //     return dm.getAllFields().entrySet().stream()
    //              .filter(entry -> entry.getKey().getName().equals(fieldName))
    //              .map(Map.Entry::getValue)
    //              .findFirst()
    //              .orElse(null);
    // }
    //
    // protected Object convertArray(List<Object> val, SeaTunnelDataType<?> dataType) {
    //     if (val == null) {
    //         return null;
    //     }
    //     int length = val.size();
    //     Object instance = Array.newInstance(dataType.getTypeClass(), length);
    //     for (int i = 0; i < val.size(); i++) {
    //         Array.set(instance, i, convertField(null, null, dataType, val.get(i), null));
    //     }
    //     return instance;
    // }

}
