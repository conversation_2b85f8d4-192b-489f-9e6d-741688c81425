package omni.audit.apex.format.protobuf;

import com.github.os72.protocjar.Protoc;
import com.google.protobuf.DescriptorProtos;
import com.google.protobuf.Descriptors;

import java.io.*;
import java.util.List;

public class CompileDescriptor {

    public static Descriptors.Descriptor compileDescriptorTempFile(
            String protoContent, String messageName)
            throws IOException, InterruptedException, Descriptors.DescriptorValidationException {
        // Because Protobuf can only be dynamically parsed through the descriptor file, the file
        // needs to be compiled and generated. The following method is used here to solve the
        // problem: generate a temporary directory and compile .proto into a descriptor temporary
        // file. The temporary file and directory are deleted after the JVM runs.
        File tmpDir = createTempDirectory();
        File protoFile = createProtoFile(tmpDir, protoContent);
        String targetDescPath = compileProtoToDescriptor(tmpDir, protoFile);

        try (FileInputStream fis = new FileInputStream(targetDescPath)) {
            DescriptorProtos.FileDescriptorSet descriptorSet =
                    DescriptorProtos.FileDescriptorSet.parseFrom(fis);
            Descriptors.FileDescriptor[] descriptorsArray = buildFileDescriptors(descriptorSet);
            return descriptorsArray[0].findMessageTypeByName(messageName);
        } finally {
            tmpDir.delete();
            protoFile.delete();
            new File(targetDescPath).delete();
        }
    }

    private static File createTempDirectory() throws IOException {
        File tmpDir = File.createTempFile("tmp_protobuf_", "_proto");
        tmpDir.delete();
        tmpDir.mkdirs();
        tmpDir.deleteOnExit();
        return tmpDir;
    }

    private static File createProtoFile(File tmpDir, String protoContent) throws IOException {
        File protoFile = new File(tmpDir, ".proto");
        protoFile.deleteOnExit();
        writeStringToFile(protoFile.getPath(), protoContent);
        return protoFile;
    }

    public static void writeStringToFile(String filePath, String str) {
        PrintStream ps = null;
        try {
            File file = new File(filePath);
            ps = new PrintStream(new FileOutputStream(file));
            ps.println(str);
        } catch (FileNotFoundException e) {
            throw new RuntimeException("file not found: " + filePath, e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    private static String compileProtoToDescriptor(File tmpDir, File protoFile)
            throws IOException, InterruptedException {
        String targetDesc = tmpDir + "/.desc";
        new File(targetDesc).deleteOnExit();

        int exitCode =
                Protoc.runProtoc(
                        new String[]{
                                "--proto_path=" + protoFile.getParent(),
                                "--descriptor_set_out=" + targetDesc,
                                protoFile.getPath()
                        });

        if (exitCode != 0) {
            throw new RuntimeException(
                    "Protoc compile error, exit code: Protobuf descriptor conversion failed.");
        }
        return targetDesc;
    }

    private static Descriptors.FileDescriptor[] buildFileDescriptors(
            DescriptorProtos.FileDescriptorSet descriptorSet
    )
            throws Descriptors.DescriptorValidationException {
        List<DescriptorProtos.FileDescriptorProto> fileDescriptors = descriptorSet.getFileList();
        Descriptors.FileDescriptor[] descriptorsArray =
                new Descriptors.FileDescriptor[fileDescriptors.size()];
        for (int i = 0; i < fileDescriptors.size(); i++) {
            descriptorsArray[i] =
                    Descriptors.FileDescriptor.buildFrom(
                            fileDescriptors.get(i), new Descriptors.FileDescriptor[]{});
        }
        return descriptorsArray;
    }

}
