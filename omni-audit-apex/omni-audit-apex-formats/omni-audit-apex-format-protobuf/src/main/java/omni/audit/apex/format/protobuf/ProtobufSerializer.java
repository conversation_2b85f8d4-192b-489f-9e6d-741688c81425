package omni.audit.apex.format.protobuf;

import com.google.protobuf.Descriptors;
import omni.audit.apex.api.serializer.Serializer;
import omni.audit.apex.api.type.ApexRow;

import java.io.IOException;

public class ProtobufSerializer implements Serializer {

    private static final long serialVersionUID = 1L;

    private final RowToProtobufConverter converter;

    public ProtobufSerializer(String protobufMessageName, String protobufSchema) {
        try {
            Descriptors.Descriptor descriptor =
                    CompileDescriptor.compileDescriptorTempFile(
                            protobufSchema, protobufMessageName);
            this.converter = new RowToProtobufConverter(descriptor);
        } catch (IOException | InterruptedException | Descriptors.DescriptorValidationException e) {
            throw new RuntimeException(e);
        }
    }

    public byte[] serialize(ApexRow element) {
        return converter.convertRowToGenericRecord(element);
    }

}
