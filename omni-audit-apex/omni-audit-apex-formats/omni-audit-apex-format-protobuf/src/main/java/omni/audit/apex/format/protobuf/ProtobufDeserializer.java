package omni.audit.apex.format.protobuf;

import com.google.protobuf.Descriptors;
import com.google.protobuf.DynamicMessage;
import omni.audit.apex.api.serializer.Deserializer;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.api.type.Catalog;

import java.io.IOException;

public class ProtobufDeserializer implements Deserializer {

    private static final long serialVersionUID = 1L;

    private final ProtobufToRowConverter converter;

    private final Catalog catalog;

    private final String protoContent;

    private final String messageName;

    public ProtobufDeserializer(Catalog catalog) {
        this.catalog = catalog;
        this.messageName = catalog.getOptions().get("protobuf_message_name");
        this.protoContent = catalog.getOptions().get("protobuf_schema");
        this.converter = new ProtobufToRowConverter(protoContent, messageName);
    }

    public ApexRow deserialize(byte[] message) throws IOException {
        Descriptors.Descriptor descriptor = this.converter.getDescriptor();
        DynamicMessage dynamicMessage = DynamicMessage.parseFrom(descriptor, message);
        ApexRow apexRow = this.converter.converter(descriptor, dynamicMessage);
        return apexRow;
    }

}
