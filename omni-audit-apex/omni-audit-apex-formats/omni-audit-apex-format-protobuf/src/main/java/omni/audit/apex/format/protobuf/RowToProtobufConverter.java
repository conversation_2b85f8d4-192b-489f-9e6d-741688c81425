package omni.audit.apex.format.protobuf;

import com.google.protobuf.ByteString;
import com.google.protobuf.Descriptors;
import com.google.protobuf.DynamicMessage;
import omni.audit.apex.api.type.ApexRow;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class RowToProtobufConverter implements Serializable {

    private static final long serialVersionUID = 1L;

    private final Descriptors.Descriptor descriptor;

    public RowToProtobufConverter(Descriptors.Descriptor descriptor) {
        this.descriptor = descriptor;
    }

    // public byte[] convertRowToGenericRecord(ApexRow element) {
    //     DynamicMessage.Builder builder = DynamicMessage.newBuilder(descriptor);
    //     String[] fieldNames = rowType.getFieldNames();
    //
    //     for (int i = 0; i < fieldNames.length; i++) {
    //         String fieldName = rowType.getFieldName(i);
    //         Object value = element.getField(i);
    //         Object resolvedValue =
    //                 resolveObject(fieldName, value, rowType.getFieldType(i), builder);
    //         if (resolvedValue != null) {
    //             if (resolvedValue instanceof byte[]) {
    //                 resolvedValue = ByteString.copyFrom((byte[]) resolvedValue);
    //             }
    //             builder.setField(
    //                     descriptor.findFieldByName(fieldName.toLowerCase()), resolvedValue);
    //         }
    //     }
    //
    //     return builder.build().toByteArray();
    // }

    // public byte[] convertRowToGenericRecord(ApexRow element) {
    //     DynamicMessage.Builder builder = DynamicMessage.newBuilder(descriptor);
    //
    //     // 按照 Protobuf 描述符中的字段顺序遍历
    //     for (Descriptors.FieldDescriptor fieldDescriptor : descriptor.getFields()) {
    //         String fieldName = fieldDescriptor.getName();
    //
    //         // 从 ApexRow 中按字段名获取值（假设字段名匹配）
    //         Object value = element.get(fieldName);
    //
    //         Object resolvedValue = resolveObject(fieldName, value, null, builder);
    //
    //         if (resolvedValue != null) {
    //             if (resolvedValue instanceof byte[]) {
    //                 resolvedValue = ByteString.copyFrom((byte[]) resolvedValue);
    //             }
    //             builder.setField(fieldDescriptor, resolvedValue);
    //         }
    //     }
    //
    //     return builder.build().toByteArray();
    // }
    public byte[] convertRowToGenericRecord(ApexRow element) {
        DynamicMessage.Builder builder = DynamicMessage.newBuilder(descriptor);

        for (Descriptors.FieldDescriptor fieldDescriptor : descriptor.getFields()) {
            String fieldName = fieldDescriptor.getName();
            Object value = element.get(fieldName);

            if (value == null) {
                continue;
            }

            Object resolvedValue = resolveObject(fieldName, value, fieldDescriptor, builder);

            if (resolvedValue != null) {
                if (resolvedValue instanceof byte[]) {
                    resolvedValue = ByteString.copyFrom((byte[]) resolvedValue);
                }
                builder.setField(fieldDescriptor, resolvedValue);
            }
        }

        return builder.build().toByteArray();
    }

    // private Object resolveObject(
    //         String fieldName,
    //         Object data,
    //         SeaTunnelDataType<?> seaTunnelDataType,
    //         DynamicMessage.Builder builder) {
    //     if (data == null) {
    //         return null;
    //     }
    //
    //     switch (seaTunnelDataType.getSqlType()) {
    //         case STRING:
    //         case SMALLINT:
    //         case INT:
    //         case BIGINT:
    //         case FLOAT:
    //         case DOUBLE:
    //         case BOOLEAN:
    //         case DECIMAL:
    //         case DATE:
    //         case TIMESTAMP:
    //         case BYTES:
    //             return data;
    //         case TINYINT:
    //             if (data instanceof Byte) {
    //                 return Byte.toUnsignedInt((Byte) data);
    //             }
    //             return data;
    //         case MAP:
    //             return handleMapType(fieldName, data, seaTunnelDataType, builder);
    //         case ARRAY:
    //             return Arrays.asList((Object[]) data);
    //         case ROW:
    //             return handleRowType(fieldName, data, seaTunnelDataType);
    //         default:
    //             throw new RuntimeException(
    //                     String.format(
    //                             "Apex protobuf format is not supported for this data type [%s]",
    //                             seaTunnelDataType.getSqlType()));
    //     }
    // }
    private Object resolveObject(
            String fieldName,
            Object data,
            Descriptors.FieldDescriptor fieldDescriptor,
            DynamicMessage.Builder builder
    ) {
        if (data == null) {
            return null;
        }

        switch (fieldDescriptor.getType()) {
            case STRING:
                return data.toString();
            case INT32:
                // 支持 Integer 和 Long 类型转换为 int32
                if (data instanceof Integer) {
                    return data;
                } else if (data instanceof Long) {
                    return ((Long) data).intValue();
                }
                return data;
            case INT64:
                // 支持 Integer 和 Long 类型转换为 int64
                if (data instanceof Long) {
                    return data;
                } else if (data instanceof Integer) {
                    return ((Integer) data).longValue();
                }
                return data;
            case FLOAT:
                // 支持 Integer/Long 转换为 float
                if (data instanceof Float) {
                    return data;
                } else if (data instanceof Integer) {
                    return ((Integer) data).floatValue();
                } else if (data instanceof Long) {
                    return ((Long) data).floatValue();
                }
                return data;
            case DOUBLE:
                // 支持 Integer/Long/Float 转换为 double
                if (data instanceof Double) {
                    return data;
                } else if (data instanceof Float) {
                    return ((Float) data).doubleValue();
                } else if (data instanceof Integer) {
                    return ((Integer) data).doubleValue();
                } else if (data instanceof Long) {
                    return ((Long) data).doubleValue();
                }
                return data;
            case BOOL:
                return data;
            case BYTES:
                if (data instanceof byte[]) {
                    return ByteString.copyFrom((byte[]) data);
                }
                return data;
            case MESSAGE:
                if (fieldDescriptor.isRepeated()) {
                    // repeated message 类型，递归处理每个元素
                    if (data instanceof List<?>) {
                        return ((List<?>) data).stream()
                                               .map(item -> handleMessageType(fieldDescriptor.getMessageType(),
                                                                              item,
                                                                              builder))
                                               .collect(Collectors.toList());
                    }
                } else {
                    // 单个 message 类型
                    return handleMessageType(fieldDescriptor.getMessageType(), data, builder);
                }
                break;
            case ENUM:
                // 枚举类型处理，假设传入的是字符串或整数
                return fieldDescriptor.getEnumType().findValueByName(data.toString());
            default:
                throw new RuntimeException("Unsupported field type: " + fieldDescriptor.getType());
        }

        return data;
    }

    private DynamicMessage handleMessageType(
            Descriptors.Descriptor nestedDescriptor,
            Object data,
            DynamicMessage.Builder parentBuilder
    ) {
        DynamicMessage.Builder nestedBuilder = DynamicMessage.newBuilder(nestedDescriptor);

        if (data instanceof Map) {
            // 假设嵌套结构以 Map<String, Object> 表示
            Map<?, ?> map = (Map<?, ?>) data;
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                String key = entry.getKey().toString();
                Descriptors.FieldDescriptor fd = nestedDescriptor.findFieldByName(key);
                if (fd != null) {
                    Object resolved = resolveObject(key, entry.getValue(), fd, nestedBuilder);
                    if (resolved != null) {
                        nestedBuilder.setField(fd, resolved);
                    }
                }
            }
            // } else if (data instanceof SeaTunnelRow) {
            //     SeaTunnelRow row = (SeaTunnelRow) data;
            //     for (Descriptors.FieldDescriptor fd : nestedDescriptor.getFields()) {
            //         Object value = row.get(fd.getName());
            //         Object resolved = resolveObject(fd.getName(), value, fd, nestedBuilder);
            //         if (resolved != null) {
            //             nestedBuilder.setField(fd, resolved);
            //         }
            //     }
        }

        return nestedBuilder.build();
    }


    // private Object handleMapType(
    //         String fieldName,
    //         Object data,
    //         SeaTunnelDataType<?> seaTunnelDataType,
    //         DynamicMessage.Builder builder) {
    //     Descriptors.Descriptor mapEntryDescriptor =
    //             descriptor.findFieldByName(fieldName).getMessageType();
    //
    //     if (data instanceof Map) {
    //         Map<?, ?> mapData = (Map<?, ?>) data;
    //         mapData.forEach(
    //                 (key, value) -> {
    //                     DynamicMessage mapEntry =
    //                             DynamicMessage.newBuilder(mapEntryDescriptor)
    //                                     .setField(mapEntryDescriptor.findFieldByName("key"), key)
    //                                     .setField(
    //                                             mapEntryDescriptor.findFieldByName("value"), value)
    //                                     .build();
    //                     builder.addRepeatedField(descriptor.findFieldByName(fieldName), mapEntry);
    //                 });
    //     }
    //
    //     return null;
    // }

    // private Object handleRowType(
    //         String fieldName, Object data, SeaTunnelDataType<?> seaTunnelDataType) {
    //     SeaTunnelRow seaTunnelRow = (SeaTunnelRow) data;
    //     SeaTunnelDataType<?>[] fieldTypes = ((SeaTunnelRowType) seaTunnelDataType).getFieldTypes();
    //     String[] fieldNames = ((SeaTunnelRowType) seaTunnelDataType).getFieldNames();
    //     Descriptors.Descriptor nestedTypeDescriptor = descriptor.findNestedTypeByName(fieldName);
    //     DynamicMessage.Builder nestedBuilder = DynamicMessage.newBuilder(nestedTypeDescriptor);
    //
    //     for (int i = 0; i < fieldNames.length; i++) {
    //         Object resolvedValue =
    //                 resolveObject(
    //                         fieldNames[i], seaTunnelRow.getField(i), fieldTypes[i], nestedBuilder);
    //         nestedBuilder.setField(
    //                 nestedTypeDescriptor.findFieldByName(fieldNames[i]), resolvedValue);
    //     }
    //
    //     return nestedBuilder.build();
    // }
}