package omni.audit.apex.api.serializer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.api.util.ApexRowUtil;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @class JsonDeserialization
 * @created 2025/6/23 16:14
 * @desc
 **/
@Slf4j
public class JsonSerializer implements Serializer {

    private String apexRowClass;

    private final ObjectMapper objectMapper;

    public JsonSerializer() {
        // TODO 祁灵 2025/6/23 17:04:
        this.apexRowClass = "DefaultApexRow";
        objectMapper = new ObjectMapper();
        // objectMapper.enable(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS);
        objectMapper.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
    }

    @Override
    public byte[] serialize(ApexRow row) {
        try {
            return objectMapper.writeValueAsBytes(row.toMap());
        } catch (JsonProcessingException e) {
            log.error("serialize error", e);
        }
        return null;
    }

    @Override
    public ApexRow deserialize(byte[] bytes) {
        try {
            Map<String, Object> map;
            String json;
            try {
                json = new String(bytes, StandardCharsets.UTF_8);
                map = objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {
                });
            } catch (JsonProcessingException e) {
                json = new String(bytes, StandardCharsets.ISO_8859_1);
                map = objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {
                });
            }

            return ApexRowUtil.create(map);
        } catch (Exception e) {
            log.error("deserialize error", e);
        }
        return null;
    }
}
