package omni.audit.apex.api.factory;

import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.sink.ApexSink;
import omni.audit.apex.api.source.ApexSource;
import omni.audit.apex.api.transform.ApexTransform;

import java.util.LinkedList;
import java.util.List;
import java.util.ServiceConfigurationError;
import java.util.ServiceLoader;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class FactoryUtil
 * @created 2025/6/16 16:15
 * @desc
 **/
@Slf4j
public final class FactoryUtil {

    public static Option<String> PLUGIN_NAME =
            Options.key("plugin_name")
                   .stringType()
                   .noDefaultValue()
                   .withDescription("Name of the SPI plugin class.");

    public static ApexSource createSource(ReadonlyConfig config, ClassLoader classLoader) {
        String pluginName = (String) config.get(PLUGIN_NAME);
        ApexSourceFactory factory = discoverFactory(classLoader, ApexSourceFactory.class, pluginName);
        ApexSourceFactoryContext context = new ApexSourceFactoryContext(config, classLoader);
        ApexSource source = factory.createSource(context);
        return source;
    }

    public static ApexSink createSink(ReadonlyConfig config, ClassLoader classLoader) {
        String pluginName = (String) config.get(PLUGIN_NAME);
        ApexSinkFactory factory = discoverFactory(classLoader, ApexSinkFactory.class, pluginName);
        ApexSinkFactoryContext context = new ApexSinkFactoryContext(config, classLoader);
        ApexSink sink = factory.createSink(context);
        return sink;
    }

    public static <T extends Factory> T discoverFactory(ClassLoader classLoader, Class<T> factoryClass, String factoryIdentifier) {
        final List<T> foundFactories = discoverFactories(classLoader, factoryClass);

        if (foundFactories.isEmpty()) {
            throw new FactoryException(
                    String.format(
                            "Could not find any factories that implement '%s' in the classpath.",
                            factoryClass.getName()));
        }

        final List<T> matchingFactories =
                foundFactories.stream()
                              .filter(f -> f.factoryIdentifier().equalsIgnoreCase(factoryIdentifier))
                              .collect(Collectors.toList());

        if (matchingFactories.isEmpty()) {
            throw new FactoryException(
                    String.format(
                            "Could not find any factory for identifier '%s' that implements '%s' in the classpath.\n\n"
                            + "Available factory identifiers are:\n\n"
                            + "%s",
                            factoryIdentifier,
                            factoryClass.getName(),
                            foundFactories.stream()
                                          .map(Factory::factoryIdentifier)
                                          .distinct()
                                          .sorted()
                                          .collect(Collectors.joining("\n"))));
        }

        return matchingFactories.get(0);
    }

    private static <T extends Factory> void checkMultipleMatchingFactories(
            String factoryIdentifier, Class<T> factoryClass, List<T> matchingFactories) {
        if (matchingFactories.size() > 1) {
            throw new FactoryException(
                    String.format(
                            "Multiple factories for identifier '%s' that implement '%s' found in the classpath.\n\n"
                            + "Ambiguous factory classes are:\n\n"
                            + "%s",
                            factoryIdentifier,
                            factoryClass.getName(),
                            matchingFactories.stream()
                                             .map(f -> f.getClass().getName())
                                             .sorted()
                                             .collect(Collectors.joining("\n"))));
        }
    }

    public static <T extends Factory> List<T> discoverFactories(
            ClassLoader classLoader, Class<T> factoryClass) {
        return discoverFactories(classLoader).stream()
                                             .filter(f -> factoryClass.isAssignableFrom(f.getClass()))
                                             .map(f -> (T) f)
                                             .collect(Collectors.toList());
    }

    public static List<Factory> discoverFactories(ClassLoader classLoader) {
        try {
            final List<Factory> result = new LinkedList<>();
            ServiceLoader.load(Factory.class, classLoader).iterator().forEachRemaining(result::add);
            return result;
        } catch (ServiceConfigurationError e) {
            log.error("Could not load service provider for factories.", e);
            throw new FactoryException("Could not load service provider for factories.", e);
        }
    }

    public static ApexTransform createTransform(ReadonlyConfig config, ClassLoader classLoader) {
        String pluginName = (String) config.get(PLUGIN_NAME);
        ApexTransformFactory factory = discoverFactory(classLoader, ApexTransformFactory.class, pluginName);
        ApexTransformFactoryContext context = new ApexTransformFactoryContext(config, classLoader);
        ApexTransform apexTransform = factory.createTransform(context);
        return apexTransform;
    }
}
