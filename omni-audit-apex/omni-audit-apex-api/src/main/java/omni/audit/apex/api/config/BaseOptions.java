package omni.audit.apex.api.config;

/**
 * <AUTHOR>
 * @class BaseOptions
 * @created 2025/7/11 11:32
 * @desc source sink transform 都支持的基础配置
 **/
public class BaseOptions {

    /**
     * 插件是否启用，用于调试
     */
    public static Option<Boolean> ENABLE =
            Options.key("enable")
                   .booleanType()
                   .defaultValue(true)
                   .withDescription("The enable of this plugin instance");

    /**
     * 为不同插件分配可读的名称
     * 推荐使用英文名，避免乱码，降低可读性
     */
    public static Option<String> NAME =
            Options.key("name")
                   .stringType()
                   .defaultValue("apex_plugin_instance")
                   .withDescription("The name of this plugin instance");
}
