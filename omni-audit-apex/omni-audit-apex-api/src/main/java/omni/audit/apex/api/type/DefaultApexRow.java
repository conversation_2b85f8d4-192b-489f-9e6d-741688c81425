package omni.audit.apex.api.type;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @class ApexRowImpl
 * @created 2025/6/20 16:41
 * @desc Implementation of ApexRow
 **/
public class DefaultApexRow implements ApexRow {

    private Map<String, Object> data;

    public DefaultApexRow() {
        data = new LinkedHashMap<>();
    }

    public DefaultApexRow(Map<String, Object> data) {
        this.data = new LinkedHashMap<>(data);
    }

    @Override
    public Object get(String field) {
        Object value = data.get(field);
        if (value == null) {
            return null;
        }
        return value;
    }

    @Override
    public <T> T get(String field, Class<T> clazz) {
        Object value = data.get(field);
        if (value == null) {
            return null;
        }
        return (T) value;
    }

    @Override
    public <T> void set(String field, T value) {
        data.put(field, value);
    }

    @Override
    public Map<String, Object> toMap() {
        return data;
    }

    @Override
    public String toString() {
        if (data == null) {
            return "null";
        }
        // print data as json
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            sb.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
        }
        return sb.toString();
    }
}
