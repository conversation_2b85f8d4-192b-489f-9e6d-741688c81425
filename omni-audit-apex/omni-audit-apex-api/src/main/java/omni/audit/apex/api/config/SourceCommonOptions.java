package omni.audit.apex.api.config;

/**
 * <AUTHOR>
 * @class SourceCommonOptions
 * @created 2025/6/20 14:28
 * @desc
 **/
public class SourceCommonOptions extends BaseOptions {

    public static Option<Integer> PARALLELISM =
            Options.key("parallelism")
                   .intType()
                   .defaultValue(1)
                   .withDescription(
                           "When parallelism is not specified in connector, the parallelism in env is used by default. "
                           + "When parallelism is specified, it will override the parallelism in env.");
}
