package omni.audit.apex.connectors.console;

import omni.audit.apex.api.sink.ApexSink;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.sink.SinkWriterContext;

/**
 * <AUTHOR>
 * @class ConsoleSink
 * @created 2025/6/26 14:38
 * @desc
 **/
public class ConsoleSink  implements ApexSink {

    @Override
    public SinkWriter createWriter(SinkWriterContext context) {
        return new ConsoleSinkWriter();
    }
}
