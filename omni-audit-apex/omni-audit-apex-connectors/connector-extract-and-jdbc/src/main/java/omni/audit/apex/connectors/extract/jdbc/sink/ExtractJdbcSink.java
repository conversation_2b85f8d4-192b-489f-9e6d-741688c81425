package omni.audit.apex.connectors.extract.jdbc.sink;

import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.sink.ApexSink;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.sink.SinkWriterContext;

/**
 * <AUTHOR>
 * @class JdbcSink
 * @created 2025/7/18 14:30
 * @desc JDBC sink implementation for PostgreSQL
 **/
public class ExtractJdbcSink implements ApexSink {

    private final ReadonlyConfig config;

    public ExtractJdbcSink(ReadonlyConfig config) {
        this.config = config;
    }

    @Override
    public SinkWriter createWriter(SinkWriterContext context) {
        return new ExtractJdbcSinkWriter(config);
    }

}