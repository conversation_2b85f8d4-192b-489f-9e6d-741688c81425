package omni.audit.apex.connectors.extract.jdbc.config;

import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;
import omni.audit.apex.connectors.jdbc.config.JdbcOptions;

public interface ExtractJdbcOptions extends JdbcOptions {

    Option<String> EXTRACT_FIELD = Options.key("extractField").stringType().noDefaultValue()
                                          .withDescription("extractField");

}
