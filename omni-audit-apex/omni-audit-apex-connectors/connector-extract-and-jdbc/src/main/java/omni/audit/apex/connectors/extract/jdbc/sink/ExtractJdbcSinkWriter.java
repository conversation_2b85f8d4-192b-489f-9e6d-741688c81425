package omni.audit.apex.connectors.extract.jdbc.sink;

import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.connectors.extract.jdbc.config.ExtractJdbcOptions;
import omni.audit.apex.connectors.jdbc.config.JdbcConnectionConfig;
import omni.audit.apex.connectors.jdbc.config.JdbcOptions;
import omni.audit.apex.connectors.jdbc.sink.JdbcSinkWriter;
import omni.audit.common.batch.ConcurrentAutoFlushBatch;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.ScalarHandler;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @class JdbcSinkWriter
 * @created 2025/7/18 14:30
 * @desc JDBC sink writer implementation for PostgreSQL
 **/
@Slf4j
public class ExtractJdbcSinkWriter extends JdbcSinkWriter {

    public ExtractJdbcSinkWriter(ReadonlyConfig config) {
        super(config);
    }


    @Override
    public ApexRow write(ApexRow row) {
        String extractField = config.get(ExtractJdbcOptions.extractField);
        if (extractField == null) {
            return row;
        }
        Object extractFieldValue = row.get(extractField);
        if (extractFieldValue == null) {
            return row;
        }
        if (extractFieldValue instanceof Collection) {
            List<Object> extractFieldValueList = new ArrayList<>((Collection) extractFieldValue);
            for (Object o : extractFieldValueList) {
                writeMap(toMap(o));
            }
        } else {
            writeMap(toMap(extractFieldValue));
        }
        return row;
    }

    private Map<String, Object> toMap(Object obj) {
        if (obj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> map = (Map<String, Object>) obj;
            return map;
        }

        Map<String, Object> result = new HashMap<>();
        if (obj != null) {
            Class<?> clazz = obj.getClass();
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                try {
                    result.put(field.getName(), field.get(obj));
                } catch (IllegalAccessException e) {
                    // 忽略无法访问的字段
                    continue;
                }
            }
        }
        return result;
    }
}