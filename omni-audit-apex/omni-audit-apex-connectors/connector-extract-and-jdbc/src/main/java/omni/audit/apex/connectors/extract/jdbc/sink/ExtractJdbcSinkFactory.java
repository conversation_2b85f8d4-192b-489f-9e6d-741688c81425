package omni.audit.apex.connectors.extract.jdbc.sink;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexSinkFactory;
import omni.audit.apex.api.factory.ApexSinkFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.sink.ApexSink;
import omni.audit.apex.connectors.jdbc.sink.JdbcSink;

/**
* <AUTHOR>
* create at 2025/6/26 10:24
* @description: 
**/
@AutoService(Factory.class)
public class ExtractJdbcSinkFactory implements ApexSinkFactory {


    @Override
    public ApexSink createSink(ApexSinkFactoryContext context) {
        return new ExtractJdbcSink(context.getOptions());
    }

    @Override
    public String factoryIdentifier() {
        return "extract-jdbc";
    }
}