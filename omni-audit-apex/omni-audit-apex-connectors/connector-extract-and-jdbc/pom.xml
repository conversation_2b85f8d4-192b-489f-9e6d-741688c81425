<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.quanzhi.omni.audit</groupId>
        <artifactId>omni-audit-apex-connectors</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>omni.audit.apex.connectors.extract.jdbc</groupId>
    <artifactId>connector-extract-and-jdbc</artifactId>
    <packaging>jar</packaging>

    <name>connector-extract-and-jdbc</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omni.audit</groupId>
            <artifactId>connector-jdbc</artifactId>
            <version>1.0.0</version>
        </dependency>

    </dependencies>
</project>
