package omni.audit.apex.connectors.kafka.source;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexSourceFactory;
import omni.audit.apex.api.factory.ApexSourceFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.source.ApexSource;

/**
 * <AUTHOR>
 * @class KafkaSourceFactory
 * @created 2025/6/17 17:41
 * @desc
 **/
@AutoService(Factory.class)
public class KafkaSourceFactory implements ApexSourceFactory {

    @Override
    public ApexSource createSource(ApexSourceFactoryContext context) {
        return new KafkaSource(context.getOptions());
    }

    @Override
    public String factoryIdentifier() {
        return "Kafka";
    }
}
