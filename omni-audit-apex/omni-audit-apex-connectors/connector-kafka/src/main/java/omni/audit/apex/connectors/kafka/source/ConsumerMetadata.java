package omni.audit.apex.connectors.kafka.source;

import lombok.Data;
import omni.audit.apex.api.serializer.Deserializer;
import omni.audit.apex.api.type.Catalog;

/**
 * <AUTHOR>
 * @class ConsumerMetadata
 * @created 2025/7/23 15:27
 * @desc
 **/
@Data
public class ConsumerMetadata {

    private String topic;

    private String tableId;

    private Deserializer deserializer;

    private Catalog catalogTable;

}
