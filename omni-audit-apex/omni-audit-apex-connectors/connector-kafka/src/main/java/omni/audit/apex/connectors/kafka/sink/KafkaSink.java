package omni.audit.apex.connectors.kafka.sink;

import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.sink.ApexSink;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.sink.SinkWriterContext;

/**
 * <AUTHOR>
 * @class KafkaSink
 * @created 2025/6/24 10:42
 * @desc
 **/
public class KafkaSink implements ApexSink {

    private final ReadonlyConfig config;

    public KafkaSink(ReadonlyConfig config) {
        this.config = config;
    }

    @Override
    public SinkWriter createWriter(SinkWriterContext context) {
        return new KafkaSinkWriter(config);
    }
}
