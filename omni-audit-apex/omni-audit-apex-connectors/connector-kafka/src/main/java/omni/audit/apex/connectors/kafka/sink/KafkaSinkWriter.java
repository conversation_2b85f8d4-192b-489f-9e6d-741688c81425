package omni.audit.apex.connectors.kafka.sink;

import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.serializer.Serializer;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.connectors.kafka.config.MessageFormat;
import omni.audit.apex.format.fastjson2.Fastjson2Serializer;
import omni.audit.apex.format.jackson.JacksonSerializer;
import omni.audit.apex.format.protobuf.ProtobufSerializer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.ByteArraySerializer;

import java.util.Properties;

import static omni.audit.apex.connectors.kafka.config.KafkaBaseOptions.*;

/**
 * <AUTHOR>
 * @class KafkaSinkWriter
 * @created 2025/6/24 10:43
 * @desc
 **/
@Slf4j
public class KafkaSinkWriter implements SinkWriter<ApexRow> {

    private final KafkaProducer<byte[], byte[]> producer;

    private final Serializer serializer;

    public final String topic;

    public KafkaSinkWriter(ReadonlyConfig config) {
        Properties kafkaProperties = new Properties();
        if (config.get(KAFKA_CONFIG) != null) {
            kafkaProperties.putAll(config.get(KAFKA_CONFIG));
        }

        kafkaProperties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, config.get(BOOTSTRAP_SERVERS));
        kafkaProperties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
        kafkaProperties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
        topic = config.get(TOPIC);
        producer = new KafkaProducer<>(kafkaProperties);
        serializer = createSerializer(config);
    }

    private Serializer createSerializer(ReadonlyConfig config) {
        MessageFormat format = config.get(FORMAT);
        switch (format) {
            case JSON:
                return new JacksonSerializer();
            case FASTJSON2:
                return new Fastjson2Serializer();
            case PROTOBUF:
                return new ProtobufSerializer(config.get(PROTOBUF_MESSAGE_NAME), config.get(PROTOBUF_SCHEMA));
            default:
                throw new UnsupportedOperationException("Unsupported format: " + format);

        }
    }

    @Override
    public ApexRow write(ApexRow row) {
        try {
            producer.send(new ProducerRecord<>(topic, serializer.serialize(row)));
        } catch (Exception e) {
            log.error("KafkaSinkWriter write error, topic: {}, row: {}", topic, row, e);
        }
        return row;
    }

}
