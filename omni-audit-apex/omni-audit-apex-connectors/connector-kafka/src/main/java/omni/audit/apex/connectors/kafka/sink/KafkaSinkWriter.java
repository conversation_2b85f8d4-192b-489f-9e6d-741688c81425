package omni.audit.apex.connectors.kafka.sink;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.api.type.DefaultApexRow;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.ByteArraySerializer;

import java.util.Properties;

import static omni.audit.apex.connectors.kafka.config.KafkaBaseOptions.*;
import static omni.audit.apex.connectors.kafka.config.KafkaSinkOptions.ASSIGN_PARTITIONS;

/**
 * <AUTHOR>
 * @class KafkaSinkWriter
 * @created 2025/6/24 10:43
 * @desc
 **/
@Slf4j
public class KafkaSinkWriter implements SinkWriter<ApexRow> {

    private final KafkaProducer<byte[], byte[]> producer;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public final String topic;

    public KafkaSinkWriter(ReadonlyConfig config) {
        Properties kafkaProperties = new Properties();
        if (config.get(KAFKA_CONFIG) != null) {
            kafkaProperties.putAll(config.get(KAFKA_CONFIG));
        }

        if (config.get(ASSIGN_PARTITIONS) != null) {
            kafkaProperties.put(
                    ProducerConfig.PARTITIONER_CLASS_CONFIG,
                    "org.apache.seatunnel.connectors.seatunnel.kafka.sink.MessageContentPartitioner");
        }

        kafkaProperties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, config.get(BOOTSTRAP_SERVERS));
        kafkaProperties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
        kafkaProperties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
        topic = config.get(TOPIC);
        producer = new KafkaProducer<>(kafkaProperties);
    }

    @Override
    public ApexRow write(ApexRow row) {
        // TODO 祁灵 2025/6/24 11:07: 不应该关系实现，而是需要根据 schema 序列化
        if (row instanceof DefaultApexRow) {
            DefaultApexRow defaultApexRow = (DefaultApexRow) row;
            try {
                byte[] bytes = objectMapper.writeValueAsBytes(defaultApexRow.toMap());
                producer.send(new ProducerRecord<>(topic, bytes));
            } catch (Exception e) {
                log.error("KafkaSinkWriter write error, topic: {}, row: {}", topic, row, e);
            }
        }
        return null;
    }
}
