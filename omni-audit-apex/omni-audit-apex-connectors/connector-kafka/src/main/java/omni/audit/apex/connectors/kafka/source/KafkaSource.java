package omni.audit.apex.connectors.kafka.source;

import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.source.ApexSource;
import omni.audit.apex.api.source.SourceReader;
import omni.audit.apex.api.source.SourceReaderContext;

/**
 * <AUTHOR>
 * @class KafkaSource
 * @created 2025/6/16 17:34
 * @desc
 **/
public class KafkaSource implements ApexSource {

    private final ReadonlyConfig config;

    private final KafkaSourceConfig kafkaSourceConfig;

    public KafkaSource(ReadonlyConfig config) {
        this.config = config;
        this.kafkaSourceConfig = new KafkaSourceConfig(config);
    }

    @Override
    public SourceReader createReader(SourceReaderContext context) {
        // TODO 祁灵 2025/6/16 19:20: create kafkaSourceReader
        // TODO 祁灵 2025/6/17 19:00: kafka consumer properties
        return new KafkaSourceReader(kafkaSourceConfig);
    }

}
