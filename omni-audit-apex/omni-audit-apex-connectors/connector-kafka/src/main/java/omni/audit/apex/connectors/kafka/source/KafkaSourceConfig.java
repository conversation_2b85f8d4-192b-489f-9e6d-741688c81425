package omni.audit.apex.connectors.kafka.source;

import lombok.Getter;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.serializer.Deserializer;
import omni.audit.apex.api.type.Catalog;
import omni.audit.apex.connectors.kafka.config.MessageFormat;
import omni.audit.apex.format.fastjson2.Fastjson2Deserializer;
import omni.audit.apex.format.jackson.JacksonDeserializer;
import omni.audit.apex.format.protobuf.ProtobufDeserializer;

import java.util.HashMap;
import java.util.Optional;
import java.util.Properties;

import static omni.audit.apex.connectors.kafka.config.KafkaBaseOptions.BOOTSTRAP_SERVERS;
import static omni.audit.apex.connectors.kafka.config.KafkaSourceOptions.*;

/**
 * <AUTHOR>
 * @class KafkaSourceConfig
 * @created 2025/6/16 17:34
 * @desc
 **/
public class KafkaSourceConfig {

    @Getter
    private final String bootstrap;

    @Getter
    private final ConsumerMetadata metadata;

    @Getter
    private final boolean commitOnCheckpoint;

    @Getter
    private final Properties properties;

    @Getter
    private final long discoveryIntervalMillis;
    // @Getter private final MessageFormatErrorHandleWay messageFormatErrorHandleWay;

    @Getter
    private final String consumerGroup;

    @Getter
    private final long pollTimeout;

    @Getter
    private final String topic;

    public KafkaSourceConfig(ReadonlyConfig readonlyConfig) {
        this.bootstrap = readonlyConfig.get(BOOTSTRAP_SERVERS);
        this.metadata = createConsumerMetadata(readonlyConfig);
        this.commitOnCheckpoint = readonlyConfig.get(COMMIT_ON_CHECKPOINT);
        this.properties = createKafkaProperties(readonlyConfig);
        this.discoveryIntervalMillis = readonlyConfig.get(KEY_PARTITION_DISCOVERY_INTERVAL_MILLIS);
        // this.messageFormatErrorHandleWay = readonlyConfig.get(MESSAGE_FORMAT_ERROR_HANDLE_WAY_OPTION);
        this.pollTimeout = readonlyConfig.get(KEY_POLL_TIMEOUT);
        this.consumerGroup = readonlyConfig.get(CONSUMER_GROUP);
        this.topic = readonlyConfig.get(TOPIC);
    }

    private ConsumerMetadata createConsumerMetadata(ReadonlyConfig readonlyConfig) {
        ConsumerMetadata metadata = new ConsumerMetadata();
        metadata.setTopic(readonlyConfig.get(TOPIC));
        Catalog catalog = createCatalog(readonlyConfig);
        metadata.setCatalogTable(catalog);
        metadata.setDeserializer(createDeserializer(catalog, readonlyConfig));
        return metadata;
    }

    private Catalog createCatalog(ReadonlyConfig readonlyConfig) {
        Catalog catalogTable = new Catalog();
        catalogTable.setOptions(new HashMap<String, String>() {

            {
                Optional.ofNullable(readonlyConfig.get(PROTOBUF_MESSAGE_NAME))
                        .ifPresent(value -> put(PROTOBUF_MESSAGE_NAME.key(), value));

                Optional.ofNullable(readonlyConfig.get(PROTOBUF_SCHEMA))
                        .ifPresent(value -> put(PROTOBUF_SCHEMA.key(), value));
            }
        });
        return catalogTable;
    }

    private Deserializer createDeserializer(Catalog catalogTable, ReadonlyConfig readonlyConfig) {
        MessageFormat format = readonlyConfig.get(FORMAT);
        switch (format) {
            case JSON:
                return new JacksonDeserializer();
            case FASTJSON2:
                return new Fastjson2Deserializer();
            case PROTOBUF:
                return new ProtobufDeserializer(catalogTable);
            default:
                throw new UnsupportedOperationException("Unsupported format: " + format);
        }

    }

    private Properties createKafkaProperties(ReadonlyConfig readonlyConfig) {
        Properties resultProperties = new Properties();
        readonlyConfig.getOptional(KAFKA_CONFIG).ifPresent(resultProperties::putAll);
        return resultProperties;
    }

}
