package omni.audit.apex.connectors.kafka.sink;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexSinkFactory;
import omni.audit.apex.api.factory.ApexSinkFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.sink.ApexSink;

/**
 * <AUTHOR>
 * @class KafkaSinkFactory
 * @created 2025/6/24 10:42
 * @desc
 **/
@AutoService(Factory.class)
public class KafkaSinkFactory implements ApexSinkFactory {
    @Override
    public ApexSink createSink(ApexSinkFactoryContext context) {
        return new KafkaSink(context.getOptions());
    }

    @Override
    public String factoryIdentifier() {
        return "kafka";
    }
}
