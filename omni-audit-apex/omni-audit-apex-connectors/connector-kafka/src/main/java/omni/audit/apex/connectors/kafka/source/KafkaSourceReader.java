package omni.audit.apex.connectors.kafka.source;

import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.serializer.Serializer;
import omni.audit.apex.api.source.SourceReader;
import omni.audit.apex.api.type.ApexRow;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Properties;

/**
 * <AUTHOR>
 * @class KafkaSourceReader
 * @created 2025/6/16 17:46
 * @desc
 **/
@Slf4j
public class KafkaSourceReader implements SourceReader {

    private final KafkaConsumer<byte[], byte[]> consumer;

    private final long pollTimeout;

    private Serializer serializer;

    private final KafkaSourceConfig kafkaSourceConfig;

    private static final String CLIENT_ID_PREFIX = "apex";

    private String topic;

    public KafkaSourceReader(KafkaSourceConfig kafkaSourceConfig) {
        this.kafkaSourceConfig = kafkaSourceConfig;
        this.consumer = initConsumer(kafkaSourceConfig, 1);
        this.pollTimeout = kafkaSourceConfig.getPollTimeout();
    }

    private KafkaConsumer<byte[], byte[]> initConsumer(KafkaSourceConfig kafkaSourceConfig, int subtaskId) {
        Properties props = new Properties();
        kafkaSourceConfig
                .getProperties()
                .forEach(
                        (key, value) ->
                                props.setProperty(String.valueOf(key), String.valueOf(value)));
        props.setProperty(ConsumerConfig.GROUP_ID_CONFIG, kafkaSourceConfig.getConsumerGroup());
        props.setProperty(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaSourceConfig.getBootstrap());
        if (this.kafkaSourceConfig.getProperties().get("client.id") == null) {
            props.setProperty(
                    ConsumerConfig.CLIENT_ID_CONFIG,
                    CLIENT_ID_PREFIX + "-consumer-" + subtaskId);
        } else {
            props.setProperty(
                    ConsumerConfig.CLIENT_ID_CONFIG,
                    this.kafkaSourceConfig.getProperties().get("client.id").toString()
                    + "-"
                    + subtaskId);
        }
        props.setProperty(
                ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG,
                ByteArrayDeserializer.class.getName());
        props.setProperty(
                ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,
                ByteArrayDeserializer.class.getName());
        props.setProperty(
                ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,
                String.valueOf(kafkaSourceConfig.isCommitOnCheckpoint()));

        // Disable auto create topics feature
        props.setProperty(ConsumerConfig.ALLOW_AUTO_CREATE_TOPICS_CONFIG, "false");
        this.topic = kafkaSourceConfig.getTopic();

        return new KafkaConsumer<>(props);
    }

    @Override
    public void open() {
        if (consumer != null) {
            consumer.subscribe(Collections.singletonList(topic));
        }
    }

    @Override
    public void close() {
        if (consumer != null) {
            consumer.close();
        }
    }

    @Override
    public Collection<ApexRow> pollNext() {
        if (consumer == null) {
            return new ArrayList<>();
        }
        Collection<ApexRow> collection = new ArrayList<>();
        ConsumerRecords<byte[], byte[]> consumerRecords = consumer.poll(Duration.ofMillis(pollTimeout));
        for (ConsumerRecord<byte[], byte[]> record : consumerRecords) {
            ApexRow deserialize = null;
            try {
                deserialize = kafkaSourceConfig.getMetadata().getDeserializer().deserialize(record.value());
            } catch (IOException e) {
                log.error("deserialize error", e);
            }
            if (deserialize == null) {
                continue;
            }
            collection.add(deserialize);
        }
        this.consumer.commitAsync();
        return collection;
    }

}
