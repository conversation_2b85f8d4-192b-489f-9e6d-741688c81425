package omni.audit.apex.connectors.blackhole;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexSinkFactory;
import omni.audit.apex.api.factory.ApexSinkFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.sink.ApexSink;

/**
 * <AUTHOR>
 * @class BlackHoleSinkFactory
 * @created 2025/6/26 14:47
 * @desc
 **/
@AutoService(Factory.class)
public class BlackHoleSinkFactory implements ApexSinkFactory {

    @Override
    public ApexSink createSink(ApexSinkFactoryContext context) {
        return new BlackHoleSink();
    }

    @Override
    @SuppressWarnings("SpellCheckingInspection")
    public String factoryIdentifier() {
        return "blackhole";
    }
}
