package omni.audit.apex.connectors.jdbc.sink;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexSinkFactory;
import omni.audit.apex.api.factory.ApexSinkFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.sink.ApexSink;

/**
* <AUTHOR>
* create at 2025/6/26 10:24
* @description: 
**/
@AutoService(Factory.class)
public class JdbcSinkFactory implements ApexSinkFactory {


    @Override
    public ApexSink createSink(ApexSinkFactoryContext context) {
        return new JdbcSink(context.getOptions());
    }

    @Override
    public String factoryIdentifier() {
        return "jdbc";
    }
}