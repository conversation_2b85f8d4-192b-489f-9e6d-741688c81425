package omni.audit.apex.connectors.jdbc.sink;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.api.type.DefaultApexRow;
import omni.audit.apex.connectors.jdbc.config.JdbcConnectionConfig;
import omni.audit.apex.connectors.jdbc.config.JdbcOptions;
import omni.audit.common.batch.AutoFlushBatch;
import omni.audit.common.batch.ConcurrentAutoFlushBatch;
import org.apache.commons.dbutils.DbUtils;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.MapListHandler;
import org.apache.commons.dbutils.handlers.ScalarHandler;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @class JdbcSinkWriter
 * @created 2025/7/18 14:30
 * @desc JDBC sink writer implementation for PostgreSQL
 **/
@Slf4j
public class JdbcSinkWriter implements SinkWriter<ApexRow> {
    protected final ReadonlyConfig config;
    private final QueryRunner queryRunner;
    private ConcurrentAutoFlushBatch<Map<String, Object>> batch;
    private boolean batchFlush = false;


    public JdbcSinkWriter(ReadonlyConfig config) {
        this.config = config;
        this.queryRunner = createQueryRunner(config);
        Integer batchSize = config.get(JdbcOptions.BATCH_SIZE);
        batchFlush = batchSize > 0;
        if (batchFlush) {
            batch = new ConcurrentAutoFlushBatch<>(list -> {
                batchInsert(list);
            }, batchSize);
            initThread();
        }

    }

    public void initThread() {
        Executors.newSingleThreadScheduledExecutor()
                .scheduleAtFixedRate(() -> {
                    try {
                        batch.flush();
                    } catch (Exception e) {
                        log.error("schedule error", e);
                    }
                }, 10, 3, TimeUnit.SECONDS);
    }

    private QueryRunner createQueryRunner(ReadonlyConfig config) {
        JdbcConnectionConfig jdbcConnectionConfig = JdbcConnectionConfig.of(config);
        JdbcConfig jdbcConfig = JdbcConfig.builder().username(jdbcConnectionConfig.getUsername())
                .jdbcUrl(jdbcConnectionConfig.getUrl())
                .password(jdbcConnectionConfig.getPassword())
                .driverClassName(jdbcConnectionConfig.getDriverName())
                .build();

        return new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
    }

    @Override
    public ApexRow write(ApexRow row) {
        Map<String, Object> map = row.toMap();
        writeMap(map);
        return row;
    }

    protected void writeMap(Map<String, Object> map) {
        if (batchFlush) {
            batch.add(map);
            return;
        }
        String sql = config.get(JdbcOptions.QUERY);
        try {
            Connection connection = queryRunner.getDataSource().getConnection();
            Object[] params = getParams(map, connection);
            queryRunner.insert(sql, new ScalarHandler<Integer>(), params);
        } catch (SQLException e) {
            System.out.println(e);
            log.error("insert sql:{} error:", sql, e);
        }
    }

    private void batchInsert(List<Map<String, Object>> list) {
        String sql = config.get(JdbcOptions.QUERY);

        try (Connection connection = queryRunner.getDataSource().getConnection()) {
            List<Object[]> paramsList = new ArrayList<>();
            for (Map<String, Object> map : list) {
                try {
                    Object[] params = getParams(map, connection);
                    if (params != null) {
                        paramsList.add(params);
                    }
                } catch (SQLException e) {
                    log.error("get params error:", e);
                    // 中断批量插入操作以避免后续异常
                    return;
                }
            }

            if (!paramsList.isEmpty()) {
                Object[][] params = paramsList.toArray(new Object[0][]);
                try {
                    queryRunner.batch(sql, params);
                } catch (SQLException e) {
                    log.error("batch sql:{} error:", sql, e);
                }
            }
        } catch (SQLException e) {
            log.error("get connection error:", e);
        }
    }

    private Object[] getParams(Map<String, Object> rowData, Connection connection) throws SQLException {
        Map<String, String> fieldMapping = config.get(JdbcOptions.FIELD_MAPPING);
        List<Object> params = new ArrayList<>();
        for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
            String sourceField = entry.getKey();
            if (rowData.containsKey(sourceField)) {
                Object value = rowData.get(sourceField);
                if (value instanceof Collection) {
                    params.add(connection.createArrayOf("text", ((Collection<?>) value).toArray()));
                } else {
                    params.add(value);
                }
            } else {
                params.add(null);
            }
        }
        return params.toArray();
    }

}