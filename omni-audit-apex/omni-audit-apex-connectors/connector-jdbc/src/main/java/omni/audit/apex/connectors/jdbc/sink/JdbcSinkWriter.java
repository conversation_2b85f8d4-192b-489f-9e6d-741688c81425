package omni.audit.apex.connectors.jdbc.sink;

import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.connectors.jdbc.config.JdbcConnectionConfig;
import omni.audit.apex.connectors.jdbc.config.JdbcOptions;
import omni.audit.common.batch.ConcurrentAutoFlushBatch;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.ScalarHandler;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @class JdbcSinkWriter
 * @created 2025/7/18 14:30
 * @desc JDBC sink writer implementation for PostgreSQL
 **/
@Slf4j
public class JdbcSinkWriter implements SinkWriter<ApexRow> {

    // 常量定义
    private static final int DEFAULT_BATCH_FLUSH_INITIAL_DELAY = 10;
    private static final int DEFAULT_BATCH_FLUSH_PERIOD = 3;
    private static final TimeUnit DEFAULT_TIME_UNIT = TimeUnit.SECONDS;
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 1000;

    protected final ReadonlyConfig config;

    private final QueryRunner queryRunner;

    private DataSource dataSource;

    private ConcurrentAutoFlushBatch<Map<String, Object>> batch;

    private ReentrantLock connectionLock = new ReentrantLock();

    private boolean batchFlush = false;

    private volatile Connection connection;
    private ScheduledExecutorService scheduledExecutor;

    public JdbcSinkWriter(ReadonlyConfig config) {
        this.config = Objects.requireNonNull(config, "Config cannot be null");
        this.dataSource = createDataSource(config);
        this.queryRunner = new QueryRunner(dataSource);

        Integer batchSize = config.get(JdbcOptions.BATCH_SIZE);
        this.batchFlush = batchSize != null && batchSize > 0;

        if (batchFlush) {
            this.batch = new ConcurrentAutoFlushBatch<>(this::batchInsert, batchSize);
            initScheduledFlush();
        } else {
            this.batch = null;
        }

    }

    private DataSource createDataSource(ReadonlyConfig config) {
        try {
            JdbcConnectionConfig jdbcConnectionConfig = JdbcConnectionConfig.of(config);
            JdbcConfig jdbcConfig = JdbcConfig.builder()
                    .username(jdbcConnectionConfig.getUsername())
                    .jdbcUrl(jdbcConnectionConfig.getUrl())
                    .password(jdbcConnectionConfig.getPassword())
                    .driverClassName(jdbcConnectionConfig.getDriverName())
                    .schema(jdbcConnectionConfig.getSchema())
                    .build();

            return DataSourceFactory.createDataSource(jdbcConfig);
        } catch (Exception e) {
            log.error("Failed to create data source", e);
            throw new RuntimeException("Data source creation failed", e);
        }
    }

    private void initScheduledFlush() {
        this.scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "JdbcSinkWriter-BatchFlush");
            thread.setDaemon(true);
            return thread;
        });

        scheduledExecutor.scheduleAtFixedRate(
                this::safeFlushBatch,
                DEFAULT_BATCH_FLUSH_INITIAL_DELAY,
                DEFAULT_BATCH_FLUSH_PERIOD,
                DEFAULT_TIME_UNIT
        );
    }

    private void safeFlushBatch() {
        try {
            if (batch != null) {
                batch.flush();
            }
        } catch (Exception e) {
            log.error("Scheduled batch flush error", e);
        }
    }

    @Override
    public void open() {
        ensureConnection();
    }

    private void ensureConnection() {
        connectionLock.lock();
        try {
            if (connection == null || connection.isClosed()) {
                closeConnectionSafely(connection);
                this.connection = dataSource.getConnection();
                log.debug("New JDBC connection established");
            }
        } catch (SQLException e) {
            log.error("Failed to establish JDBC connection", e);
            throw new RuntimeException("JDBC connection establishment failed", e);
        } finally {
            connectionLock.unlock();
        }
    }

    @Override
    public ApexRow write(ApexRow row) {
        Objects.requireNonNull(row, "Row cannot be null");
        ensureConnection();
        Map<String, Object> map = row.toMap();
        writeMap(map);
        return row;
    }

    @Override
    public void close() {
        log.info("Closing JdbcSinkWriter");

        // 关闭定时任务
        shutdownScheduledExecutor();

        // 刷新剩余批次数据
        flushRemainingBatch();

        // 关闭连接
        closeConnectionSafely(connection);

        // 关闭数据源
        closeDataSourceSafely();

        log.info("JdbcSinkWriter closed successfully");
    }

    private void shutdownScheduledExecutor() {
        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                    log.warn("Scheduled executor did not terminate gracefully");
                }
            } catch (InterruptedException e) {
                scheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
                log.warn("Interrupted while waiting for scheduled executor termination");
            }
        }
    }

    private void flushRemainingBatch() {
        if (batch != null) {
            try {
                batch.flush();
                log.debug("Flushed remaining batch data");
            } catch (Exception e) {
                log.error("Failed to flush remaining batch data", e);
            }
        }
    }

    private void closeConnectionSafely(Connection conn) {
        if (conn != null) {
            try {
                if (!conn.isClosed()) {
                    conn.close();
                    log.debug("JDBC connection closed");
                }
            } catch (SQLException e) {
                log.warn("Failed to close JDBC connection", e);
            }
        }
    }

    private void closeDataSourceSafely() {
        if (dataSource instanceof HikariDataSource) {
            try {
                ((HikariDataSource) dataSource).close();
                log.debug("Data source closed");
            } catch (Exception e) {
                log.warn("Failed to close data source", e);
            }
        }
    }

    protected void writeMap(Map<String, Object> map) {
        Objects.requireNonNull(map, "Map cannot be null");
        if (batchFlush) {
            batch.add(map);
        } else {
            insertSingle(map);
        }
    }

    private void insertSingle(Map<String, Object> map) {
        String sql = config.get(JdbcOptions.QUERY);
        if (sql == null || sql.trim().isEmpty()) {
            log.error("SQL query is not configured");
            return;
        }

        int attempts = 0;
        while (attempts < MAX_RETRY_ATTEMPTS) {
            try {
                Object[] params = getParams(map);
                queryRunner.insert(sql, new ScalarHandler<>(), params);
                return; // 成功执行，退出重试循环
            } catch (SQLException e) {
                attempts++;
                log.error("Insert failed (attempt {}/{}): SQL: {}", attempts, MAX_RETRY_ATTEMPTS, sql, e);

                if (attempts < MAX_RETRY_ATTEMPTS) {
                    waitBeforeRetry();
                    ensureConnection(); // 重新建立连接
                }
            }
        }

        log.error("Failed to insert after {} attempts, data may be lost", MAX_RETRY_ATTEMPTS);
    }

    private void batchInsert(List<Map<String, Object>> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        ensureConnection();
        String sql = config.get(JdbcOptions.QUERY);
        if (sql == null || sql.trim().isEmpty()) {
            log.error("SQL query is not configured for batch insert");
            return;
        }

        List<Object[]> paramsList = new ArrayList<>();
        List<Map<String, Object>> failedRows = new ArrayList<>();

        // 准备参数
        for (Map<String, Object> map : list) {
            try {
                Object[] params = getParams(map);
                if (params != null) {
                    paramsList.add(params);
                } else {
                    failedRows.add(map);
                }
            } catch (Exception e) {
                log.error("Failed to prepare parameters for row: {}", map, e);
                failedRows.add(map);
            }
        }

        // 记录失败的行
        if (!failedRows.isEmpty()) {
            log.warn("Failed to prepare parameters for {} rows out of {}", failedRows.size(), list.size());
        }

        // 执行批量插入
        if (!paramsList.isEmpty()) {
            executeBatchInsert(sql, paramsList);
        }
    }

    private void executeBatchInsert(String sql, List<Object[]> paramsList) {
        Object[][] params = paramsList.toArray(new Object[0][]);

        int attempts = 0;
        while (attempts < MAX_RETRY_ATTEMPTS) {
            try {
                int[] results = queryRunner.batch(sql, params);
                log.debug("Batch insert completed successfully, {} rows affected",
                        results != null ? results.length : 0);
                return;
            } catch (SQLException e) {
                attempts++;
                log.error("Batch insert failed (attempt {}/{}): SQL: {}", attempts, MAX_RETRY_ATTEMPTS, sql, e);

                if (attempts < MAX_RETRY_ATTEMPTS) {
                    waitBeforeRetry();
                    ensureConnection();
                }
            }
        }

        log.error("Failed to execute batch insert after {} attempts, {} rows may be lost",
                MAX_RETRY_ATTEMPTS, paramsList.size());
    }

    private void waitBeforeRetry() {
        try {
            Thread.sleep(RETRY_DELAY_MS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Interrupted while waiting for retry");
        }
    }

    private Object[] getParams(Map<String, Object> rowData) throws SQLException {
        Map<String, String> fieldMapping = config.get(JdbcOptions.FIELD_MAPPING);
        if (fieldMapping == null || fieldMapping.isEmpty()) {
            log.warn("Field mapping is not configured");
            return new Object[0];
        }

        List<Object> params = new ArrayList<>();
        for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
            String sourceField = entry.getKey();
            Object value = rowData.get(sourceField);

            if (value instanceof Collection) {
                params.add(createArray((Collection<?>) value));
            } else {
                params.add(value);
            }
        }
        return params.toArray();
    }

    private Object createArray(Collection<?> collection) throws SQLException {
        connectionLock.lock();
        try {
            if (connection != null && !connection.isClosed()) {
                return connection.createArrayOf("text", collection.toArray());
            } else {
                log.warn("Connection is not available for array creation, using collection directly");
                return collection;
            }
        } finally {
            connectionLock.unlock();
        }
    }
}