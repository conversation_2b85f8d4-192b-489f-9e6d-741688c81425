package omni.audit.apex.connectors.jdbc.sink;

import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.sink.ApexSink;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.sink.SinkWriterContext;

/**
 * <AUTHOR>
 * @class JdbcSink
 * @created 2025/7/18 14:30
 * @desc JDBC sink implementation for PostgreSQL
 **/
public class JdbcSink implements ApexSink {

    private final ReadonlyConfig config;

    public JdbcSink(ReadonlyConfig config) {
        this.config = config;
    }

    @Override
    public SinkWriter createWriter(SinkWriterContext context) {
        return new JdbcSinkWriter(config);
    }

}