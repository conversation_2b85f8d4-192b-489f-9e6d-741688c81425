package omni.audit.apex.connectors.jdbc.config;

import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @class JdbcOptions
 * @created 2025/7/18 15:30
 * @desc Configuration options for JDBC connector
 **/
public interface JdbcOptions {
    Option<String> URL = Options.key("url").stringType().noDefaultValue().withDescription("url");

    Option<String> DRIVER =
            Options.key("driver").stringType().noDefaultValue().withDescription("driver");

    Option<Integer> CONNECTION_CHECK_TIMEOUT_SEC =
            Options.key("connection_check_timeout_sec")
                    .intType()
                    .defaultValue(30)
                    .withDescription("connection check time second");
    Option<String> COMPATIBLE_MODE =
            Options.key("compatible_mode")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "The compatible mode of database, required when the database supports multiple compatible modes. For example, when using OceanBase database, you need to set it to 'mysql' or 'oracle'.");

    Option<Integer> MAX_RETRIES =
            Options.key("max_retries").intType().defaultValue(0).withDescription("max_retired");

    Option<String> USER = Options.key("user").stringType().noDefaultValue().withDescription("user");

    Option<String> PASSWORD =
            Options.key("password").stringType().noDefaultValue().withDescription("password");

    Option<String> QUERY =
            Options.key("query").stringType().noDefaultValue().withDescription("query");


    Option<String> CUSTOM_SQL =
            Options.key("custom_sql").stringType().noDefaultValue().withDescription("custom_sql");

    Option<Boolean> AUTO_COMMIT =
            Options.key("auto_commit")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription("auto commit");

    Option<Integer> BATCH_SIZE =
            Options.key("batch_size").intType().defaultValue(0).withDescription("batch size");

    Option<Integer> FETCH_SIZE =
            Options.key("fetch_size")
                    .intType()
                    .defaultValue(0)
                    .withDescription(
                            "For queries that return a large number of objects, "
                                    + "you can configure the row fetch size used in the query to improve performance by reducing the number database hits required to satisfy the selection criteria. Zero means use jdbc default value.");

    Option<Boolean> IS_EXACTLY_ONCE =
            Options.key("is_exactly_once")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("exactly once");

    Option<Boolean> GENERATE_SINK_SQL =
            Options.key("generate_sink_sql")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("generate sql using the database table");

    Option<Boolean> DECIMAL_TYPE_NARROWING =
            Options.key("decimal_type_narrowing")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription(
                            "decimal type narrowing, if true, the decimal type will be narrowed to the int or long type if without loss of precision. Only support for Oracle at now.");

    Option<String> XA_DATA_SOURCE_CLASS_NAME =
            Options.key("xa_data_source_class_name")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("data source class name");

    Option<Integer> MAX_COMMIT_ATTEMPTS =
            Options.key("max_commit_attempts")
                    .intType()
                    .defaultValue(3)
                    .withDescription("max commit attempts");

    Option<Integer> TRANSACTION_TIMEOUT_SEC =
            Options.key("transaction_timeout_sec")
                    .intType()
                    .defaultValue(-1)
                    .withDescription("transaction timeout (second)");

    Option<String> DATABASE =
            Options.key("database").stringType().noDefaultValue().withDescription("database");

    Option<String> TABLE =
            Options.key("table").stringType().noDefaultValue().withDescription("table");

    Option<List<String>> PRIMARY_KEYS =
            Options.key("primary_keys").listType().noDefaultValue().withDescription("primary keys");

    Option<Boolean> SUPPORT_UPSERT_BY_QUERY_PRIMARY_KEY_EXIST =
            Options.key("support_upsert_by_query_primary_key_exist")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("support upsert by query primary_key exist");

    Option<Boolean> ENABLE_UPSERT =
            Options.key("enable_upsert")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription("enable upsert by primary_keys exist");
    Option<Boolean> IS_PRIMARY_KEY_UPDATED =
            Options.key("is_primary_key_updated")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription(
                            "is the primary key updated when performing an update operation");
    Option<Boolean> SUPPORT_UPSERT_BY_INSERT_ONLY =
            Options.key("support_upsert_by_insert_only")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("support upsert by insert only");

    Option<Boolean> USE_COPY_STATEMENT =
            Options.key("use_copy_statement")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("support copy in statement (postgresql)");

    /** source config */
    Option<String> PARTITION_COLUMN =
            Options.key("partition_column")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("partition column");

    Option<BigDecimal> PARTITION_UPPER_BOUND =
            Options.key("partition_upper_bound")
                    .bigDecimalType()
                    .noDefaultValue()
                    .withDescription("partition upper bound");
    Option<BigDecimal> PARTITION_LOWER_BOUND =
            Options.key("partition_lower_bound")
                    .bigDecimalType()
                    .noDefaultValue()
                    .withDescription("partition lower bound");
    Option<Integer> PARTITION_NUM =
            Options.key("partition_num")
                    .intType()
                    .noDefaultValue()
                    .withDescription("partition num");


    Option<Boolean> USE_KERBEROS =
            Options.key("use_kerberos")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("Whether to enable Kerberos, default is false.");

    Option<String> KERBEROS_PRINCIPAL =
            Options.key("kerberos_principal")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "When use kerberos, we should set kerberos principal such as 'test_user@xxx'. ");

    Option<String> KERBEROS_KEYTAB_PATH =
            Options.key("kerberos_keytab_path")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "When use kerberos, we should set kerberos principal file path such as '/home/<USER>/test_user.keytab'. ");

    Option<String> KRB5_PATH =
            Options.key("krb5_path")
                    .stringType()
                    .defaultValue("/etc/krb5.conf")
                    .withDescription(
                            "When use kerberos, we should set krb5 path file path such as '/seatunnel/krb5.conf' or use the default path '/etc/krb5.conf");

    Option<Map<String, String>> PROPERTIES =
            Options.key("properties")
                    .mapType()
                    .noDefaultValue()
                    .withDescription("additional connection configuration parameters");

    Option<Map<String, String>> FIELD_MAPPING = Options.key("field_mapping")
            .type(new com.fasterxml.jackson.core.type.TypeReference<Map<String, String>>() {})
            .noDefaultValue()
            .withDescription("Mapping of source fields to database columns");
}