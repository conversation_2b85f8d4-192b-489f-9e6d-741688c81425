package com.quanzhi.omni.audit;


import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.factory.ApexSinkFactory;
import omni.audit.apex.api.factory.ApexSinkFactoryContext;
import omni.audit.apex.api.factory.FactoryUtil;
import omni.audit.apex.api.sink.ApexSink;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.sink.SinkWriterContext;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.api.type.DefaultApexRow;
import omni.audit.apex.connectors.jdbc.sink.JdbcSinkFactory;
import omni.audit.apex.shade.com.typesafe.config.Config;
import omni.audit.apex.shade.com.typesafe.config.ConfigFactory;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.net.URISyntaxException;
import java.util.List;

/**
 * Unit test for simple App.
 */
public class AppTest {


    /**
     * 测试简单插入
     */
    @Test
    public void testInsert() throws URISyntaxException {
        File file = new File(this.getClass().getResource("/sink1.conf").toURI());
        Config config = ConfigFactory.parseFile(file);
        List<? extends Config> sinks = config.getConfigList("sink");
        ApexRow apexRow = new DefaultApexRow();
        apexRow.set("eventId", "123");
        for (Config c : sinks) {
            ReadonlyConfig readonlyConfig = ReadonlyConfig.fromConfig(c);
            JdbcSinkFactory jdbcSinkFactory = new JdbcSinkFactory();
            ApexSink sink = jdbcSinkFactory.createSink(new ApexSinkFactoryContext(readonlyConfig, null));
            sink.createWriter(new SinkWriterContext() {
            }).write(apexRow);
        }
    }

    /**
     * 测试批量插入
     */
    @Test
    public void testBatchInsert() throws URISyntaxException {
        File file = new File(this.getClass().getResource("/sink-batch.conf").toURI());
        Config config = ConfigFactory.parseFile(file);
        List<? extends Config> sinks = config.getConfigList("sink");
        Config c = sinks.get(0);
        ReadonlyConfig readonlyConfig = ReadonlyConfig.fromConfig(c);
        JdbcSinkFactory jdbcSinkFactory = new JdbcSinkFactory();
        ApexSink sink = jdbcSinkFactory.createSink(new ApexSinkFactoryContext(readonlyConfig, null));
        SinkWriter writer = sink.createWriter(new SinkWriterContext() {
        });
        for (int i = 0; i <1001 ; i++) {
            ApexRow apexRow = new DefaultApexRow();
            apexRow.set("eventId", "eventId"+i);
            writer.write(apexRow);
        }

    }










    /**
     * 测试更新
     */
    @Test
    public void testInsertAndUpdate() throws URISyntaxException {
        File file = new File(this.getClass().getResource("/sink2.conf").toURI());
        Config config = ConfigFactory.parseFile(file);
        List<? extends Config> sinks = config.getConfigList("sink");
        ApexRow apexRow = new DefaultApexRow();
        apexRow.set("name", "testUpdate");
        apexRow.set("operationId", "W-20250605-9RKZz");
        apexRow.set("state", "待确认1");
        apexRow.set("level", 1);
        apexRow.set("discoverTime", System.currentTimeMillis());
        apexRow.set("activeTime", System.currentTimeMillis());
        for (Config c : sinks) {
            ReadonlyConfig readonlyConfig = ReadonlyConfig.fromConfig(c);
            JdbcSinkFactory jdbcSinkFactory = new JdbcSinkFactory();
            ApexSink sink = jdbcSinkFactory.createSink(new ApexSinkFactoryContext(readonlyConfig, null));
            sink.createWriter(new SinkWriterContext() {
            }).write(apexRow);
        }
    }









}
