env {
  parallelism = 1
  job.mode = "STREAMING"
  common {
    output_schema = {
      timestamp = bigint
      type = string
      metaAppName = string
      metaServerVersion = string
      netSrcIp = string
      netSrcPort = int
      netDstIp = string
      netDstPort = int
      netFlowSource = string
      mac = string
      eventId = string
      reqDbName = string
      reqDbUser = string
      reqDbPassword = string
      reqSqlCmdType = string
      reqSql = string
      rspStatus = int
      rspStartTime = bigint
      rspCloseTime = bigint
      rspRowCount = int
      rspResult = string
      srvAddress = string
      srvName = string
      srvType = string
      srvLevel = string
      srvRiskLevel = string
      accessDomains = "array<int>"
      deployDomains = "array<int>"
      srvAccessDomains = "array<int>"
      srvDeployDomains = "array<int>"
      reqDataLabels = "array<int>"
      rspDataLabels = "array<int>"
      srvReqDataLabels = "array<int>"
      srvRspDataLabels = "array<int>"
      srvLifeFlag = "array<int>"
      srvDiscoverTime = bigint
      srvActiveTime = bigint
      srvBizSystem = string
      dbVersion = string
      dbType = string
      dbLevel = string
      dbRiskLevel = string
      dbAccessDomains = "array<int>"
      dbDeployDomains = "array<int>"
      dbReqDataLabels = "array<int>"
      dbRspDataLabels = "array<int>"
      dbBizSystem = string
      dbLifeFlag = "array<int>"
      dbDiscoverTime = bigint
      dbActiveTime = bigint
      eventTypes = "array<int>"
      optionMethod = string
      rspTime = bigint
      tables = "map<int, {\"name\":\"string\",\"dbType\":\"string\",\"dbVersion\":\"string\",\"level\":\"string\",\"riskLevel\":\"string\",\"dbName\":\"string\",\"srvAddress\":\"string\",\"srvName\":\"string\",\"fieldCount\":\"int\",\"bizSystem\":\"string\",\"accessDomains\":\"array<string>\",\"deployDomains\":\"array<string>\",\"reqDataLabels\":\"array<string>\",\"rspDataLabels\":\"array<string>\",\"lifeFlag\":\"array<string>\",\"discoverTime\":\"bigint\",\"activeTime\":\"bigint\",\"featureLabels\":\"array<int>\",\"tableId\":\"string\"}>"
      weaknesses = "map<int,{\"name\":\"string\",\"type\":\"string\",\"level\":\"int\",\"state\":\"string\",\"discoverTime\":\"bigint\",\"activeTime\":\"bigint\",\"srvId\":\"string\",\"dbId\":\"string\",\"tableId\":\"string\",\"weaknessId\":\"int\",\"operationId\":\"string\"}>"
      srvId = string
      dbId = string
      sample ="map<int,{\"eventId\":\"string\",\"timestamp\":\"bigint\",\"netSrcIp\":\"string\",\"netSrcPort\":\"int\",\"netDstIp\":\"string\",\"netDstPort\":\"int\",\"netFlowSource\":\"string\",\"mac\":\"string\",\"reqDbName\":\"string\",\"reqDbUser\":\"string\",\"reqDbPassword\":\"string\",\"reqSql\":\"string\",\"rspStatus\":\"int\",\"rspStartTime\":\"bigint\",\"rspCloseTime\":\"bigint\",\"rspRowCount\":\"int\",\"rspResult\":\"string\",\"srvId\":\"string\",\"dbId\":\"string\",\"accessDomains\":\"array<int>\",\"deployDomains\":\"array<int>\",\"reqDataLabels\":\"array<int>\",\"rspDataLabels\":\"array<int>\"}>"
    }
  }
}

sink {
  jdbc {
    plugin_input = "sample_sink"
    url = "jdbc:postgresql://*************:5432/postgres"
    driver = "org.postgresql.Driver"
    user = postgres
    password = password123
    batch_size = 1000
    field_mapping = {
      "eventId" = "event_id",
      "timestamp" = "timestamp",
      "netSrcIp" = "net_src_ip",
      "netSrcPort" = "net_src_port",
      "netDstIp" = "net_dst_ip",
      "netDstPort" = "net_dst_port",
      "netFlowSource" = "net_flow_source",
      "mac" = "mac",
      "reqDbName" = "req_db_name",
      "reqDbUser" = "req_db_user",
      "reqDbPassword" = "req_db_password",
      "reqSql" = "req_sql",
      "rspStatus" = "rsp_status",
      "rspStartTime" = "rsp_start_time",
      "rspCloseTime" = "rsp_close_time",
      "rspRowCount" = "rsp_row_count",
      "rspResult" = "rsp_result",
      "srvId" = "srv_id",
      "dbId" = "db_id",
      "accessDomains" = "access_domains",
      "deployDomains" = "deploy_domains",
      "reqDataLabels" = "req_data_labels",
      "rspDataLabels" = "rsp_data_labels"
    }
    query ="""
     INSERT INTO sample (
    event_id, timestamp, net_src_ip, net_src_port, net_dst_ip,
    net_dst_port, net_flow_source, mac, req_db_name, req_db_user,
    req_db_password, req_sql, rsp_status, rsp_start_time, rsp_close_time,
    rsp_row_count, rsp_result, srv_id, db_id, access_domains,
    deploy_domains, req_data_labels, rsp_data_labels
) VALUES (
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?
)
    """
  }



}
