package omni.audit.apex.connectors.clickhouse.sink;

import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.sink.ApexSink;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.sink.SinkWriterContext;

public class ClickhouseSink implements ApexSink {

    private final ReadonlyConfig config;

    private ClickhouseSinkConfig clickhouseSinkConfig;

    public ClickhouseSink(ReadonlyConfig config) {
        this.config = config;
        this.clickhouseSinkConfig = new ClickhouseSinkConfig(config);
    }

    @Override
    public SinkWriter createWriter(SinkWriterContext context) {
        return new ClickhouseSinkWriter(clickhouseSinkConfig);
    }
}
