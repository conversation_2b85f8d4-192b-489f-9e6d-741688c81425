package omni.audit.apex.connectors.clickhouse.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class MapUtil {

    public static Object getValue(Map<String, Object> map, String path) {
        try {
            String[] split = path.split("\\.");
            Map<String, Object> temp = map;
            for (int i = 0; i < split.length; i++) {
                if (i == split.length - 1) {
                    return temp.get(split[i]);
                } else {
                    temp = (Map<String, Object>) temp.get(split[i]);
                }
            }
        } catch (Exception e) {
            log.error("get field:{} value error", path, e);
        }
        return null;
    }

}
