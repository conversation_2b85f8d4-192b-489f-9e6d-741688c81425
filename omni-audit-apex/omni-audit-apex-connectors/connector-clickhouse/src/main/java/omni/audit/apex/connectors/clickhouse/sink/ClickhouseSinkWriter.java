package omni.audit.apex.connectors.clickhouse.sink;

import cc.blynk.clickhouse.ClickHousePreparedStatement;
import cc.blynk.clickhouse.except.ClickHouseException;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.api.type.DefaultApexRow;
import omni.audit.apex.connectors.clickhouse.config.ClickhouseConfiguration;
import omni.audit.apex.connectors.clickhouse.inject.*;
import omni.audit.apex.connectors.clickhouse.utils.MapUtil;
import omni.audit.common.batch.AutoFlushBatch;
import omni.audit.common.batch.ConcurrentAutoFlushBatch;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

@Slf4j
public class ClickhouseSinkWriter implements SinkWriter<ApexRow> {

    private final AutoFlushBatch<Map<String, Object>> flushBatch;

    private ThreadLocal<ClickHousePreparedStatement> statementThreadLocal;

    private final Boolean enableBatch;
    private final ClickhouseConfiguration clickhouseConfiguration;
    private final Integer maxRetryCount;
    private final Integer retryIntervalSeconds;
    private final String sql;
    private final List<Map<String, String>> fields;

    private static final Pattern NULLABLE = Pattern.compile("Nullable\\((.*)\\)");
    private static final Pattern LOW_CARDINALITY = Pattern.compile("LowCardinality\\((.*)\\)");
    private static final ClickhouseFieldInjectFunction DEFAULT_INJECT_FUNCTION =
            new StringInjectFunction();
    private final Map<String, ClickhouseFieldInjectFunction> fieldInjectFunctionMap;

    public ClickhouseSinkWriter(ClickhouseSinkConfig clickhouseSinkConfig) {
        this.enableBatch = clickhouseSinkConfig.getBatchEnable();
        this.sql = clickhouseSinkConfig.getSql();
        this.maxRetryCount = clickhouseSinkConfig.getMaxRetryCount();
        this.retryIntervalSeconds = clickhouseSinkConfig.getRetryIntervalSeconds();
        this.clickhouseConfiguration = new ClickhouseConfiguration(clickhouseSinkConfig.getHost(), clickhouseSinkConfig.getUser(), clickhouseSinkConfig.getPwd(), clickhouseSinkConfig.getDb());
        this.fields = clickhouseSinkConfig.getFields();
        this.fieldInjectFunctionMap = createFieldInjectFunctionMap(fields);
        if(clickhouseSinkConfig.getBatchEnable()){
            flushBatch = new ConcurrentAutoFlushBatch<>(this::process, clickhouseSinkConfig.getBatchSize());
            Executors.newSingleThreadScheduledExecutor()
                    .scheduleAtFixedRate(() -> {
                        try {
                            flushBatch.flush();
                        } catch (Exception e) {
                            log.error("schedule flush error:", e);
                        }
                    }, clickhouseSinkConfig.getInitSeconds(), clickhouseSinkConfig.getDelaySeconds(), TimeUnit.SECONDS);
        } else {
            flushBatch = null;
        }
    }

    private Map<String, ClickhouseFieldInjectFunction> createFieldInjectFunctionMap(List<Map<String, String>> fields) {
        Map<String, ClickhouseFieldInjectFunction> fieldInjectFunctionMap = new HashMap<>();
        for (Map<String, String> field : fields) {
            String fieldType = field.get("type");
            String name = field.get("name");
            ClickhouseFieldInjectFunction injectFunction =
                    Stream.of(
                                    new ArrayInjectFunction(),
                                    new MapInjectFunction(),
                                    new BigDecimalInjectFunction(),
                                    new DateInjectFunction(),
                                    new DateTimeInjectFunction(),
                                    new LongInjectFunction(),
                                    new DoubleInjectFunction(),
                                    new FloatInjectFunction(),
                                    new IntInjectFunction(),
                                    new StringInjectFunction(),
                                    new IPv4InjectFunction(),
                                    new IPv6InjectFunction())
                            .filter(f -> f.isCurrentFieldType(unwrapCommonPrefix(fieldType)))
                            .findFirst()
                            .orElse(new StringInjectFunction());
            fieldInjectFunctionMap.put(name, injectFunction);
        }
        return fieldInjectFunctionMap;
    }

    private String unwrapCommonPrefix(String fieldType) {
        Matcher nullMatcher = NULLABLE.matcher(fieldType);
        Matcher lowMatcher = LOW_CARDINALITY.matcher(fieldType);
        if (nullMatcher.matches()) {
            return nullMatcher.group(1);
        } else if (lowMatcher.matches()) {
            return lowMatcher.group(1);
        } else {
            return fieldType;
        }
    }

    public void process(Collection<Map<String, Object>> rows) {
        if(rows == null || rows.isEmpty()){
            return;
        }
        try {
            ClickHousePreparedStatement preparedStatement = getPreparedStatement();
            for (Map<String, Object> row : rows) {
                for (int i = 0; i < fields.size(); i++) {
                    String fieldName = fields.get(i).get("name");
                    String path = fields.get(i).get("path");
                    ClickhouseFieldInjectFunction injectFunction = fieldInjectFunctionMap.getOrDefault(fieldName, DEFAULT_INJECT_FUNCTION);
                    Object fieldValue = MapUtil.getValue(row, path);
                    // 因为值不能为null，只能设置默认值
                    if (fieldValue == null) {
                        fieldValue = injectFunction.getDefaultValue();
                    }
                    injectFunction.injectFields(preparedStatement, i + 1, fieldValue);
                }
                preparedStatement.addBatch();
            }
            bulk(preparedStatement);
        } catch (Exception e) {
            log.error("batch insert error:", e);
        }
    }

    public void bulk(ClickHousePreparedStatement preparedStatement) {
        bulk0(preparedStatement, -1);
    }

    public void bulk0(ClickHousePreparedStatement preparedStatement, int count) {
        try {
            count += 1;
            int[] ints = preparedStatement.executeBatch();
            // 可以缓存一下，隔一段时间算一下，或者做个配置，有问题排查的时候看
            log.info("bulk insert success, size:{}, retry:{}", ints == null ? -1 : ints.length, count);
        } catch (Exception e) {
            log.error("execute batch insert error, retry {}:", count, e);
            if (e.getCause() instanceof ClickHouseException && e.getMessage().contains("Connection refused")) {
                log.info("wait for retry clickhouse insert, wait {} seconds", retryIntervalSeconds);
                try {
                    TimeUnit.SECONDS.sleep(retryIntervalSeconds);
                } catch (InterruptedException ignored) {
                }
                if (maxRetryCount >= count) {
                    bulk0(preparedStatement, count);
                }
            } else {
                log.error("batch insert clickhouse error:", e);
            }
        }
    }

    private ClickHousePreparedStatement getPreparedStatement() {
        ClickHousePreparedStatement clickHousePreparedStatement = statementThreadLocal.get();
        if (clickHousePreparedStatement != null) {
            return clickHousePreparedStatement;
        }
        initConnection();
        return statementThreadLocal.get();
    }

    private void initConnection() {
        try (ClickHousePreparedStatement clickHousePreparedStatement = (ClickHousePreparedStatement) clickhouseConfiguration.newConnection().prepareStatement(sql)) {
            statementThreadLocal.set(clickHousePreparedStatement);
        } catch (SQLException e) {
            log.error("ClickhouseSinkWriter initConnection error:", e);
        }
    }

    @Override
    public ApexRow write(ApexRow row) {
        if (row instanceof DefaultApexRow) {
            DefaultApexRow defaultApexRow = (DefaultApexRow) row;
            try {
                Map<String, Object> rowMap = defaultApexRow.toMap();
                if(enableBatch){
                    flushBatch.add(rowMap);
                }else{
                    // TODO 直接入库，但是clickhouse这样做应该是不行的
                    List<Map<String, Object>> rows = new ArrayList<>();
                    rows.add(rowMap);
                    process(rows);
                }
            } catch (Exception e) {
                log.error("ClickhouseSinkWriter write error, row: {}", row, e);
            }
        }
        return null;
    }
}
