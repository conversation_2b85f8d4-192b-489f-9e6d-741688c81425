package omni.audit.apex.connectors.clickhouse.sink;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexSinkFactory;
import omni.audit.apex.api.factory.ApexSinkFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.sink.ApexSink;

@AutoService(Factory.class)
public class ClickhouseSinkFactory implements ApexSinkFactory {

    @Override
    public ApexSink createSink(ApexSinkFactoryContext context) {
        return new ClickhouseSink(context.getOptions());
    }

    @Override
    public String factoryIdentifier() {
        return "clickhouse";
    }

}
