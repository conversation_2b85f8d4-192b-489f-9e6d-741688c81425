package omni.audit.apex.connectors.clickhouse.inject;

import java.sql.PreparedStatement;
import java.sql.SQLException;

public class IPv6InjectFunction implements ClickhouseFieldInjectFunction {

    private static final String DEFAULT_VALUE = "::";

    @Override
    public void injectFields(PreparedStatement statement, int index, Object value) throws SQLException {
        if (value.toString().contains(":")) {
            statement.setString(index, value.toString());
        } else {
            statement.setString(index, DEFAULT_VALUE);
        }
    }

    @Override
    public boolean isCurrentFieldType(String fieldType) {
        return "IPv6".equals(fieldType);
    }

    @Override
    public Object getDefaultValue() {
        return DEFAULT_VALUE;
    }

}
