package omni.audit.apex.connectors.clickhouse.config;

import com.fasterxml.jackson.core.type.TypeReference;
import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;

import java.util.List;
import java.util.Map;

public class ClickhouseSinkOptions extends ClickhouseBaseOptions{

    /** Bulk size of clickhouse jdbc */
    public static final Option<Integer> BULK_SIZE =
            Options.key("bulk_size")
                    .intType()
                    .defaultValue(20000)
                    .withDescription("Bulk size of clickhouse jdbc");

    public static final Option<Boolean> ENABLE_BATCH =
            Options.key("batch_enable")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription("");

    public static final Option<Integer> INIT_SECONDS =
            Options.key("init_seconds")
                    .intType()
                    .defaultValue(10)
                    .withDescription("");

    public static final Option<Integer> DELAY_SECONDS =
            Options.key("delay_seconds")
                    .intType()
                    .defaultValue(30)
                    .withDescription("");

    public static final Option<List<Map<String, String>>> FIELDS =
            Options.key("fields")
                    .type(new TypeReference<List<Map<String, String>>>() {})
                    .noDefaultValue()
                    .withDescription("filed param");

    /** Clickhouse table name */
    public static final Option<String> TABLE =
            Options.key("table")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("Clickhouse table name");

    /** Split mode when table is distributed engine */
    public static final Option<Boolean> SPLIT_MODE =
            Options.key("split_mode")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("Split mode when table is distributed engine");

    /** When split_mode is true, the sharding_key use for split */
    public static final Option<String> SHARDING_KEY =
            Options.key("sharding_key")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("When split_mode is true, the sharding_key use for split");

    public static final Option<String> PRIMARY_KEY =
            Options.key("primary_key")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "Mark the primary key column from clickhouse table, and based on primary key execute INSERT/UPDATE/DELETE to clickhouse table");

    public static final Option<Boolean> SUPPORT_UPSERT =
            Options.key("support_upsert")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("Support upsert row by query primary key");

    public static final Option<Boolean> ALLOW_EXPERIMENTAL_LIGHTWEIGHT_DELETE =
            Options.key("allow_experimental_lightweight_delete")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription(
                            "Allow experimental lightweight delete based on `*MergeTree` table engine");

    public static final Option<String> SQL =
            Options.key("sql")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("when data_save_mode selects CUSTOM_PROCESSING custom SQL");
}
