package omni.audit.apex.connectors.clickhouse.config;

import cc.blynk.clickhouse.BalancedClickhouseDataSource;
import cc.blynk.clickhouse.ClickHouseConnection;
import cc.blynk.clickhouse.settings.ClickHouseConnectionSettings;
import lombok.extern.slf4j.Slf4j;

import java.sql.SQLException;
import java.util.Properties;

@Slf4j
public class ClickhouseConfiguration {
    String driver = "cc.blynk.clickhouse.ClickHouseDriver";
    BalancedClickhouseDataSource dataSource;
    String user;
    String pwd;

    public ClickhouseConfiguration(String host, String user, String pwd, String db) {
        this.user = user;
        this.pwd = pwd;
        try {
            Class.forName(driver);
        }catch (Exception ignored){
        }
        String urlSb = "jdbc:clickhouse://" +
                host + "/" + db +
                "?characterEncoding=utf8&useSSL=false";
        Properties properties = new Properties();
        properties.put(ClickHouseConnectionSettings.CONNECTION_TIMEOUT, 10000);
        properties.put(ClickHouseConnectionSettings.KEEP_ALIVE_TIMEOUT, 300000);
        properties.put(ClickHouseConnectionSettings.SOCKET_TIMEOUT, 3000000);
        properties.put(ClickHouseConnectionSettings.TIME_TO_LIVE_MILLIS, 3000000);
        dataSource = new BalancedClickhouseDataSource(urlSb, properties);
    }

    public ClickHouseConnection newConnection() throws SQLException {
        return this.dataSource.getConnection(user, pwd);
    }
}
