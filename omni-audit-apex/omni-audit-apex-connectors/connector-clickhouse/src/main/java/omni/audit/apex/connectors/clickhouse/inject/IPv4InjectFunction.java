package omni.audit.apex.connectors.clickhouse.inject;

import java.sql.PreparedStatement;
import java.sql.SQLException;

public class IPv4InjectFunction implements ClickhouseFieldInjectFunction {

    private static final String DEFAULT_VALUE = "0.0.0.0";

    @Override
    public void injectFields(PreparedStatement statement, int index, Object value) throws SQLException {
        if (value.toString().contains(":")) {
            statement.setString(index, DEFAULT_VALUE);
        } else {
            statement.setString(index, value.toString());
        }
    }

    @Override
    public boolean isCurrentFieldType(String fieldType) {
        return "IPv4".equals(fieldType);
    }

    @Override
    public Object getDefaultValue() {
        return DEFAULT_VALUE;
    }

}
