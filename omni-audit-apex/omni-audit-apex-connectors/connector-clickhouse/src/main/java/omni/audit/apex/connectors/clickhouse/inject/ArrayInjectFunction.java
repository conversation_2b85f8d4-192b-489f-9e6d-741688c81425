package omni.audit.apex.connectors.clickhouse.inject;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

public class ArrayInjectFunction implements ClickhouseFieldInjectFunction {

    private static final Pattern PATTERN = Pattern.compile("(Array.*)");

    private String fieldType;

    @Override
    public void injectFields(PreparedStatement statement, int index, Object value)
            throws SQLException {
        String sqlType;
        Object[] elements;

        // Handle both List and Array types
        if (value instanceof List) {
            List<?> list = (List<?>) value;
            elements = list.toArray();
        } else if (value instanceof Object[]) {
            elements = (Object[]) value;
        } else {
            throw new IllegalArgumentException("Value must be either List or Array, but got: "
                                               + (value != null ? value.getClass().getName() : "null"));
        }
        String type = fieldType.substring(fieldType.indexOf("(") + 1, fieldType.indexOf(")"));
        switch (type) {
            case "String":
            case "Int128":
            case "UInt128":
            case "Int256":
            case "UInt256":
                sqlType = "TEXT";
                elements = Arrays.copyOf(elements, elements.length, String[].class);
                break;
            case "Int8":
            case "UInt8":
            case "Int16":
            case "UInt16":
            case "Int32":
                sqlType = "INTEGER";
                elements = Arrays.copyOf(elements, elements.length, Integer[].class);
                break;
            case "UInt32":
            case "Int64":
            case "UInt64":
                sqlType = "BIGINT";
                elements = Arrays.copyOf(elements, elements.length, Long[].class);
                break;
            case "Float32":
                sqlType = "REAL";
                elements = Arrays.copyOf(elements, elements.length, Float[].class);
                break;
            case "Float64":
                sqlType = "DOUBLE";
                elements = Arrays.copyOf(elements, elements.length, Double[].class);
                break;
            case "Bool":
                sqlType = "BOOLEAN";
                elements = Arrays.copyOf(elements, elements.length, Boolean[].class);
                break;
            default:
                throw new RuntimeException("array inject error, unsupported data type: " + type);
        }
        statement.setArray(index, statement.getConnection().createArrayOf(sqlType, elements));
    }

    @Override
    public boolean isCurrentFieldType(String fieldType) {
        if (PATTERN.matcher(fieldType).matches()) {
            this.fieldType = fieldType;
            return true;
        }
        return false;
    }

    @Override
    public Object getDefaultValue() {
        return new ArrayList<>();
    }

}
