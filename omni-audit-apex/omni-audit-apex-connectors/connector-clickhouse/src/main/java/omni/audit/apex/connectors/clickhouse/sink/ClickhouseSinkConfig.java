package omni.audit.apex.connectors.clickhouse.sink;

import lombok.Getter;
import omni.audit.apex.api.config.ReadonlyConfig;

import java.util.List;
import java.util.Map;

import static omni.audit.apex.connectors.clickhouse.config.ClickhouseSinkOptions.*;

public class ClickhouseSinkConfig {

    @Getter
    private final Integer batchSize;

    @Getter
    private final Boolean batchEnable;

    @Getter
    private final String sql;

    @Getter
    private final Integer initSeconds;

    @Getter
    private final Integer delaySeconds;

    @Getter
    private final Integer maxRetryCount;

    @Getter
    private final Integer retryIntervalSeconds;

    @Getter
    private final String host;

    @Getter
    private final String user;

    @Getter
    private final String pwd;

    @Getter
    private final String db;

    @Getter
    private final List<Map<String, String>> fields;

    public ClickhouseSinkConfig(ReadonlyConfig readonlyConfig) {
        this.batchSize = readonlyConfig.get(BULK_SIZE);
        this.batchEnable = readonlyConfig.get(ENABLE_BATCH);
        this.initSeconds = readonlyConfig.get(INIT_SECONDS);
        this.delaySeconds = readonlyConfig.get(DELAY_SECONDS);
        this.sql = readonlyConfig.get(SQL);
        this.maxRetryCount = readonlyConfig.get(RETRY_COUNT);
        this.retryIntervalSeconds = readonlyConfig.get(RETRY_INTERVAL);
        this.host = readonlyConfig.get(HOST);
        this.user = readonlyConfig.get(USERNAME);
        this.pwd = readonlyConfig.get(PASSWORD);
        this.db = readonlyConfig.get(DATABASE);
        this.fields = readonlyConfig.get(FIELDS);
    }

}
