package omni.audit.apex.connectors.clickhouse.config;

import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;

import java.time.ZoneId;
import java.util.Collections;
import java.util.Map;

public class ClickhouseBaseOptions {

    /** Clickhouse server host */
    public static final Option<String> HOST =
            Options.key("host")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("Clickhouse server host");

    /** Clickhouse database name */
    public static final Option<String> DATABASE =
            Options.key("database")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("Clickhouse database name");

    /** Clickhouse server username */
    public static final Option<String> USERNAME =
            Options.key("username")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("Clickhouse server username");

    /** Clickhouse server password */
    public static final Option<String> PASSWORD =
            Options.key("password")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("Clickhouse server password");


    public static final Option<Integer> RETRY_COUNT =
            Options.key("retry_count")
                    .intType()
                    .defaultValue(1)
                    .withDescription("");

    public static final Option<Integer> RETRY_INTERVAL =
            Options.key("retry_interval")
                    .intType()
                    .defaultValue(5)
                    .withDescription("");

    /** Clickhouse server timezone */
    public static final Option<String> SERVER_TIME_ZONE =
            Options.key("server_time_zone")
                    .stringType()
                    .defaultValue(ZoneId.systemDefault().getId())
                    .withDescription(
                            "The session time zone in database server."
                                    + "If not set, then ZoneId.systemDefault() is used to determine the server time zone");

    public static final Option<Map<String, String>> CLICKHOUSE_CONFIG =
            Options.key("clickhouse.config")
                    .mapType()
                    .defaultValue(Collections.emptyMap())
                    .withDescription("Clickhouse custom config");
}
