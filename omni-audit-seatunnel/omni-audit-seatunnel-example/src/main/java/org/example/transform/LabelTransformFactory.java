package org.example.transform;

import com.google.auto.service.AutoService;
import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.connector.TableTransform;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableTransformFactory;
import org.apache.seatunnel.api.table.factory.TableTransformFactoryContext;

/**
 * <AUTHOR>
 * @class LabelTransformFactory
 * @created 2025/5/9 15:11
 * @desc
 **/
@AutoService(Factory.class)
public class LabelTransformFactory implements TableTransformFactory {
    @Override
    public String factoryIdentifier() {
        return LabelTransform.PLUGIN_NAME;
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder().build();
    }

    @Override
    public  TableTransform createTransform(TableTransformFactoryContext context) {
        return () -> new LabelMultiCatalogTransform(context.getCatalogTables(), context.getOptions());
    }
}
