package org.example.transform;

import org.apache.seatunnel.api.table.catalog.*;
import org.apache.seatunnel.api.table.type.*;
import org.apache.seatunnel.common.exception.CommonError;
import org.apache.seatunnel.transform.common.AbstractCatalogSupportMapTransform;

import java.lang.reflect.Array;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class LabelTransform
 * @created 2025/5/9 14:38
 * @desc
 **/
public class LabelTransform extends AbstractCatalogSupportMapTransform {

    public static final String PLUGIN_NAME = "BizLabel";
    private final LabelTransformConfig config;

    private int[] inputValueIndexList;

    private List<String> fieldNames;
//    private List<Integer> fieldOriginalIndexes;
    private List<SeaTunnelDataType<?>> fieldTypes;

    public LabelTransform(LabelTransformConfig config, CatalogTable catalogTable) {
        super(catalogTable);
        this.config = config;
        SeaTunnelRowType seaTunnelRowType = catalogTable.getTableSchema().toPhysicalRowDataType();
        initOutputFields(seaTunnelRowType);
    }

    private void initOutputFields(
            SeaTunnelRowType inputRowType) {
        List<String> fieldNames = new ArrayList<>();
//        List<Integer> fieldOriginalIndexes = new ArrayList<>();
        List<SeaTunnelDataType<?>> fieldsType = new ArrayList<>();
//        for (Map.Entry<String, String> field : fields.entrySet()) {
//            String srcField = field.getValue();
//            int srcFieldIndex;
//            try {
//                srcFieldIndex = inputRowType.indexOf(srcField);
//            } catch (IllegalArgumentException e) {
//                throw TransformCommonError.cannotFindInputFieldError(getPluginName(), srcField);
//            }
//            fieldNames.add(field.getKey());
//            fieldOriginalIndexes.add(srcFieldIndex);
//            fieldsType.add(inputRowType.getFieldType(srcFieldIndex));
//        }

        fieldNames.addAll(Arrays.asList(inputRowType.getFieldNames()));
        fieldsType.addAll(Arrays.asList(inputRowType.getFieldTypes()));

        this.fieldNames = fieldNames;
//        this.fieldOriginalIndexes = fieldOriginalIndexes;
        this.fieldTypes = fieldsType;
    }

    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow seaTunnelRow) {
        // TODO 祁灵 2025/5/9 16:45: 添加字段
        Object[] newFields = new Object[seaTunnelRow.getArity()+1];
        for (int i = 0; i < seaTunnelRow.getArity(); i++) {
            newFields[i] = seaTunnelRow.getField(i);
        }
        newFields[seaTunnelRow.getArity()] = "cbcbcbc";
        SeaTunnelRow newRow = new SeaTunnelRow(newFields);
        newRow.setRowKind(seaTunnelRow.getRowKind());
        newRow.setTableId(seaTunnelRow.getTableId());
        return newRow;
    }

    @Override
    protected TableSchema transformTableSchema() {
        List<Column> outputColumns = new ArrayList<>();

        SeaTunnelRowType seaTunnelRowType =
                inputCatalogTable.getTableSchema().toPhysicalRowDataType();

        ArrayList<String> outputFieldNames = new ArrayList<>();
        List<Column> inputColumns = inputCatalogTable.getTableSchema().getColumns();

        for (Column column : inputColumns) {
            outputColumns.add(column.copy());
            outputFieldNames.add(column.getName());
        }
        // TODO 祁灵 2025/5/9 17:08: 新增字段
        outputColumns.add(PhysicalColumn.of("label", BasicType.STRING_TYPE, 200, true, "cbcbcbc", ""));
        outputFieldNames.add("label");

        // 约束
        List<ConstraintKey> outputConstraintKeys =
                inputCatalogTable.getTableSchema().getConstraintKeys().stream()
                                 .map(ConstraintKey::copy)
                                 .collect(Collectors.toList());

        PrimaryKey copiedPrimaryKey = null;
        PrimaryKey primaryKey = inputCatalogTable.getTableSchema().getPrimaryKey();
        if (primaryKey != null && outputFieldNames.containsAll(primaryKey.getColumnNames())) {
            copiedPrimaryKey = primaryKey.copy();
        }

        return TableSchema.builder()
                          .columns(outputColumns)
                          .primaryKey(copiedPrimaryKey)
                          .constraintKey(outputConstraintKeys)
                          .build();

    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

//    @Override
//    protected Column[] getOutputColumns() {
//        if (inputCatalogTable == null) {
//            Column[] columns = new Column[fieldNames.size()];
//            for (int i = 0; i < fieldNames.size(); i++) {
//                columns[i] =
//                        PhysicalColumn.of(fieldNames.get(i), fieldTypes.get(i), 200, true, "", "");
//            }
//            return columns;
//        }
//        Map<String, Column> catalogTableColumns =
//                inputCatalogTable.getTableSchema().getColumns().stream()
//                                 .collect(Collectors.toMap(column -> column.getName(), column -> column));
//
//        List<Column> columns = new ArrayList<>();
//        for (Map.Entry<String, String> copyField : config.getFields().entrySet()) {
//            Column srcColumn = catalogTableColumns.get(copyField.getValue());
//            PhysicalColumn destColumn =
//                    PhysicalColumn.of(
//                            copyField.getKey(),
//                            srcColumn.getDataType(),
//                            srcColumn.getColumnLength(),
//                            srcColumn.isNullable(),
//                            srcColumn.getDefaultValue(),
//                            srcColumn.getComment());
//            columns.add(destColumn);
//        }
//        return columns.toArray(new Column[0]);
//    }

//    @Override
//    protected Object[] getOutputFieldValues(SeaTunnelRowAccessor inputRow) {
//        Object[] fieldValues = new Object[fieldNames.size()];
//        for (int i = 0; i < fieldOriginalIndexes.size(); i++) {
//            fieldValues[i] =
//                    clone(
//                            fieldNames.get(i),
//                            fieldTypes.get(i),
//                            inputRow.getField(fieldOriginalIndexes.get(i)));
//        }
//        return fieldValues;
//    }
    private Object clone(String field, SeaTunnelDataType<?> dataType, Object value) {
        if (value == null) {
            return null;
        }
        switch (dataType.getSqlType()) {
            case BOOLEAN:
            case STRING:
            case TINYINT:
            case SMALLINT:
            case INT:
            case BIGINT:
            case FLOAT:
            case DOUBLE:
            case DECIMAL:
            case DATE:
            case TIME:
            case TIMESTAMP:
                return value;
            case BYTES:
                byte[] bytes = (byte[]) value;
                byte[] newBytes = new byte[bytes.length];
                System.arraycopy(bytes, 0, newBytes, 0, bytes.length);
                return newBytes;
            case ARRAY:
                ArrayType arrayType = (ArrayType) dataType;
                Object[] array = (Object[]) value;
                Object newArray =
                        Array.newInstance(arrayType.getElementType().getTypeClass(), array.length);
                for (int i = 0; i < array.length; i++) {
                    Array.set(newArray, i, clone(field, arrayType.getElementType(), array[i]));
                }
                return newArray;
            case MAP:
                MapType mapType = (MapType) dataType;
                Map map = (Map) value;
                Map<Object, Object> newMap = new HashMap<>();
                for (Object key : map.keySet()) {
                    newMap.put(
                            clone(field, mapType.getKeyType(), key),
                            clone(field, mapType.getValueType(), map.get(key)));
                }
                return newMap;
            case ROW:
                SeaTunnelRowType rowType = (SeaTunnelRowType) dataType;
                SeaTunnelRow row = (SeaTunnelRow) value;

                Object[] newFields = new Object[rowType.getTotalFields()];
                for (int i = 0; i < rowType.getTotalFields(); i++) {
                    newFields[i] =
                            clone(
                                    rowType.getFieldName(i),
                                    rowType.getFieldType(i),
                                    row.getField(i));
                }
                SeaTunnelRow newRow = new SeaTunnelRow(newFields);
                newRow.setRowKind(row.getRowKind());
                newRow.setTableId(row.getTableId());
                return newRow;
            case NULL:
                return null;
            default:
                throw CommonError.unsupportedDataType(
                        getPluginName(), dataType.getSqlType().toString(), field);
        }
    }

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }
}
