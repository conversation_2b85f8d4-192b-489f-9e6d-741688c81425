package org.example.transform;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.transform.SeaTunnelTransform;
import org.apache.seatunnel.transform.common.AbstractMultiCatalogMapTransform;

import java.util.List;

/**
 * <AUTHOR>
 * @class LabelMultiCatalogTransform
 * @created 2025/5/9 15:12
 * @desc
 **/
public class LabelMultiCatalogTransform extends AbstractMultiCatalogMapTransform {

    public LabelMultiCatalogTransform(List<CatalogTable> inputCatalogTables, ReadonlyConfig config) {
        super(inputCatalogTables, config);
    }

    @Override
    public String getPluginName() {
        return LabelTransform.PLUGIN_NAME;
    }

    @Override
    protected SeaTunnelTransform<SeaTunnelRow> buildTransform(CatalogTable catalogTable, ReadonlyConfig config) {
        return new LabelTransform(LabelTransformConfig.of(config), catalogTable);
    }
}
