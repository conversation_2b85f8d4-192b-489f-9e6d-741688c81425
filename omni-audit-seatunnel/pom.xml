<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.quanzhi.omni.audit</groupId>
        <artifactId>omni-audit-parent</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>omni-audit-seatunnel</artifactId>
    <name>Omni Audit: Seatunnel</name>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <modules>
        <module>omni-audit-seatunnel-example</module>
        <module>omni-audit-seatunnel-transform</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <seatunnel.version>2.3.11</seatunnel.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- SeaTunnel API -->
            <dependency>
                <groupId>org.apache.seatunnel</groupId>
                <artifactId>seatunnel-api</artifactId>
                <version>${seatunnel.version}</version>
            </dependency>

            <!-- SeaTunnel Common -->
            <dependency>
                <groupId>org.apache.seatunnel</groupId>
                <artifactId>seatunnel-common</artifactId>
                <version>${seatunnel.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.seatunnel</groupId>
                <artifactId>seatunnel-transforms-v2</artifactId>
                <version>${seatunnel.version}</version>
            </dependency>

            <!-- Lombok for reducing boilerplate code -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <scope>provided</scope>
            </dependency>

            <!-- Test Dependencies -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.google.auto.service</groupId>
                <artifactId>auto-service-annotations</artifactId>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.omniaudit</groupId>
                <artifactId>omni-audit-weakness</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.omniaudit</groupId>
                <artifactId>omni-audit-sampling</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>