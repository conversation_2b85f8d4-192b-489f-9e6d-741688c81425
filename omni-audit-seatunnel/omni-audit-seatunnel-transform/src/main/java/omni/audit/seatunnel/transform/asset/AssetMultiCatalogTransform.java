package omni.audit.seatunnel.transform.asset;


import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.transform.SeaTunnelTransform;
import org.apache.seatunnel.transform.common.AbstractMultiCatalogMapTransform;

import java.util.List;
import java.util.Map;

public class AssetMultiCatalogTransform extends AbstractMultiCatalogMapTransform {
    public AssetMultiCatalogTransform(List<CatalogTable> inputCatalogTables, ReadonlyConfig config) {
        super(inputCatalogTables, config);
    }

    @Override
    protected SeaTunnelTransform<SeaTunnelRow> buildTransform(CatalogTable catalogTable, ReadonlyConfig readonlyConfig) {
        return new AssetTransform(catalogTable,readonlyConfig);
    }

    @Override
    public String getPluginName() {
        return AssetTransform.PLUGIN_NAME;
    }
}
