package omni.audit.seatunnel.transform.normalizer;

import lombok.Getter;
import lombok.Setter;
import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @class NormalizerTransform
 * @created 2025/5/19 10:49
 * @desc
 **/
@Getter
@Setter
public class NormalizerTransformConfig implements Serializable {

    /**
     * 原字段转换
     */
    public static final Option<Map<String, String>> FIELDS =
            Options.key("fields")
                   .mapType()
                   .noDefaultValue()
                   .withDescription(
                           "Specify the field append between input and output");

    /**
     * 输出的表结构定义
     */
    public static final Option<Map<String, String>> OUTPUT_SCHEMA =
            Options.key("output_schema")
                   .mapType()
                   .noDefaultValue()
                   .withDescription(
                           "Specify the output schema");

    private Map<String, String> fields;

    private Integer append;

    private Map<String, String> outputSchema;

    public static NormalizerTransformConfig of(ReadonlyConfig config) {
        LinkedHashMap<String, String> fields = new LinkedHashMap<>();
        Optional<Map<String, String>> fieldsOption = config.getOptional(FIELDS);
        if (fieldsOption.isPresent()) {
            fields.putAll(config.get(FIELDS));
        }

        LinkedHashMap<String, String> outputSchema = new LinkedHashMap<>();
        Optional<Map<String, String>> outputSchemaOption = config.getOptional(OUTPUT_SCHEMA);
        if (outputSchemaOption.isPresent()) {
            outputSchema.putAll(config.get(OUTPUT_SCHEMA));
        }

        NormalizerTransformConfig transformConfig = new NormalizerTransformConfig();
        transformConfig.setFields(fields);
        transformConfig.setOutputSchema(outputSchema);
        transformConfig.setAppend(outputSchema.size() - fields.size());
        return transformConfig;
    }
}

