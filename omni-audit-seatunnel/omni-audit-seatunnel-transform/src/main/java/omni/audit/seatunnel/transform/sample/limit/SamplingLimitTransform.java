package omni.audit.seatunnel.transform.sample.limit;

import com.quanzhi.omniaudit.sampling.manager.CacheManager;
import com.quanzhi.omniaudit.sampling.manager.ICacheManager;
import com.quanzhi.omniaudit.sampling.moudle.SamplingConfig;
import com.quanzhi.omniaudit.sampling.service.ISamplingService;
import com.quanzhi.omniaudit.sampling.service.strategy.MemorySamplingStrategy;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.transform.common.AbstractCatalogSupportMapTransform;
import org.apache.seatunnel.transform.exception.TransformCommonError;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:50
 * @description:
 **/
@Slf4j
public class SamplingLimitTransform extends AbstractCatalogSupportMapTransform {

    public static final String PLUGIN_NAME = "SamplingLimit";
    private final SamplingLimitTransformConfig samplingLimitTransformConfig;
    private List<Integer> inputFieldIndexs = new ArrayList<>();
    private transient ISamplingService samplingService;

    public SamplingLimitTransform(SamplingLimitTransformConfig samplingLimitTransformConfig, @NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
        this.samplingLimitTransformConfig = samplingLimitTransformConfig;
        initInputFieldIndexs(inputCatalogTable.getTableSchema().toPhysicalRowDataType(), samplingLimitTransformConfig.getSamplingKeys());
    }

    private void initInputFieldIndexs(SeaTunnelRowType seaTunnelRowType, List<String> samplingKeys) {
        for (String fieldName : samplingKeys) {
            try {
                int inputFieldIndex = seaTunnelRowType.indexOf(fieldName);
                inputFieldIndexs.add(inputFieldIndex);
            } catch (IllegalArgumentException e) {
                throw TransformCommonError.cannotFindInputFieldError(getPluginName(), fieldName);
            }
        }
    }

    @Override
    public void open() {
        ICacheManager cacheManager=new CacheManager();
        SamplingConfig samplingConfig = new SamplingConfig();
        this.samplingService = new MemorySamplingStrategy(cacheManager,samplingConfig);
    }

    private void tryOpen() {
        if (samplingService == null) {
            open();
        }
    }
    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow seaTunnelRow) {
        tryOpen();
        String key = splicingKey(seaTunnelRow);
        if (StringUtils.isEmpty(key)) {
            log.error("splicing sample key is null");
            return seaTunnelRow;
        }
        boolean sampling = samplingService.sampling(key);
        return sampling ? seaTunnelRow : null;
    }

    private String splicingKey(SeaTunnelRow seaTunnelRow) {
        StringBuilder sb = new StringBuilder();
        for (Integer index : inputFieldIndexs){
            Object value =seaTunnelRow.getField(index);
            if (value == null) {
                continue;
            }
            sb.append(value).append("_");
        }
        return sb.length() > 0 ? sb.deleteCharAt(sb.length() - 1).toString() : "";
    }

    @Override
    protected TableSchema transformTableSchema() {
        return inputCatalogTable.getTableSchema().copy();
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }
}