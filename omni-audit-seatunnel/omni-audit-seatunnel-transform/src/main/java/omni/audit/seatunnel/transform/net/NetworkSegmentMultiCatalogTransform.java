package omni.audit.seatunnel.transform.net;


import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.transform.SeaTunnelTransform;
import org.apache.seatunnel.transform.common.AbstractMultiCatalogMapTransform;

import java.util.List;
import java.util.Map;

public class NetworkSegmentMultiCatalogTransform extends AbstractMultiCatalogMapTransform {
    public NetworkSegmentMultiCatalogTransform(List<CatalogTable> inputCatalogTables, ReadonlyConfig config) {
        super(inputCatalogTables, config);
    }

    @Override
    protected SeaTunnelTransform<SeaTunnelRow> buildTransform(CatalogTable catalogTable, ReadonlyConfig readonlyConfig) {
        Map<String, String> netParam = readonlyConfig.get(NetworkSegmentTransformConfig.NET_PARAM);
        return new NetworkSegmentTransform(catalogTable,netParam);
    }

    @Override
    public String getPluginName() {
        return NetworkSegmentTransform.PLUGIN_NAME;
    }
}
