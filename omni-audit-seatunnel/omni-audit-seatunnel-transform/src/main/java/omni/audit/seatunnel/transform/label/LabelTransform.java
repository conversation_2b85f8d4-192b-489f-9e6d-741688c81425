package omni.audit.seatunnel.transform.label;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import omni.audit.asset.handle.processors.AssetHandlerInit;
import omni.audit.common.util.HandleUtil;
import omni.audit.label.handle.LabelHandle;
import omni.audit.seatunnel.transform.common.AbstractSupportAllMapTransform;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.transform.common.AbstractCatalogSupportMapTransform;

import java.util.*;

@Slf4j
public class LabelTransform extends AbstractSupportAllMapTransform {
    public static final String PLUGIN_NAME = "Label";
    private LabelHandle labelHandle;
    private final  List<Map<String, String>> labelParam;



    public LabelTransform(@NonNull CatalogTable catalogTable, List<Map<String, String>> labelParam, List<Map<String, Object>> maps) {
        super(catalogTable,maps);
        this.labelParam = labelParam;
    }

    @Override
    public void open() {
        labelHandle = new LabelHandle(null);
    }

    private void tryOpen() {
        if (labelHandle == null) {
            open();
        }
    }


    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow seaTunnelRow) {
        tryOpen();
        Map<String, Object> eventMap = convertRowToMap(seaTunnelRow);
        for (Map<String, String> stringStringMap : labelParam) {
            List<Integer> labelIds = labelHandle.handle(eventMap, stringStringMap.get("labelType"));
            Integer[] array = labelIds.stream().toArray(Integer[]::new);
            HandleUtil.putMapField(eventMap, stringStringMap.get("labelPath"),array);
        }
        convertMapToRow(seaTunnelRow,eventMap);
        return seaTunnelRow;
    }



    @Override
    protected TableSchema transformTableSchema() {
        return inputCatalogTable.getTableSchema();
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }

}
