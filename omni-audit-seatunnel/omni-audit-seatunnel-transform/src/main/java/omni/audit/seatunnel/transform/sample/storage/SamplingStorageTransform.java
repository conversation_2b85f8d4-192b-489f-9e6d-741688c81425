package omni.audit.seatunnel.transform.sample.storage;

import com.quanzhi.omniaudit.sampling.service.SamplingStorageService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import omni.audit.seatunnel.transform.common.AbstractFieldNameSupportMapTransform;
import omni.audit.seatunnel.transform.common.SchemaParser;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:51
 * @description:
 **/
@Slf4j
public class SamplingStorageTransform extends AbstractFieldNameSupportMapTransform {

    public static final String PLUGIN_NAME = "SamplingStorage";

    private final SamplingStorageTransformConfig samplingStorageTransformConfig;
    private transient SamplingStorageService samplingStorageService;
    private TableSchema outputSampleSchema;
    private List<String> outputSampleFieldNames;



    public SamplingStorageTransform(SamplingStorageTransformConfig samplingStorageTransformConfig, @NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
        this.samplingStorageTransformConfig = samplingStorageTransformConfig;
        SchemaParser schemaParser = new SchemaParser();
        outputSampleSchema = schemaParser.parse(samplingStorageTransformConfig.getSampleScheme());
        outputSampleFieldNames = Arrays.asList(outputSampleSchema.getFieldNames());
    }


    @Override
    public void open() {
        this.samplingStorageService = new SamplingStorageService(samplingStorageTransformConfig.getJdbcConfig(),
                samplingStorageTransformConfig.getMultiDimensionalSamplingConfig(),
                samplingStorageTransformConfig.getSampleConfig());
    }

    private void tryOpen() {
        if (samplingStorageService == null) {
            open();
        }
    }

    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow seaTunnelRow) {
        tryOpen();
        Map<String, Object> map = convertRowToMap(seaTunnelRow);
        Map<String, Object> sample = buildSample(map);
       boolean needSampling = samplingStorageService.checkSample(map,sample);
        if (needSampling) {
            Map<Integer,SeaTunnelRow> seaTunnelRows = new HashMap<>();
            Object[] objects = new Object[outputSampleFieldNames.size()];
            for (int i = 0; i <outputSampleFieldNames.size() ; i++) {
                String fieldName = outputSampleFieldNames.get(i);
                objects[i]=sample.get(fieldName);
            }
            SeaTunnelRow row=new SeaTunnelRow(objects);
            row.setRowKind(seaTunnelRow.getRowKind());
            row.setTableId(seaTunnelRow.getTableId());
            seaTunnelRows.put(0,row);

            Integer index = getIndexByFieldName(seaTunnelRow, "sample");
            seaTunnelRow.setField(index, seaTunnelRows);
        }
        return seaTunnelRow;
    }

    private Map<String, Object> buildSample(Map<String, Object> sourceMap) {
        Map<String, Object> map = new HashMap<>();
        outputSampleFieldNames.forEach(fieldName -> {
            map.put(fieldName, sourceMap.get(fieldName));
        });
        return map;
    }

    @Override
    protected TableSchema transformTableSchema() {
       return inputCatalogTable.getTableSchema().copy();
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }
}