package omni.audit.seatunnel.transform.label;


import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.transform.SeaTunnelTransform;
import org.apache.seatunnel.transform.common.AbstractMultiCatalogMapTransform;

import java.util.List;
import java.util.Map;

public class LabelMultiCatalogTransform extends AbstractMultiCatalogMapTransform {
    public LabelMultiCatalogTransform(List<CatalogTable> inputCatalogTables, ReadonlyConfig config) {
        super(inputCatalogTables, config);
    }

    @Override
    protected SeaTunnelTransform<SeaTunnelRow> buildTransform(CatalogTable catalogTable, ReadonlyConfig readonlyConfig) {
        List<Map<String, String>> labelParam = readonlyConfig.get(LabelTransformConfig.LABEL_PARAM);
        List<Map<String, Object>> maps = readonlyConfig.get(LabelTransformConfig.MAP_PARAM);
        return new LabelTransform(catalogTable,labelParam,maps);
    }

    @Override
    public String getPluginName() {
        return LabelTransform.PLUGIN_NAME;
    }
}
