package omni.audit.seatunnel.transform.insert;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.shade.com.fasterxml.jackson.core.type.TypeReference;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class InsertTransformConfig  implements Serializable {
    public static final Option<String> INPUT_FIELD_NAME =
            Options.key("inputFieldName")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "inputFieldName");

    public static final Option<String> FIRST =
            Options.key("isFirst")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "isFirst");

    public static final Option<List<Map<String, String>>> CONVERT_FIELD =
            Options.key("field")
                    .type(new TypeReference<List<Map<String, String>>>() {})
                    .noDefaultValue()
                    .withDescription("filed param");

    public static final Option<Map<String, String>> TABLE_FIELD =
            Options.key("tableField")
                    .mapType()
                    .noDefaultValue()
                    .withDescription("filed param");
}
