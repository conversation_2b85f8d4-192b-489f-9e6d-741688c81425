package omni.audit.seatunnel.transform.asset;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.shade.com.fasterxml.jackson.core.type.TypeReference;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class AssetTransformConfig implements Serializable {
    public static final Option<List<Map<String, String>>> ASSET_LEVEL_PARAM =
            Options.key("asset.level")
                    .type(new TypeReference<List<Map<String, String>>>() {})
                    .noDefaultValue()
                    .withDescription("asset level param");

    public static final Option<List<Map<String, String>>> ASSET_ASSIGNMENT_PARAM =
            Options.key("asset.assignment")
                    .type(new TypeReference<List<Map<String, String>>>() {})
                    .noDefaultValue()
                    .withDescription("asset assignment param");

    public static final Option<Map<String, String>> ASSET_TIME_PARAM =
            Options.key("asset.time")
                    .mapType()
                    .noDefaultValue()
                    .withDescription("asset time param");

    public static final Option<List<Map<String, Object>>> ASSET_MAP_PARAM =
            Options.key("asset.map")
                    .type(new TypeReference<List<Map<String, Object>>>() {})
                    .noDefaultValue()
                    .withDescription("asset map param");

    public static final Option<Map<String,Object>> ASSET_ID_PARAM =
            Options.key("asset.id")
                    .type(new TypeReference<Map<String, Object>>() {})
                    .noDefaultValue()
                    .withDescription("asset id param");

    public static final Option<List<String>> ASSET_HANDLE =
            Options.key("asset.handle")
                    .listType()
                    .noDefaultValue()
                    .withDescription("asset handle");
}
