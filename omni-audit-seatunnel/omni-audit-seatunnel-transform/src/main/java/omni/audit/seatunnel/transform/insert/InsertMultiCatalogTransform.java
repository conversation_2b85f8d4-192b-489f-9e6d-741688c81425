package omni.audit.seatunnel.transform.insert;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.transform.SeaTunnelTransform;
import org.apache.seatunnel.transform.common.AbstractMultiCatalogFlatMapTransform;
import org.apache.seatunnel.transform.common.AbstractMultiCatalogMapTransform;

import java.util.List;

public class InsertMultiCatalogTransform  extends AbstractMultiCatalogFlatMapTransform {
    public InsertMultiCatalogTransform(List<CatalogTable> inputCatalogTables, ReadonlyConfig config) {
        super(inputCatalogTables, config);
    }

    @Override
    protected SeaTunnelTransform<SeaTunnelRow> buildTransform(CatalogTable catalogTable, ReadonlyConfig readonlyConfig) {
        return  new InsertTransform(catalogTable,readonlyConfig);
    }

    @Override
    public String getPluginName() {
        return InsertTransform.PLUGIN_NAME;
    }
}
