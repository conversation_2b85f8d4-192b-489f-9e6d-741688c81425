package omni.audit.seatunnel.transform.sample.storage;

import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.quanzhi.omniaudit.sampling.moudle.MultiDimensionalSamplingConfig;
import lombok.Getter;
import lombok.Setter;
import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:32
 * @description:
 **/
@Getter
@Setter
public class SamplingStorageTransformConfig implements Serializable {


    public static final Option<Map<String, String>> SAMPLE_SCHEME =
            Options.key("sample_scheme")
                    .mapType()
                    .noDefaultValue()
                    .withDescription(
                            "sample table scheme info");


    public static final Option<Map<String, String>> SAMPLE_CONFIG =
            Options.key("sample_config")
                    .mapType()
                    .noDefaultValue()
                    .withDescription(
                            "sample config info");



    private JdbcConfig jdbcConfig;
    private MultiDimensionalSamplingConfig multiDimensionalSamplingConfig;
    private LinkedHashMap<String, String> sampleScheme;
    private Properties sampleConfig;






    public static SamplingStorageTransformConfig of(ReadonlyConfig config) {
        LinkedHashMap<String, String> sampleScheme=new LinkedHashMap<>();
        Optional<Map<String, String>> o1 = config.getOptional(SAMPLE_SCHEME);
        o1.ifPresent(sampleScheme::putAll);

        Properties sampleConfig=new Properties();
        Optional<Map<String, String>> o2 = config.getOptional(SAMPLE_CONFIG);
        o2.ifPresent(sampleConfig::putAll);

        SamplingStorageTransformConfig samplingStorageTransformConfig=new SamplingStorageTransformConfig();
        samplingStorageTransformConfig.setJdbcConfig(buildJdbcConfig(config));
        samplingStorageTransformConfig.setMultiDimensionalSamplingConfig(buildJdbcMultiDimensionalSamplingConfig());
        samplingStorageTransformConfig.setSampleScheme(sampleScheme);
        samplingStorageTransformConfig.setSampleConfig(sampleConfig);
        return samplingStorageTransformConfig;
    }

    private static MultiDimensionalSamplingConfig buildJdbcMultiDimensionalSamplingConfig() {
        MultiDimensionalSamplingConfig multidimensionalSamplingConfig = new MultiDimensionalSamplingConfig();
        multidimensionalSamplingConfig.setSampleCount(100);
        multidimensionalSamplingConfig.setSimpleKindCount(20);
        multidimensionalSamplingConfig.setSingleSimpleKindCount(2);
        multidimensionalSamplingConfig.setDimensionTypes(Arrays.asList("dbName"));
        return multidimensionalSamplingConfig;
    }

    private static JdbcConfig buildJdbcConfig(ReadonlyConfig config) {
        JdbcConfig jdbcConfig = JdbcConfig.builder().username("postgres").jdbcUrl("*********************************************")
                .password("password123")
                .driverClassName("org.postgresql.Driver")
                .build();
        return jdbcConfig;
    }


}