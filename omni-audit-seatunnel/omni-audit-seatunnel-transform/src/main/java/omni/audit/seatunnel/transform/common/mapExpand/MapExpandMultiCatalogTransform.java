package omni.audit.seatunnel.transform.common.mapExpand;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.transform.SeaTunnelTransform;
import org.apache.seatunnel.transform.common.AbstractMultiCatalogFlatMapTransform;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2025/5/26 20:06
 * @description:
 **/
public class MapExpandMultiCatalogTransform extends AbstractMultiCatalogFlatMapTransform {


    public MapExpandMultiCatalogTransform(List<CatalogTable> inputCatalogTables, ReadonlyConfig config) {
        super(inputCatalogTables, config);
    }

    @Override
    protected SeaTunnelTransform<SeaTunnelRow> buildTransform(CatalogTable catalogTable, ReadonlyConfig readonlyConfig) {
        return new MapExpandTransform(MapExpandTransformConfig.of(readonlyConfig),catalogTable);
    }

    @Override
    public String getPluginName() {
        return MapExpandTransform.PLUGIN_NAME;
    }
}