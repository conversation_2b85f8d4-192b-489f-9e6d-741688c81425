package omni.audit.seatunnel.transform.normalizer;

import lombok.NonNull;
import omni.audit.seatunnel.transform.common.AbstractFieldNameSupportMapTransform;
import omni.audit.seatunnel.transform.common.SchemaParser;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.api.table.type.SqlType;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @class NormalizerTransform
 * @created 2025/5/19 10:49
 * @desc
 **/
public class NormalizerTransform extends AbstractFieldNameSupportMapTransform {

    public static final String PLUGIN_NAME = "Normalizer";

    private final NormalizerTransformConfig config;

    private Map<String, Integer> fieldIndexMap;

    private final SchemaParser schemaParser;

    public NormalizerTransform(NormalizerTransformConfig config, @NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
        this.config = config;
        getInputTableIndex(inputCatalogTable);
        schemaParser = new SchemaParser();
    }

    private void getInputTableIndex(CatalogTable inputCatalogTable) {
        SeaTunnelRowType seaTunnelRowType = inputCatalogTable.getTableSchema().toPhysicalRowDataType();
        Map<String, Integer> fieldIndexMap = new HashMap<>();

        generatePathIndex("", seaTunnelRowType, fieldIndexMap);
        this.fieldIndexMap = fieldIndexMap;
    }

    public static void generatePathIndex(String path, SeaTunnelRowType seaTunnelRowType, Map<String, Integer> indexMap) {
        for (int i = 0; i < seaTunnelRowType.getFieldNames().length; i++) {
            String childPath = path.isEmpty() ? seaTunnelRowType.getFieldNames()[i] : path + "." + seaTunnelRowType.getFieldNames()[i];
            indexMap.put(childPath, i);
            SeaTunnelDataType<?> fieldType = seaTunnelRowType.getFieldType(i);
            if (fieldType.getSqlType().equals(SqlType.ROW)) {
                generatePathIndex(childPath, (SeaTunnelRowType) fieldType, indexMap);
            }
        }
    }

    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow seaTunnelRow) {
        Object[] newFields = new Object[config.getFields().size() + config.getAppend()];

        int i = 0;
        for (Map.Entry<String, String> kv : config.getFields().entrySet()) {
            String value = kv.getValue();
            if (value.contains(".")) {
                String[] paths = value.split("\\.");
                String path = "";
                SeaTunnelRow sto = seaTunnelRow;
                for (int k = 0; k < paths.length; k++) {
                    if (k == 0) {
                        path = paths[k];
                        sto = (SeaTunnelRow) sto.getField(fieldIndexMap.get(path));
                        continue;
                    }
                    path = path + "." + paths[k];
                    if (k == paths.length - 1) {
                        newFields[i++] = sto.getField(fieldIndexMap.get(path));
                    } else {
                        sto = (SeaTunnelRow) sto.getField(fieldIndexMap.get(path));
                    }

                }
            } else {
                newFields[i++] = seaTunnelRow.getField(fieldIndexMap.get(value));
            }
        }

        // append field
        for (int j = 0; j < config.getAppend(); j++) {
            newFields[i++] = null;
        }

        SeaTunnelRow newRow = new SeaTunnelRow(newFields);
        newRow.setRowKind(seaTunnelRow.getRowKind());
        newRow.setTableId(seaTunnelRow.getTableId());
        return newRow;
    }

    @Override
    protected TableSchema transformTableSchema() {
        return schemaParser.parse(config.getOutputSchema());
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return NormalizerTransform.PLUGIN_NAME;
    }
}
