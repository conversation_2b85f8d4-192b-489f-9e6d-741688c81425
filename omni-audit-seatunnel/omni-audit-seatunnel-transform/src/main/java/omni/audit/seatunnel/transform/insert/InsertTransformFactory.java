package omni.audit.seatunnel.transform.insert;

import com.google.auto.service.AutoService;
import omni.audit.seatunnel.transform.asset.AssetMultiCatalogTransform;
import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.connector.TableTransform;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableTransformFactory;
import org.apache.seatunnel.api.table.factory.TableTransformFactoryContext;

@AutoService(Factory.class)
public class InsertTransformFactory  implements TableTransformFactory {
    @Override
    public String factoryIdentifier() {
        return InsertTransform.PLUGIN_NAME;
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder().build();
    }

    @Override
    public TableTransform createTransform(TableTransformFactoryContext context) {
        return() -> new InsertMultiCatalogTransform(context.getCatalogTables(), context.getOptions());
    }
}
