package omni.audit.seatunnel.transform.common.mapExpand;

import com.google.auto.service.AutoService;
import omni.audit.seatunnel.transform.weakness.WeaknessMultiCatalogTransform;
import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.connector.TableTransform;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableTransformFactory;
import org.apache.seatunnel.api.table.factory.TableTransformFactoryContext;

/**
 * <AUTHOR>
 * create at 2025/5/26 20:23
 * @description:
 **/
@AutoService(Factory.class)
public class MapExpandTransformFactory implements TableTransformFactory {


    @Override
    public String factoryIdentifier() {
        return MapExpandTransform.PLUGIN_NAME;
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder().required(MapExpandTransformConfig.INPUT_FIELD_NAME).build();
    }

    @Override
    public TableTransform createTransform(TableTransformFactoryContext context) {
        return () -> new MapExpandMultiCatalogTransform(context.getCatalogTables(), context.getOptions());
    }
}