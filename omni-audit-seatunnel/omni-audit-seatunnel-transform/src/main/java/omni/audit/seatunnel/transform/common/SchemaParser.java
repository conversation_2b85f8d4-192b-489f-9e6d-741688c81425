package omni.audit.seatunnel.transform.common;

import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.Column;
import org.apache.seatunnel.api.table.catalog.PhysicalColumn;
import org.apache.seatunnel.api.table.catalog.SeaTunnelDataTypeConvertorUtil;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.common.utils.JsonUtils;
import org.apache.seatunnel.shade.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.seatunnel.shade.com.fasterxml.jackson.databind.JsonNode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @class SchemaParser
 * @created 2025/5/20 16:43
 * @desc
 **/
public class SchemaParser implements Serializable {

    public TableSchema parse(ReadonlyConfig readonlyConfig, String schemaKey) {
        return parse(readonlyConfig.get(Options.key(schemaKey)
                                               .type(new TypeReference<Map<String, String>>() {})
                                               .noDefaultValue()
                                               .withDescription("SeaTunnel Schema Fields")));
    }

    public TableSchema parse(Map<String, String> schema) {
        JsonNode jsonNode = JsonUtils.toJsonNode(schema);
        Map<String, String> fieldsMap = JsonUtils.toStringMap(jsonNode);
        int fieldsNum = fieldsMap.size();
        List<Column> columns = new ArrayList<>(fieldsNum);
        for (Map.Entry<String, String> entry : fieldsMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            SeaTunnelDataType<?> dataType =
                    SeaTunnelDataTypeConvertorUtil.deserializeSeaTunnelDataType(key, value);
            PhysicalColumn column =
                    PhysicalColumn.of(key, dataType, null, null, true, null, null);
            columns.add(column);
        }
        TableSchema.Builder tableSchemaBuilder = TableSchema.builder();
        tableSchemaBuilder.columns(columns);
        return tableSchemaBuilder.build();
    }
}
