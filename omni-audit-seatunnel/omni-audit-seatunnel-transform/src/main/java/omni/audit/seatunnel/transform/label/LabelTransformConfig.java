package omni.audit.seatunnel.transform.label;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.shade.com.fasterxml.jackson.core.type.TypeReference;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LabelTransformConfig implements Serializable {
    public static final Option<List<Map<String, String>>> LABEL_PARAM =
            Options.key("label")
                    .type(new TypeReference<List<Map<String, String>>>() {})
                    .noDefaultValue()
                    .withDescription("label param");

    public static final Option<List<Map<String, Object>>> MAP_PARAM =
            Options.key("table.map")
                    .type(new TypeReference<List<Map<String, Object>>>() {})
                    .noDefaultValue()
                    .withDescription("label map param");
}
