package omni.audit.seatunnel.transform.insert;

import lombok.NonNull;
import omni.audit.seatunnel.transform.common.SchemaParser;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.transform.common.AbstractCatalogSupportFlatMapTransform;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

public class InsertTransform extends AbstractCatalogSupportFlatMapTransform {
    public static final String PLUGIN_NAME = "Insert";
    private TableSchema outputSchema;
    private boolean first;
    private Integer fieldNameIndex;
    private Map<String,Integer> fieldNameToIndexMap=new HashMap<>();
    List<Map<String, String>> maps = new ArrayList<>();


    public InsertTransform(@NonNull CatalogTable inputCatalogTable, ReadonlyConfig readonlyConfig) {
        super(inputCatalogTable);
        String inputFieldName = readonlyConfig.get(InsertTransformConfig.INPUT_FIELD_NAME);
        String string = readonlyConfig.get(InsertTransformConfig.FIRST);
        if (string.equals("true")){
            first = true;
        }

        List<Map<String, String>> maps = readonlyConfig.get(InsertTransformConfig.CONVERT_FIELD);
        this.maps = maps;
        Map<String, String> schemeMap = new LinkedHashMap<>();
        for (Map<String, String> map : maps) {
            schemeMap.put(map.get("sqlName"), map.get("sqlType"));
        }

        SchemaParser schemaParser = new SchemaParser();
        outputSchema=schemaParser.parse(schemeMap);
        SeaTunnelRowType seaTunnelRowType = inputCatalogTable.getSeaTunnelRowType();
        for (int i = 0; i < seaTunnelRowType.getFieldNames().length; i++) {
            if (!first){
                if (inputFieldName.trim().equals(seaTunnelRowType.getFieldNames()[i])) {
                    fieldNameIndex = i;
                    Map<String, String> map = readonlyConfig.get(InsertTransformConfig.TABLE_FIELD);
                    TableSchema schema = new SchemaParser().parse(map);
                    for (int j = 0; j < schema.getFieldNames().length; j++) {
                        fieldNameToIndexMap.put(schema.getFieldNames()[j],j);
                    }
                }
            }else {
                fieldNameToIndexMap.put(seaTunnelRowType.getFieldNames()[i],i);
            }
        }


    }


    @Override
    protected List<SeaTunnelRow> transformRow(SeaTunnelRow seaTunnelRow) {
        List<SeaTunnelRow> seaTunnelRows = new ArrayList<>();
        if (!first){
            Object field = seaTunnelRow.getField(fieldNameIndex);
            if (field instanceof Map){
                Map<Integer,Object> fieldMap = (Map<Integer, Object>) field;
                for (Object value : fieldMap.values()) {
                    if (value instanceof SeaTunnelRow){
                        SeaTunnelRow eventFieldMap = (SeaTunnelRow) value;
                        addSeaTunnelRows(seaTunnelRows,eventFieldMap);
                    }
                }
            }
        }else {
            addSeaTunnelRows(seaTunnelRows,seaTunnelRow);
        }
        return seaTunnelRows;
    }

    private void addSeaTunnelRows(List<SeaTunnelRow> seaTunnelRows, SeaTunnelRow seaTunnelRow) {
        SeaTunnelRow seaTunnelRow1 = new SeaTunnelRow(maps.size());
        for (int i = 0; i < maps.size(); i++) {
            Map<String, String> map = maps.get(i);
            String eventName = map.get("eventName");
            if (fieldNameToIndexMap.containsKey(eventName)) {
                Integer index = fieldNameToIndexMap.get(eventName);
                Object field = seaTunnelRow.getField(index);
                Object object = convertFieldName(map.get("sqlType"), map.get("eventType"), field);
                seaTunnelRow1.setField(i,object);
            }else {
                seaTunnelRow1.setField(i,null);
            }
        }
        seaTunnelRows.add(seaTunnelRow1);
    }

    /**
     * 将long时间戳转换为PostgreSQL TIMESTAMPTZ
     * @param millis 毫秒时间戳
     * @param zoneId 目标时区
     * @return java.sql.Timestamp对象
     */
    public static Timestamp convertToTimestampTz(long millis, String zoneId) {
        Instant instant = Instant.ofEpochMilli(millis);
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.of(zoneId));
        return Timestamp.from(zonedDateTime.toInstant());
    }

    private Object convertFieldName(String sqlType,String eventType, Object field) {
        if (sqlType == null || sqlType.isEmpty() || eventType == null || eventType.isEmpty() || field == null) {
            return field;
        }
        return field;
    }


    @Override
    protected TableSchema transformTableSchema() {
        return outputSchema;
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }
}

