package omni.audit.seatunnel.transform.sample.storage;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.transform.SeaTunnelTransform;
import org.apache.seatunnel.transform.common.AbstractMultiCatalogMapTransform;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:37
 * @description:
 **/
public class SamplingStorageMultiCatalogTransform extends AbstractMultiCatalogMapTransform {

    public SamplingStorageMultiCatalogTransform(List<CatalogTable> inputCatalogTables, ReadonlyConfig config) {
        super(inputCatalogTables, config);
    }

    @Override
    protected SeaTunnelTransform<SeaTunnelRow> buildTransform(CatalogTable catalogTable, ReadonlyConfig readonlyConfig) {
        return new SamplingStorageTransform(SamplingStorageTransformConfig.of(readonlyConfig),catalogTable);
    }

    @Override
    public String getPluginName() {
        return SamplingStorageTransform.PLUGIN_NAME;
    }
}