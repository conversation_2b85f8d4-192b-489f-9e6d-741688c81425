package omni.audit.seatunnel.transform.common.mapExpand;

import lombok.Getter;
import lombok.Setter;
import omni.audit.seatunnel.transform.weakness.WeaknessTransformConfig;
import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;

/**
 * <AUTHOR>
 * create at 2025/5/26 20:11
 * @description:
 **/
@Getter
@Setter
public class MapExpandTransformConfig implements Serializable {

    public static final Option<String> INPUT_FIELD_NAME =
            Options.key("inputFieldName")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "inputFieldName");

    public static final Option<Map<String, String>> OUTPUT_SCHEME =
            Options.key("output_scheme")
                    .mapType()
                    .noDefaultValue()
                    .withDescription(
                            "output table scheme info");


    private LinkedHashMap<String, String> outputScheme;
    private String inputFieldName;


    public static MapExpandTransformConfig of(ReadonlyConfig config) {
        MapExpandTransformConfig mapExpandTransformConfig = new MapExpandTransformConfig();

        Optional<String> o1 = config.getOptional(INPUT_FIELD_NAME);
        o1.ifPresent(mapExpandTransformConfig::setInputFieldName);

        LinkedHashMap<String, String> outputScheme = new LinkedHashMap<>();
        Optional<Map<String, String>> o2 = config.getOptional(OUTPUT_SCHEME);
        o2.ifPresent(outputScheme::putAll);

        mapExpandTransformConfig.setOutputScheme(outputScheme);
        return mapExpandTransformConfig;
    }


}