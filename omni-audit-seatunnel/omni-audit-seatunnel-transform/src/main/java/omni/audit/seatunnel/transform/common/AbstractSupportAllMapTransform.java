package omni.audit.seatunnel.transform.common;

import lombok.NonNull;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.transform.common.AbstractCatalogSupportMapTransform;

import java.io.Serializable;
import java.util.*;

public abstract class AbstractSupportAllMapTransform extends AbstractCatalogSupportMapTransform implements Serializable {
    private List<String> fieldNames;
    private final Map<String,Map<Integer,String>> fieldNameToIndexMap = new HashMap<>();

    public AbstractSupportAllMapTransform(@NonNull CatalogTable catalogTable, List<Map<String, Object>> tableMap ) {
        super(catalogTable);

        SeaTunnelRowType seaTunnelRowType = catalogTable.getTableSchema().toPhysicalRowDataType();
        List<String> fieldNames = new ArrayList<>(Arrays.asList(seaTunnelRowType.getFieldNames()));
        this.fieldNames = fieldNames;

        for (Map<String, Object> stringObjectMap : tableMap) {
            String eventKey = (String)stringObjectMap.get("eventKey");
            Map<String,String> eventSchema = (Map<String,String>)stringObjectMap.get("eventSchema");
            TableSchema schema = new SchemaParser().parse(eventSchema);
            String[] schemaFieldNames = schema.getFieldNames();
            Map<Integer,String> map = new HashMap<>();
            for (int i = 0; i < schemaFieldNames.length; i++) {
                map.put( i, schemaFieldNames[i]);
            }
            fieldNameToIndexMap.put(eventKey, map);
        }
    }

    public Map<String,Object> convertRowToMap(SeaTunnelRow seaTunnelRow){
        Map<String,Object> eventMap = new HashMap<>();

        for (int i = 0; i < fieldNames.size(); i++) {
            Object field = seaTunnelRow.getField(i);
            String fieldName = fieldNames.get(i);
            if (field == null){
                eventMap.put(fieldName, null);
                continue;
            }
            if (fieldNameToIndexMap.containsKey(fieldName)){
                Map<Integer, String> integerStringMap = fieldNameToIndexMap.get(fieldName);
                Map<Integer, Map<String, Object>> tableMap = getIntegerMapMap((Map<Integer, Object>) field, integerStringMap);

                eventMap.put(fieldName, tableMap);
            }else {
                eventMap.put(fieldName, field);
            }
        }
        return eventMap;
    }

    public SeaTunnelRow convertMapToRow(SeaTunnelRow seaTunnelRow,Map<String,Object> eventMap){
        for (int i = 0; i < fieldNames.size(); i++) {
            String fieldName = fieldNames.get(i);
            Object object = eventMap.get(fieldName);
            if (object == null){
                seaTunnelRow.setField(i, null);
                continue;
            }
            if (fieldNameToIndexMap.containsKey(fieldName)){
                Map<Integer, String> integerStringMap = fieldNameToIndexMap.get(fieldName);
                Map<Integer, Object> oldMap = (Map<Integer, Object>) object;
                Map<Integer, SeaTunnelRow> newMap = new HashMap<>();
                oldMap.forEach((key, value) -> {
                    Object oldObject = oldMap.get(key);
                    Object[] objects = new Object[integerStringMap.size()];
                    integerStringMap.keySet().stream().sorted().forEach(
                            sortKey -> {
                                objects[sortKey] = ((Map<String,Object>)oldObject).get(integerStringMap.get(sortKey));
                            }
                    );
                    newMap.put(key, new SeaTunnelRow(objects));

                });
                seaTunnelRow.setField(i, newMap);
            }else {
                seaTunnelRow.setField(i, object);
            }
        }
        return seaTunnelRow;
    }

    private static Map<Integer, Map<String, Object>> getIntegerMapMap(Map<Integer, Object> field, Map<Integer, String> integerStringMap) {
        Map<Integer,Map<String,Object>> tableMap = new HashMap<>();
        Map<Integer,Object> fieldMap = field;
        fieldMap.forEach((key, value) -> {
            Map<String,Object> map = new HashMap<>();
            SeaTunnelRow seaTunnelRowField = (SeaTunnelRow)value;
            integerStringMap.forEach((key1, value1) -> {
                Object field1 = seaTunnelRowField.getField(key1);
                map.put(value1,field1);
            });
            tableMap.put(key, map);
        });
        return tableMap;
    }
}
