package omni.audit.seatunnel.transform.common.mapExpand;

import lombok.NonNull;
import omni.audit.seatunnel.transform.common.SchemaParser;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.transform.common.AbstractCatalogSupportFlatMapTransform;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2025/5/26 20:08
 * @description:
 **/
public class MapExpandTransform extends AbstractCatalogSupportFlatMapTransform {

    public static final String PLUGIN_NAME = "MapExpand";
    private  Map<String, Integer> sourcefieldNameToIndexMap;
    private TableSchema outputSchema;
    private String inputFieldName;

    private final MapExpandTransformConfig mapExpandTransformConfig;

    public MapExpandTransform(MapExpandTransformConfig mapExpandTransformConfig, @NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
        this.mapExpandTransformConfig = mapExpandTransformConfig;
        SchemaParser schemaParser = new SchemaParser();
        outputSchema=schemaParser.parse(mapExpandTransformConfig.getOutputScheme());
        inputFieldName=mapExpandTransformConfig.getInputFieldName();

        sourcefieldNameToIndexMap=new HashMap<>();
        TableSchema schema = inputCatalogTable.getTableSchema();
        String[] fieldNames = schema.getFieldNames();
        for (int i = 0; i < fieldNames.length; i++) {
            sourcefieldNameToIndexMap.put(fieldNames[i], i);
        }
    }


    @Override
    protected List<SeaTunnelRow> transformRow(SeaTunnelRow seaTunnelRow) {
        List<SeaTunnelRow> seaTunnelRows=new ArrayList<>();
        Integer index = sourcefieldNameToIndexMap.get(inputFieldName);
        if(index!=null&&seaTunnelRow.getField(index)!=null){
            Map<Integer,SeaTunnelRow> map = (Map<Integer, SeaTunnelRow>) seaTunnelRow.getField(index);
           return map.isEmpty()?seaTunnelRows:map.values().stream().collect(Collectors.toList());
        }
        return seaTunnelRows;
    }

    @Override
    protected TableSchema transformTableSchema() {
        return outputSchema;
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }
}