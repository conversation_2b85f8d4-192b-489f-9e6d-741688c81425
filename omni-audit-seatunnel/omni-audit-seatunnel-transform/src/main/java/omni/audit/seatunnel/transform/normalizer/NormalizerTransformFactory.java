package omni.audit.seatunnel.transform.normalizer;

import com.google.auto.service.AutoService;
import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.connector.TableTransform;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableTransformFactory;
import org.apache.seatunnel.api.table.factory.TableTransformFactoryContext;
import org.apache.seatunnel.transform.common.TransformCommonOptions;

/**
 * <AUTHOR>
 * @class NormalizerTransform
 * @created 2025/5/19 10:49
 * @desc
 **/
@AutoService(Factory.class)
public class NormalizerTransformFactory implements TableTransformFactory {
    @Override
    public String factoryIdentifier() {
        return NormalizerTransform.PLUGIN_NAME;
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder()
                         .bundled(NormalizerTransformConfig.FIELDS)
                         .bundled(NormalizerTransformConfig.OUTPUT_SCHEMA)
                         .optional(TransformCommonOptions.MULTI_TABLES)
                         .optional(TransformCommonOptions.TABLE_MATCH_REGEX)
                         .build();
    }

    @Override
    public TableTransform createTransform(TableTransformFactoryContext context) {
        return () -> new NormalizerMultiCatalogTransform(context.getCatalogTables(), context.getOptions());
    }
}
