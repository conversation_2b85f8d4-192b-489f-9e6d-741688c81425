package omni.audit.seatunnel.transform.normalizer;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.transform.SeaTunnelTransform;
import org.apache.seatunnel.transform.common.AbstractMultiCatalogMapTransform;

import java.util.List;

/**
 * <AUTHOR>
 * @class NormalizerTransform
 * @created 2025/5/19 10:49
 * @desc
 **/
public class NormalizerMultiCatalogTransform extends AbstractMultiCatalogMapTransform {

    public NormalizerMultiCatalogTransform(List<CatalogTable> inputCatalogTables, ReadonlyConfig config) {
        super(inputCatalogTables, config);
    }

    @Override
    protected SeaTunnelTransform<SeaTunnelRow> buildTransform(CatalogTable catalogTable, ReadonlyConfig readonlyConfig) {
        return new NormalizerTransform(NormalizerTransformConfig.of(readonlyConfig), catalogTable);
    }

    @Override
    public String getPluginName() {
        return NormalizerTransform.PLUGIN_NAME;
    }
}
