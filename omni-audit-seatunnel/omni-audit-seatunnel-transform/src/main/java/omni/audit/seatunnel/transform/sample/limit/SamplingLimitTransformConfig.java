package omni.audit.seatunnel.transform.sample.limit;

import com.quanzhi.omniaudit.sampling.manager.CacheManager;
import com.quanzhi.omniaudit.sampling.manager.ICacheManager;
import com.quanzhi.omniaudit.sampling.moudle.SamplingConfig;
import com.quanzhi.omniaudit.sampling.service.ISamplingService;
import com.quanzhi.omniaudit.sampling.service.strategy.MemorySamplingStrategy;
import lombok.Getter;
import lombok.Setter;
import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:32
 * @description:
 **/
@Getter
@Setter
public class SamplingLimitTransformConfig implements Serializable {

    public static final Option<List<String>> SAMPLING_KEYS=
            Options.key("sampling_keys")
                    .listType()
                    .noDefaultValue()
                    .withDescription("Which fields to sample from");

    private List<String> samplingKeys;

    public static SamplingLimitTransformConfig of(ReadonlyConfig config) {
        List<String> keys=new ArrayList<>();
        Optional<List<String>> optional = config.getOptional(SAMPLING_KEYS);
        if(optional.isPresent()){
            keys.addAll(config.get(SAMPLING_KEYS));
        }
        SamplingLimitTransformConfig samplingLimitTransformConfig = new SamplingLimitTransformConfig();
        samplingLimitTransformConfig.setSamplingKeys(keys);
        return samplingLimitTransformConfig;
    }

}