package omni.audit.seatunnel.transform.net;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;

import java.io.Serializable;
import java.util.Map;

public class NetworkSegmentTransformConfig implements Serializable {
    public static final Option<Map<String, String>>  NET_PARAM =
            Options.key("net")
                    .mapType()
                    .noDefaultValue()
                    .withDescription("label param");
}
