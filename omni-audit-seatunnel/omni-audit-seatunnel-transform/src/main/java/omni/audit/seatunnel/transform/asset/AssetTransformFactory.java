package omni.audit.seatunnel.transform.asset;

import com.google.auto.service.AutoService;
import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.connector.TableTransform;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableTransformFactory;
import org.apache.seatunnel.api.table.factory.TableTransformFactoryContext;

@AutoService(Factory.class)
public class AssetTransformFactory implements TableTransformFactory {
    @Override
    public TableTransform createTransform(TableTransformFactoryContext context) {
        return() -> new AssetMultiCatalogTransform(context.getCatalogTables(), context.getOptions());
    }

    @Override
    public String factoryIdentifier() {
        return AssetTransform.PLUGIN_NAME;
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder().build();
    }
}
