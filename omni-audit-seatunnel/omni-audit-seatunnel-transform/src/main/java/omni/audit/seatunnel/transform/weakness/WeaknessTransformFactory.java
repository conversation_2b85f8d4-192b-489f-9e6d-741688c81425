package omni.audit.seatunnel.transform.weakness;

import com.google.auto.service.AutoService;
import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.connector.TableTransform;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableTransformFactory;
import org.apache.seatunnel.api.table.factory.TableTransformFactoryContext;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:33
 * @description:
 **/
@AutoService(Factory.class)
public class WeaknessTransformFactory implements TableTransformFactory {


    @Override
    public String factoryIdentifier() {
        return WeaknessTransform.PLUGIN_NAME;
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder().build();
    }

    @Override
    public TableTransform createTransform(TableTransformFactoryContext context) {
        return () -> new WeaknessMultiCatalogTransform(context.getCatalogTables(), context.getOptions());
    }
}