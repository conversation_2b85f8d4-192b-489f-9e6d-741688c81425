schema {
  fields = {
    meta = {
      tm = bigint
      type = string
    }
    net = {
      src_ip = string
      src_port = int
      dst_ip = string
      dst_port = int
      flow_source = string
    }
    mac = {
      mac = string
    }
    unique_id = {
      event_id = string
    }
    req = {
      db_name = string
      db_user = string
      db_password = string
      sql = string
    }
    rsp = {
      status = int
      start_time = bigint
      close_time = bigint
      row_count = int
      result = string
    }
  }
}
