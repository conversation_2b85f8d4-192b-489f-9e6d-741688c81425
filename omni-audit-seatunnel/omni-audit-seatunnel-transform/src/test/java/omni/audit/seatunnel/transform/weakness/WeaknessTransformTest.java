package omni.audit.seatunnel.transform.weakness;


import com.quanzhi.audit.weakness.constant.WeaknessConstant;
import omni.audit.seatunnel.transform.common.SchemaParser;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TablePath;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.shade.com.typesafe.config.ConfigFactory;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.io.FileNotFoundException;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class WeaknessTransformTest {


    static CatalogTable catalogTable;
    static Object[] values;

    @Before
    public void setUp() throws FileNotFoundException, URISyntaxException {
        String configFile = "/conf/schema.conf";
        ReadonlyConfig readonlyConfig = ReadonlyConfig.fromConfig(ConfigFactory.parseFile(new File(getTestConfigFile(configFile))));
        SchemaParser parser = new SchemaParser();
        TableSchema tableSchema = parser.parse(readonlyConfig,"schema.fields");

        catalogTable=CatalogTable.of(TableIdentifier.of("catalog", TablePath.DEFAULT),
                tableSchema, new HashMap<>(),
                new ArrayList<>(),
                "comment");
        SeaTunnelRow inputRow = new SeaTunnelRow(values);
    }

    private String getTestConfigFile(String configFile)
            throws FileNotFoundException, URISyntaxException {
        URL resource = WeaknessTransformTest.class.getResource(configFile);
        if (resource == null) {
            throw new FileNotFoundException("Can't find config file: " + configFile);
        }
        return Paths.get(resource.toURI()).toString();
    }

    @Test
    public void testWeaknessTransform(){
        Map<String,Object> configMap=new HashMap<>();
        Map<String,Object> weaknessConfigMap=new HashMap<>();
        weaknessConfigMap.put(WeaknessConstant.WEAKNESS_RULE_TABLE,"weakness_rule");
        //weaknessConfigMap.put(WeaknessConstant.APPEND_FIELDS, Arrays.asList("srvAddress","srvName","dbName","dbType","dbVersion","dbBizSystem","accessDomains","deployDomains"));
        configMap.put(WeaknessTransformConfig.WEAKNESS_CONFIG.key(), weaknessConfigMap);

        WeaknessTransform weaknessTransform = new WeaknessTransform(WeaknessTransformConfig.of(ReadonlyConfig.fromMap(configMap)), catalogTable);

        Object[] values=new Object[]{};
        values[0]="1";
        values[1]="1";
        SeaTunnelRow seaTunnelRow=new SeaTunnelRow(values);

        weaknessTransform.transformRow(seaTunnelRow);
    }





}