package omni.audit.seatunnel.transform.normalizer;


import lombok.SneakyThrows;
import omni.audit.seatunnel.transform.common.SchemaParser;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.catalog.schema.ReadonlyConfigParser;
import org.apache.seatunnel.shade.com.typesafe.config.ConfigFactory;
import org.junit.Test;

import java.io.File;
import java.io.FileNotFoundException;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Paths;
import java.util.*;

import static omni.audit.seatunnel.transform.normalizer.NormalizerTransform.generatePathIndex;
import static omni.audit.seatunnel.transform.normalizer.NormalizerTransformConfig.FIELDS;

public class NormalizerTransformTest {

    @Test
    @SneakyThrows
    public void testConf() {

        String configFile = "/conf/kafka.conf";

        ReadonlyConfig config = ReadonlyConfig.fromConfig(ConfigFactory.parseFile(new File(getTestConfigFile(configFile))));
        SchemaParser parser = new SchemaParser();
        Optional<Map<String, String>> outputSchemaOption = config.getOptional(FIELDS);
        Map<String, String> outputSchema = new HashMap<>();
        if (outputSchemaOption.isPresent()) {
            outputSchema.putAll(config.get(FIELDS));
        }
        TableSchema tableSchema = parser.parse(outputSchema);
        tableSchema.copy().toPhysicalRowDataType();
        Map<String, Integer> map = new HashMap<>();
        generatePathIndex("", tableSchema.toPhysicalRowDataType(), map);
        for (Map.Entry<String, Integer> kv : map.entrySet()) {
            System.out.println(kv.getKey() + " = " + kv.getValue());
        }
        tableSchema.toString();
    }

    private String getTestConfigFile(String configFile)
            throws FileNotFoundException, URISyntaxException {
        URL resource = NormalizerTransformTest.class.getResource(configFile);
        if (resource == null) {
            throw new FileNotFoundException("Can't find config file: " + configFile);
        }
        return Paths.get(resource.toURI()).toString();
    }

    @SneakyThrows
    @Test
    public void name() {
        String configFile = "/conf/kafka.conf";

        ReadonlyConfig config = ReadonlyConfig.fromConfig(ConfigFactory.parseFile(new File(getTestConfigFile(configFile))));
        ReadonlyConfigParser parser = new ReadonlyConfigParser();
        TableSchema tableSchema = parser.parse(config);
        Map<String, Integer> map = new TreeMap<>();
        generatePathIndex("", tableSchema.toPhysicalRowDataType(), map);
        for (Map.Entry<String, Integer> kv : map.entrySet()) {
            System.out.println(kv.getKey() + " = " + kv.getValue());
        }
    }
}