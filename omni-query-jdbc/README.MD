## 简介
使用 commons-dbutils 工具进行 SQL 操作

## 使用
```java
QueryRunner queryRunner = new QueryRunner(DataSourceFactory.createDataSource(JdbcConfig.builder().username("").jdbcUrl("").password("").build()));
List<Map<String, Object>> maps = queryRunner.query("select * from dc_column", new MapListHandler());
```
- ResultSetHandler 有很多丰富的内置转换器，可以执行查阅
- 如果有一些 cursor 的复杂查询，请注意设置 StatementConfiguration ，否则可能将数据都返回
- 不要使用已经标记为废弃的 API