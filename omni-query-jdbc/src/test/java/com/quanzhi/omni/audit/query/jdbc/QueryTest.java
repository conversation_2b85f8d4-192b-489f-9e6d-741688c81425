//package com.quanzhi.omni.audit.query.jdbc;
//
//import org.apache.commons.dbutils.QueryRunner;
//import org.apache.commons.dbutils.handlers.MapListHandler;
//import org.junit.Test;
//
//import java.sql.SQLException;
//import java.util.List;
//import java.util.Map;
//
//public class QueryTest {
//    public void testQuery() throws SQLException {
//        QueryRunner queryRunner = new QueryRunner(DataSourceFactory.createDataSource(JdbcConfig.builder().username("").jdbcUrl("").password("").build()));
//        List<Map<String, Object>> maps = queryRunner.query("select * from dc_column", new MapListHandler());
//    }
//
//
//
//    @Test
//    public void test() throws SQLException {
//        JdbcConfig jdbcConfig = JdbcConfig.builder().username("postgres").jdbcUrl("jdbc:postgresql://*************:5432/postgres")
//                .password("password123")
//                .driverClassName("org.postgresql.Driver")
//                .build();
//        QueryRunner  queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
//        List<Map<String, Object>> maps = queryRunner.query("select * from weakness", new MapListHandler());
//        maps.forEach(System.out::println);
//    }
//
//    @Test
//    public void testInsert() throws SQLException {
//        JdbcConfig jdbcConfig = JdbcConfig.builder().username("postgres").jdbcUrl("jdbc:postgresql://*************:5432/postgres")
//                .password("password123")
//                .driverClassName("org.postgresql.Driver")
//                .build();
//        QueryRunner  queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
//        queryRunner.update("insert into weakness(asset_id,weakness_id,operation_id,name,type,level,state,db_name,db_type,db_version,svc_name,svc_address,visit_domains,deploy_domains,create_time,update_time,discover_time,active_time) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
//        List<Map<String, Object>> maps = queryRunner.query("select * from weakness", new MapListHandler());
//        maps.forEach(System.out::println);
//    }
//
//
//}
