package com.quanzhi.omni.audit.query.jdbc;

import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.Properties;

@Builder
@Getter
public class JdbcConfig implements Serializable {

    private String username;

    private String password;

    private String jdbcUrl;

    private String driverClassName;

    private String schema;

    private long connectionTimeout;

    private long validationTimeout;

    private long idleTimeout;

    private long leakDetectionThreshold;

    private long maxLifetime;

    private int maxPoolSize;

    private int minIdle;

    private Properties dataSourceProperties;

    private Properties poolProperties;

}
