package com.quanzhi.omni.audit.query.processor;

import com.quanzhi.omni.audit.query.utils.ColumnNameUtils;
import org.apache.commons.dbutils.BeanProcessor;

import java.beans.PropertyDescriptor;
import java.sql.Array;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * BeanProcessor implementation that converts snake_case column names to camelCase
 * before mapping them to bean properties.
 */
public class CamelCaseBeanProcessor extends BeanProcessor {

    /**
     * Override the default column to property mapping to convert snake_case column names
     * to camelCase before matching with bean properties.
     *
     * @param rsmd  ResultSetMetaData containing the column information
     * @param props Array of PropertyDescriptors for the bean
     * @return A Map of column names to PropertyDescriptors
     * @throws SQLException if a database access error occurs
     */
    @Override
    protected int[] mapColumnsToProperties(ResultSetMetaData rsmd, PropertyDescriptor[] props) throws SQLException {
        int cols = rsmd.getColumnCount();
        int[] columnToProperty = new int[cols + 1];
        Arrays.fill(columnToProperty, PROPERTY_NOT_FOUND);

        // 创建属性名到属性索引的映射
        Map<String, Integer> propertyMap = new HashMap<>();
        for (int i = 0; i < props.length; i++) {
            propertyMap.put(props[i].getName().toLowerCase(), i);
        }

        // 遍历所有列，将列名转换为camelCase，然后查找匹配的属性
        for (int col = 1; col <= cols; col++) {
            String columnName = rsmd.getColumnLabel(col);
            if (columnName == null || columnName.isEmpty()) {
                columnName = rsmd.getColumnName(col);
            }

            // 将snake_case转换为camelCase
            String camelCaseColumnName = ColumnNameUtils.snakeToCamelCase(columnName);

            // 查找匹配的属性
            Integer propertyIndex = propertyMap.get(camelCaseColumnName.toLowerCase());
            if (propertyIndex != null) {
                columnToProperty[col] = propertyIndex;
            }
        }

        return columnToProperty;
    }

    /**
     * 处理特殊类型的列，例如数组、JSON等
     * 这里保留了CustomBeanProcessor中的数组处理逻辑
     *
     * @param rs       ResultSet to process
     * @param index    Index of the column to process
     * @param propType Java type of the property
     * @return The processed column value
     * @throws SQLException if a database access error occurs
     */
    @Override
    protected Object processColumn(ResultSet rs, int index, Class<?> propType) throws SQLException {
        if (propType.equals(List.class)) {
            Array array = rs.getArray(index);
            if (array == null) {
                return new ArrayList<>(); // 返回空列表，避免 null
            }
            Object[] objects = (Object[]) array.getArray();
            return Arrays.asList(objects)
                         .stream()
                         .map(Object::toString)
                         .collect(Collectors.toList());
        }
        return super.processColumn(rs, index, propType);
    }

}
