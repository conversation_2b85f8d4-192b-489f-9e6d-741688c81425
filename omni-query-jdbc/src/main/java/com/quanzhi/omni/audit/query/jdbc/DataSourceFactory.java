package com.quanzhi.omni.audit.query.jdbc;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DataSourceFactory {

    private static final Map<String, DataSource> DATA_SOURCE_MAP = new ConcurrentHashMap<>();

    public static DataSource createDataSource(JdbcConfig config) {
        valid(config);
        DataSource dataSource = DATA_SOURCE_MAP.get(config.getJdbcUrl());
        if (dataSource == null) {
            synchronized (DATA_SOURCE_MAP) {
                dataSource = DATA_SOURCE_MAP.get(config.getJdbcUrl());
                if (dataSource == null) {
                    dataSource = getDataSource(config);
                    DATA_SOURCE_MAP.put(config.getJdbcUrl(), dataSource);
                }
            }
        }
        return dataSource;
    }

    private static void valid(JdbcConfig config) {
        if (config.getJdbcUrl() == null || config.getJdbcUrl().isEmpty()) {
            throw new IllegalArgumentException("jdbc url must not empty");
        }
        if (config.getDriverClassName() == null || config.getDriverClassName().isEmpty()) {
            throw new IllegalArgumentException("driverClassName must not empty");
        }
        try {
            Class.forName(config.getDriverClassName());
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    private static DataSource getDataSource(JdbcConfig config) {
        HikariConfig hikariConfig;
        if (config.getPoolProperties() == null) {
            hikariConfig = new HikariConfig();
        } else {
            hikariConfig = new HikariConfig(config.getPoolProperties());
        }
        hikariConfig.setDriverClassName(config.getDriverClassName());
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        hikariConfig.setJdbcUrl(config.getJdbcUrl());
        if (config.getConnectionTimeout() > 0) {
            hikariConfig.setConnectionTimeout(config.getConnectionTimeout());
        } else {
            hikariConfig.setConnectionTimeout(30000);
        }
        if (config.getIdleTimeout() > 0) {
            hikariConfig.setIdleTimeout(config.getIdleTimeout());
        } else {
            hikariConfig.setIdleTimeout(600000);
        }
        if (config.getMaxLifetime() > 0) {
            hikariConfig.setMaxLifetime(config.getMaxLifetime());
        } else {
            hikariConfig.setMaxLifetime(1800000);
        }
        if (config.getMaxPoolSize() > 0) {
            hikariConfig.setMaximumPoolSize(config.getMaxPoolSize());
        } else {
            hikariConfig.setMaximumPoolSize(32);
        }
        if (config.getMinIdle() > 0) {
            hikariConfig.setMinimumIdle(config.getMinIdle());
        } else {
            hikariConfig.setMinimumIdle(10);
        }
        if (config.getValidationTimeout() > 0) {
            hikariConfig.setValidationTimeout(config.getValidationTimeout());
        } else {
            hikariConfig.setValidationTimeout(5000);
        }
        if (config.getLeakDetectionThreshold() > 0) {
            hikariConfig.setLeakDetectionThreshold(config.getLeakDetectionThreshold());
        }
        if (config.getDataSourceProperties() != null) {
            hikariConfig.setDataSourceProperties(config.getDataSourceProperties());
        }
        if (config.getSchema() != null) {
            hikariConfig.setSchema(config.getSchema());
        }

        return new HikariDataSource(hikariConfig);
    }

    public static void close() {
        synchronized (DATA_SOURCE_MAP) {
            DATA_SOURCE_MAP.forEach((name, dataSource) -> {
                if (dataSource instanceof HikariDataSource) {
                    ((HikariDataSource) dataSource).close();
                }
            });
            DATA_SOURCE_MAP.clear();
        }
    }

}
