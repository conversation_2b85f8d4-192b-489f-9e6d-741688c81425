package com.quanzhi.omni.audit.query.handler;

import com.quanzhi.omni.audit.query.utils.ColumnNameUtils;
import org.apache.commons.dbutils.ResultSetHandler;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * A ResultSetHandler implementation that converts ResultSet rows into a List of Maps.
 * The column names in the maps are converted from snake_case to camelCase.
 */
public class CamelCaseMapListHandler implements ResultSetHandler<List<Map<String, Object>>> {

    /**
     * Converts the ResultSet rows into a List of Maps with camelCase keys.
     *
     * @param rs The ResultSet to process
     * @return A List of Maps with camelCase keys, or an empty list if there are no rows in the ResultSet
     * @throws SQLException if a database access error occurs
     */
    @Override
    public List<Map<String, Object>> handle(ResultSet rs) throws SQLException {
        List<Map<String, Object>> rows = new ArrayList<>();
        while (rs.next()) {
            rows.add(convertRowToMap(rs));
        }
        return rows;
    }

    /**
     * Converts the current row in the ResultSet into a Map with camelCase keys.
     *
     * @param rs The ResultSet to process
     * @return A Map with camelCase keys
     * @throws SQLException if a database access error occurs
     */
    protected Map<String, Object> convertRowToMap(ResultSet rs) throws SQLException {
        Map<String, Object> result = new HashMap<>();
        ResultSetMetaData rsmd = rs.getMetaData();
        int cols = rsmd.getColumnCount();

        for (int i = 1; i <= cols; i++) {
            String columnName = rsmd.getColumnLabel(i);
            if (columnName == null || columnName.isEmpty()) {
                columnName = rsmd.getColumnName(i);
            }

            // Convert snake_case to camelCase
            String camelCaseColumnName = ColumnNameUtils.snakeToCamelCase(columnName);
            result.put(camelCaseColumnName, rs.getObject(i));
        }

        return result;
    }

}
