<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.quanzhi.omni.audit</groupId>
    <artifactId>omni-audit-parent</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <name>Omni Audit: Root</name>
    <modules>
        <module>omni-query-jdbc</module>
        <module>omni-audit-label</module>
        <module>omni-audit-asset</module>
        <module>omni-audit-network-segment</module>
        <module>omni-audit-weakness</module>
        <module>omni-audit-common</module>
        <module>omni-audit-filter</module>
        <module>omni-audit-sampling</module>
        <!-- 不再构建 seatunnel-->
        <!-- <module>omni-audit-seatunnel</module>-->
        <module>omni-audit-sqlparse</module>
        <module>omni-audit-apex</module>
        <module>omni-audit-handler</module>
        <module>omni-audit-datalabel</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jackson.version>2.19.1</jackson.version>
        <checkstyle.version>10.26.1</checkstyle.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>commons-dbutils</groupId>
                <artifactId>commons-dbutils</artifactId>
                <version>1.8.1</version>
            </dependency>
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>4.0.3</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.38</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.re</groupId>
                <artifactId>ruleEngine-core</artifactId>
                <version>3.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.re</groupId>
                <artifactId>ruleEngine-repository-pgsql</artifactId>
                <version>3.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>5.3.31</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.36</version>
            </dependency>
            <dependency>
                <groupId>com.google.auto.service</groupId>
                <artifactId>auto-service-annotations</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.auto.service</groupId>
                <artifactId>auto-service</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.18.0</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>33.4.8-jre</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.12.4</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>2.9.3</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>4.31.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>4.31.1</version>
            </dependency>
            <dependency>
                <groupId>com.github.os72</groupId>
                <artifactId>protoc-jar</artifactId>
                <version>3.11.4</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.20.0</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>5.13.4</version>
                <scope>test</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>3.3.1</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.puppycrawl.tools</groupId>
                            <artifactId>checkstyle</artifactId>
                            <version>${checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                    <executions>
                        <execution>
                            <id>validate</id>
                            <phase>compile</phase>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <configLocation>/tools/maven/quanzhi_checkstyle.xml</configLocation>
                        <includeTestSourceDirectory>false</includeTestSourceDirectory>
                        <logViolationsToConsole>true</logViolationsToConsole>
                        <failOnViolation>false</failOnViolation>
                    </configuration>
                </plugin>


                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.13.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.6.0</version>
                    <configuration>
                        <shadedArtifactAttached>false</shadedArtifactAttached>
                        <createDependencyReducedPom>true</createDependencyReducedPom>
                        <!-- Make sure the transitive dependencies are written to the generated pom under <dependencies> -->
                        <promoteTransitiveDependencies>true</promoteTransitiveDependencies>
                        <artifactSet>
                        </artifactSet>
                        <filters>
                            <filter>
                                <artifact>*:*</artifact>
                                <excludes>
                                    <exclude>META-INF/*.SF</exclude>
                                    <exclude>META-INF/*.DSA</exclude>
                                    <exclude>META-INF/*.RSA</exclude>
                                </excludes>
                            </filter>
                        </filters>
                    </configuration>

                    <executions>
                        <execution>
                            <goals>
                                <goal>shade</goal>
                            </goals>
                            <phase>package</phase>
                            <configuration>
                                <transformers combine.children="append">
                                    <!-- The service transformer is needed to merge META-INF/services files -->
                                    <transformer
                                            implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                                </transformers>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>

            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://192.168.0.92:8081/nexus/content/repositories/releases/</url>
        </repository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>public</id>
            <url>http://192.168.0.92:8081/nexus/content/groups/public/</url>
        </repository>
        <repository>
            <id>snapshots</id>
            <url>http://192.168.0.92:8081/nexus/content/repositories/snapshots/</url>
        </repository>

    </repositories>

</project>
