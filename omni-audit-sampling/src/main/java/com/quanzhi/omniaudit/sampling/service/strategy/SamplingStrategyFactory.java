// package com.quanzhi.omniaudit.sampling.service.strategy;
//
//
// import com.quanzhi.omniaudit.sampling.service.ISamplingService;
//
// import java.util.HashMap;
// import java.util.Map;
//
/// **
// * <AUTHOR>
// * create at 2025/4/29 11:24
// * @description:
// **/
//@Deprecated
// public class SamplingStrategyFactory {
//
//    private static final Map<String, ISamplingService> strategyMap = new HashMap<>();
//
//
//    static {
//        registerStrategy(ProbabilisticSamplingStrategy.class.getSimpleName(), new ProbabilisticSamplingStrategy());
//        registerStrategy(RedisSamplingStrategy.class.getSimpleName(), new RedisSamplingStrategy());
//        registerStrategy(MemorySamplingStrategy.class.getSimpleName(), new MemorySamplingStrategy());
//    }
//
//    public static void registerStrategy(String strategyName, ISamplingService strategy) {
//        strategyMap.put(strategyName, strategy);
//    }
//
//    public static ISamplingService getStrategy(String strategyName) {
//        return strategyMap.get(strategyName);
//    }
//
//
//
//
//
//
//}