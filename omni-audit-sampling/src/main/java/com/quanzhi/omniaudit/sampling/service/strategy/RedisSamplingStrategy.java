package com.quanzhi.omniaudit.sampling.service.strategy;

import com.quanzhi.omniaudit.sampling.client.JedisRedisClient;
import com.quanzhi.omniaudit.sampling.client.RedisClient;
import com.quanzhi.omniaudit.sampling.moudle.SamplingConfig;
import com.quanzhi.omniaudit.sampling.service.ISamplingService;
import omni.audit.common.client.redis.RedisConfig;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * create at 2025/4/23 20:18
 * @description: 基于redis的分布式采样策略
 **/
public class RedisSamplingStrategy implements ISamplingService {

    private RedisClient redisClient;

    private ProbabilisticSamplingStrategy probabilisticSamplingStrategy;

    private SamplingConfig samplingConfig;

    public static final int STEP_COUNT = (2 << 2) - 1;

    @Override
    public void init(SamplingConfig samplingConfig) {
        this.samplingConfig = samplingConfig;
        RedisConfig redisConfig = samplingConfig.getRedisConfig();
        this.redisClient = new JedisRedisClient(
                redisConfig.getHost(),
                redisConfig.getPort(),
                redisConfig.getTimeout(),
                redisConfig.getMaxTotal()
        );
        probabilisticSamplingStrategy = new ProbabilisticSamplingStrategy(samplingConfig.getProb());
    }

    @Override
    public boolean sampling(String key) {
        // 从 redis 拿数据
        AtomicInteger at = getKeyCount(key);
        if (at == null) {
            return true;
        }
        if (at.get() < samplingConfig.getBasisCount()) {
            at.incrementAndGet();
            // 如果达到了步长，进行更新
            increment(key, at);
            return true;
        } else if (at.get() < samplingConfig.getMaxCount()) {
            if (sample()) {
                at.incrementAndGet();
                increment(key, at);
                return true;
            }
        }
        return false;
    }

    private void increment(String key, AtomicInteger at) {
        if (isRemain(at.get())) {
            int s;
            try {
                s = getAndIncrementByRedis(key, STEP_COUNT + 1);
                if (s == samplingConfig.getMaxCount() || s == samplingConfig.getBasisCount()) {
                    at.set(s);
                }
            } catch (Exception ignored) {
            }
        }
    }

    private boolean isRemain(int count) {
        return (count & STEP_COUNT) == 0;
    }

    private AtomicInteger getKeyCount(String key) {
        int c = getAndIncrementByRedis(key, 0);
        if (c >= samplingConfig.getMaxCount()) {
            c = samplingConfig.getMaxCount();
        } else if (c > 0) {
            // 这里会将计数器的余数进行清零，避免少采样（会造成多采样），例如 redis 记录 20，那么这里会将计数器重置 16
            // 因为计数器是 STEP_COUNT 的步长进行更新的。
            c = c - (c & STEP_COUNT);
        }
        return new AtomicInteger(c);

    }

    private int getAndIncrementByRedis(String key, int delta) {
        Long incr = redisClient.incr(key);
        return Math.toIntExact(incr);
    }

    private boolean sample() {
        return probabilisticSamplingStrategy.sample();
    }

    @Override
    public void reset() {
        redisClient.flush();
    }

}