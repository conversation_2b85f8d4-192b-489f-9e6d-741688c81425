package com.quanzhi.omniaudit.sampling.moudle;

import lombok.Data;
import omni.audit.common.client.redis.RedisConfig;

import java.io.Serializable;

/**
 * <AUTHOR>
 * create at 2025/4/28 11:22
 * @description:
 **/
@Data
public class SamplingConfig implements Serializable {

    private float prob;

    private int basisCount;

    private int maxCount;

    private Long samplingCacheExpireMs;

    private Long samplingCacheMaximumSize;

    private RedisConfig redisConfig;

}