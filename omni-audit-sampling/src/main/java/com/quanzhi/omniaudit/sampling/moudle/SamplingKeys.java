package com.quanzhi.omniaudit.sampling.moudle;

import lombok.Data;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2025/4/28 15:15
 * @description:
 **/
@Data
public class SamplingKeys {

    private Map<String, Map<String, Integer>> dimensionMap = new HashMap<>();

    public Map<String, Map<String, Integer>> getDimensionMap() {
        return dimensionMap;
    }

    public SamplingKeys add(String dimensionType, Object key) {
        if (key == null) {
            return this;
        }
        Map<String, Integer> map = dimensionMap.get(dimensionType);
        if (map == null) {
            map = new HashMap<>();
            dimensionMap.put(dimensionType, map);
        }
        if (key instanceof String) {
            String keyStr = ((String) key).trim();
            sumUp(map, keyStr);
        }
        if (key instanceof Collection) {
            List<String> lists = (List<String>) key;
            for (String keyStr : lists) {
                sumUp(map, keyStr);
            }
        }
        return this;
    }

    private void sumUp(Map<String, Integer> map, String key) {
        Integer value = map.get(key);
        if (value == null) {
            value = 1;
        } else {
            value++;
        }
        map.put(key, value);
    }

    public int get(String dimensionType, String key) {
        Map<String, Integer> map = dimensionMap.get(dimensionType);
        if (map == null) {
            return 0;
        }
        Integer value = map.get(key);
        return value == null ? 0 : value;
    }

}