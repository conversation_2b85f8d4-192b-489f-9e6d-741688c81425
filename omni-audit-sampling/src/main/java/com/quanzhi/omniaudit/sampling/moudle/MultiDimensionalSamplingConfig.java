package com.quanzhi.omniaudit.sampling.moudle;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2025/4/28 11:24
 * @description:
 **/
@Data
public class MultiDimensionalSamplingConfig extends SamplingConfig {

    /**
     * 最小采样数
     */
    private int sampleMinCount = 4;

    /**
     * 总的样例数
     */
    private int sampleCount = 100;

    /**
     * 单维度最多
     */
    private int simpleKindCount = 20;

    /**
     * 单种样例最多
     */
    private int singleSimpleKindCount = 2;

    /**
     * 采样维度
     */
    private List<String> dimensionTypes;

}