package com.quanzhi.omniaudit.sampling.manager;

import com.github.benmanes.caffeine.cache.Cache;

public interface ICacheManager {

    <T, V> Cache<T, V> getOrCreateCache(String cacheName);

    <T, V> Cache<T, V> getOrCreateCache(String cacheName, long expireMils, long maximumSize);

    <T, V> Cache<T, V> getOrCreateCache(
            String cacheName,
            long expireMils,
            long maximumSize,
            CacheManager.CacheRemovalListener<T, V> listener
    );

    <T, V> Cache<T, V> getOrCreateCache(
            String cacheName,
            long expireMils,
            long maximumSize,
            ExpireType expireType,
            CacheManager.CacheRemovalListener<T, V> listener
    );

    <T, V> Cache<T, V> createCache(
            String cacheName,
            long expireMils,
            long maximumSize,
            ExpireType expireType,
            CacheManager.CacheRemovalListener<T, V> listener
    );

    void removeCache(String cacheName);

    void clearAllCaches();

    enum ExpireType {
        AFTER_ACCESS,
        AFTER_WRITE
    }

}
