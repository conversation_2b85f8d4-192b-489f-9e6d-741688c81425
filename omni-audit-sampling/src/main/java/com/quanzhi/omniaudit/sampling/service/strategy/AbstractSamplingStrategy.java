package com.quanzhi.omniaudit.sampling.service.strategy;

import com.github.benmanes.caffeine.cache.Cache;
import com.quanzhi.omniaudit.sampling.manager.ICacheManager;
import com.quanzhi.omniaudit.sampling.moudle.SamplingConfig;
import com.quanzhi.omniaudit.sampling.service.ISamplingService;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * create at 2025/5/19 13:12
 * @description:
 **/
public abstract class AbstractSamplingStrategy implements ISamplingService {

    public static final String CACHE_KEY = "samplingCache";

    public static final int DEFAULT_STANDARD_SIZE = 1024 * 1024 * 16;

    protected volatile Cache<String, AtomicInteger> sampleCache;

    private Long samplingCacheExpireMs;

    private Long samplingCacheMaximumSize;

    protected ProbabilisticSamplingStrategy probabilisticSamplingStrategy;

    private final ICacheManager cacheManager;

    public AbstractSamplingStrategy(ICacheManager cacheManager, SamplingConfig samplingConfig) {
        this.cacheManager = cacheManager;
        this.samplingCacheExpireMs = samplingConfig.getSamplingCacheExpireMs() == null ? 3 * 60 * 60 * 1000L : samplingConfig.getSamplingCacheExpireMs();
        this.samplingCacheMaximumSize = samplingConfig.getSamplingCacheMaximumSize() == null ? calculateSamplingMaximumSize() : samplingConfig.getSamplingCacheMaximumSize();
        this.sampleCache = creatCache();
        this.probabilisticSamplingStrategy = new ProbabilisticSamplingStrategy(samplingConfig.getProb());
    }

    private long calculateSamplingMaximumSize() {
        long maxMemory = Runtime.getRuntime().maxMemory();
        if (maxMemory >= DEFAULT_STANDARD_SIZE) {
            // 限制最大值
            return Math.min(1_000_000, maxMemory / (1024 * 1024));
        }
        // 更保守的估计
        return Math.min(500_000, maxMemory / (1024 * 1024) / 2);
    }

    private Cache<String, AtomicInteger> creatCache() {
        Cache<String, AtomicInteger> createCache = cacheManager.getOrCreateCache(CACHE_KEY,
                                                                                 samplingCacheExpireMs,
                                                                                 samplingCacheMaximumSize,
                                                                                 ICacheManager.ExpireType.AFTER_WRITE,
                                                                                 null);
        return createCache;
    }

    @Override
    public void init(SamplingConfig samplingConfig) {
    }

    @Override
    public void reset() {
        cacheManager.removeCache(CACHE_KEY);
        sampleCache = creatCache();
    }

    protected boolean probSample() {
        return probabilisticSamplingStrategy.sample();
    }

}