package com.quanzhi.omniaudit.sampling.service;

import com.quanzhi.omni.audit.query.handler.CamelCaseMapListHandler;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.quanzhi.omniaudit.sampling.moudle.MultiDimensionalSamplingConfig;
import lombok.extern.slf4j.Slf4j;
import omni.audit.common.util.DateUtil;
import org.apache.commons.dbutils.QueryRunner;

import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 * create at 2025/5/28 10:50
 * @description:
 **/
@Slf4j
public class SamplingStorageService {

    private final QueryRunner queryRunner;
    private final MultiDimensionalSamplingService multiDimensionalSamplingService;
    private final Properties samplingConfig;


    public SamplingStorageService(JdbcConfig jdbcConfig, MultiDimensionalSamplingConfig multiDimensionalSamplingConfig, Properties samplingConfig) {
        this.queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        this.multiDimensionalSamplingService = new MultiDimensionalSamplingService(multiDimensionalSamplingConfig);
        this.samplingConfig = samplingConfig;
    }


    public boolean checkSample(Map<String, Object> sourceMap, Map<String, Object> currentSample) {
        String sampleUniqueKey = samplingConfig.getProperty("sample_unique_key", "dbId");
        if (sampleUniqueKey == null) {
            //没配置，先默认采
            return true;
        }
        String sampleUniqueKeyValue = sourceMap.get(sampleUniqueKey)==null?null:sourceMap.get(sampleUniqueKey).toString();
        if(sampleUniqueKeyValue==null){
            //没配置，先默认采
            return true;
        }
        List<Map<String, Object>> dbSamples = findDbSamples(sampleUniqueKey, sampleUniqueKeyValue);

        boolean needSample = multiDimensionalSamplingService.checkSample(dbSamples, currentSample);
        return needSample;
    }

    private List<Map<String, Object>> findDbSamples(String sampleUniqueKey, String sampleUniqueKeyValue) {
        long timestamp = DateUtil.addDate(new Date(), Calendar.DATE, -7).getTime();
        String sql = "select * from sample where 1=1 and timestamp > "+timestamp+" and db_id = '"+sampleUniqueKeyValue+"' order by timestamp desc limit 100";
       // String sql = "select * from sample where 1=1  and "+sampleUniqueKey+" = '"+sampleUniqueKeyValue+"' order by timestamp desc limit 100";

        try {
            List<Map<String, Object>> query = queryRunner.query(sql, new CamelCaseMapListHandler());
            return query;
        } catch (SQLException e) {
            log.error("findDbSamples sql:{} error", sql, e);
        }
        return new ArrayList<>();
    }
}