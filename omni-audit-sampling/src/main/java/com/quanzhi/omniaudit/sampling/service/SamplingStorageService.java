package com.quanzhi.omniaudit.sampling.service;

import com.quanzhi.omni.audit.query.handler.CamelCaseMapListHandler;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.quanzhi.omniaudit.sampling.moudle.MultiDimensionalSamplingConfig;
import lombok.extern.slf4j.Slf4j;
import omni.audit.common.util.DateUtil;
import org.apache.commons.dbutils.QueryRunner;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * create at 2025/5/28 10:50
 * @description:
 **/
@Slf4j
public class SamplingStorageService {

    private static final String DEFAULT_UNIQUE_KEY = "dbId";
    private static final int DEFAULT_CACHE_EXPIRE_MINUTES = 30;
    private static final int DEFAULT_MAX_SAMPLES_PER_DB = 50;
    private static final int CACHE_CLEANUP_INTERVAL_MINUTES = 10;

    private final QueryRunner queryRunner;
    private final MultiDimensionalSamplingService multiDimensionalSamplingService;
    private final Properties samplingConfig;

    // 缓存：dbId -> 采样统计信息
    private final Map<String, SamplingStats> samplingStatsCache = new ConcurrentHashMap<>();
    // 缓存：dbId -> 最近采样列表
    private final Map<String, RecentSamples> recentSamplesCache = new ConcurrentHashMap<>();

    private final ScheduledExecutorService cacheCleanupExecutor;
    private final int maxSamplesPerDb;
    private final long cacheExpireMs;

    public SamplingStorageService(
            JdbcConfig jdbcConfig,
            MultiDimensionalSamplingConfig multiDimensionalSamplingConfig,
            Properties samplingConfig
    ) {
        this.queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        this.multiDimensionalSamplingService = new MultiDimensionalSamplingService(multiDimensionalSamplingConfig);
        this.samplingConfig = samplingConfig;

        this.maxSamplesPerDb = Integer.parseInt(
                samplingConfig.getProperty("max_samples_per_db", String.valueOf(DEFAULT_MAX_SAMPLES_PER_DB))
        );
        this.cacheExpireMs = Long.parseLong(
                samplingConfig.getProperty("cache_expire_minutes", String.valueOf(DEFAULT_CACHE_EXPIRE_MINUTES))
        ) * 60 * 1000;

        // 启动缓存清理任务
        this.cacheCleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "SamplingCache-Cleanup");
            t.setDaemon(true);
            return t;
        });

        cacheCleanupExecutor.scheduleAtFixedRate(
                this::cleanupExpiredCache,
                CACHE_CLEANUP_INTERVAL_MINUTES,
                CACHE_CLEANUP_INTERVAL_MINUTES,
                TimeUnit.MINUTES
        );
    }

    /**
     * 检查是否需要采样
     */
    public boolean checkSample(Map<String, Object> sourceMap, Map<String, Object> currentSample) {
        String sampleUniqueKey = samplingConfig.getProperty("sample_unique_key", DEFAULT_UNIQUE_KEY);
        String sampleUniqueKeyValue = extractUniqueKeyValue(sourceMap, sampleUniqueKey);

        if (sampleUniqueKeyValue == null) {
            log.debug("Sample unique key value is null, default to sample");
            return true;
        }

        try {
            // 1. 先检查基础采样统计
            SamplingStats stats = getSamplingStats(sampleUniqueKeyValue);
            if (shouldSampleBasedOnStats(stats)) {
                return true;
            }

            // 2. 再进行多维度采样检查
            List<Map<String, Object>> recentSamples = getRecentSamples(sampleUniqueKeyValue);
            boolean needSample = multiDimensionalSamplingService.checkSample(recentSamples, currentSample);

            // 3. 更新统计信息
            if (needSample) {
                updateSamplingStats(sampleUniqueKeyValue, currentSample);
            }

            return needSample;

        } catch (Exception e) {
            log.error("Error checking sample for key: {}", sampleUniqueKeyValue, e);
            // 出错时默认采样，避免数据丢失
            return true;
        }
    }

    /**
     * 基于统计信息的快速采样判断
     */
    private boolean shouldSampleBasedOnStats(SamplingStats stats) {
        long currentTime = System.currentTimeMillis();
        long timeWindow = 24 * 60 * 60 * 1000; // 24小时窗口

        // 如果24小时内采样数量已达上限，进行概率采样
        if (stats.getSampleCountInWindow(currentTime, timeWindow) >= maxSamplesPerDb) {
            // 使用递减概率采样
            double probability = 1.0 / (1 + stats.getSampleCountInWindow(currentTime, timeWindow) - maxSamplesPerDb);
            return Math.random() < probability;
        }

        return true;
    }

    /**
     * 获取采样统计信息
     */
    private SamplingStats getSamplingStats(String dbId) {
        SamplingStats stats = samplingStatsCache.get(dbId);
        if (stats == null || stats.isExpired(cacheExpireMs)) {
            stats = loadSamplingStatsFromDb(dbId);
            samplingStatsCache.put(dbId, stats);
        }
        return stats;
    }

    /**
     * 从数据库加载采样统计信息
     */
    private SamplingStats loadSamplingStatsFromDb(String dbId) {
        try {
            long timestamp24h = System.currentTimeMillis() - (24 * 60 * 60 * 1000);
            long timestamp7d = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000);

            String sql = " SELECT COUNT(*) as total_count, COUNT(CASE WHEN timestamp > ? THEN 1 END) as count_24h, COUNT(CASE WHEN timestamp > ? THEN 1 END) as count_7d, MAX(timestamp) as last_sample_time"
                    + " FROM oad_sample WHERE db_id = ?";
            List<Map<String, Object>> result = queryRunner.query(sql,
                    new CamelCaseMapListHandler(), timestamp24h, timestamp7d, dbId);
            if (!result.isEmpty()) {
                Map<String, Object> row = result.get(0);
                return new SamplingStats(
                        ((Number) row.get("totalCount")).longValue(),
                        ((Number) row.get("count24h")).longValue(),
                        ((Number) row.get("count7d")).longValue(),
                        ((Number) row.getOrDefault("lastSampleTime", 0L)).longValue(),
                        System.currentTimeMillis()
                );
            }
        } catch (SQLException e) {
            log.error("Failed to load sampling stats for dbId: {}", dbId, e);
        }

        return new SamplingStats(0, 0, 0, 0, System.currentTimeMillis());
    }

    /**
     * 获取最近的采样数据
     */
    private List<Map<String, Object>> getRecentSamples(String dbId) {
        RecentSamples cached = recentSamplesCache.get(dbId);
        if (cached == null || cached.isExpired(cacheExpireMs)) {
            cached = loadRecentSamplesFromDb(dbId);
            recentSamplesCache.put(dbId, cached);
        }
        return cached.getSamples();
    }

    /**
     * 从数据库加载最近的采样数据（优化查询）
     */
    private RecentSamples loadRecentSamplesFromDb(String dbId) {
        try {
            long timestamp = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000); // 7天内
            String sql = " SELECT * FROM oad_sample WHERE db_id = ? AND timestamp > ? ORDER BY timestamp DESC LIMIT ?";
            List<Map<String, Object>> samples = queryRunner.query(sql,
                    new CamelCaseMapListHandler(), dbId, timestamp, maxSamplesPerDb);

            return new RecentSamples(samples, System.currentTimeMillis());

        } catch (SQLException e) {
            log.error("Failed to load recent samples for dbId: {}", dbId, e);
            return new RecentSamples(new ArrayList<>(), System.currentTimeMillis());
        }
    }

    /**
     * 更新采样统计信息
     */
    private void updateSamplingStats(String dbId, Map<String, Object> sample) {
        SamplingStats stats = samplingStatsCache.get(dbId);
        if (stats != null) {
            stats.incrementSampleCount();
        }

        // 更新最近采样缓存
        RecentSamples recentSamples = recentSamplesCache.get(dbId);
        if (recentSamples != null) {
            recentSamples.addSample(sample, maxSamplesPerDb);
        }
    }

    /**
     * 提取唯一键值
     */
    private String extractUniqueKeyValue(Map<String, Object> sourceMap, String sampleUniqueKey) {
        Object value = sourceMap.get(sampleUniqueKey);
        return value != null ? value.toString() : null;
    }

    /**
     * 清理过期缓存
     */
    private void cleanupExpiredCache() {
        try {
            samplingStatsCache.entrySet().removeIf(entry ->
                    entry.getValue().isExpired(cacheExpireMs));

            recentSamplesCache.entrySet().removeIf(entry ->
                    entry.getValue().isExpired(cacheExpireMs));

            log.debug("Cache cleanup completed. Stats cache size: {}, Samples cache size: {}",
                    samplingStatsCache.size(), recentSamplesCache.size());

        } catch (Exception e) {
            log.error("Error during cache cleanup", e);
        }
    }

    /**
     * 关闭服务
     */
    public void close() {
        if (cacheCleanupExecutor != null && !cacheCleanupExecutor.isShutdown()) {
            cacheCleanupExecutor.shutdown();
            try {
                if (!cacheCleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cacheCleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cacheCleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        samplingStatsCache.clear();
        recentSamplesCache.clear();
    }

    /**
     * 采样统计信息
     */
    private static class SamplingStats {
        private volatile long totalCount;
        private volatile long count24h;
        private volatile long count7d;
        private volatile long lastSampleTime;
        private final long loadTime;

        public SamplingStats(long totalCount, long count24h, long count7d, long lastSampleTime, long loadTime) {
            this.totalCount = totalCount;
            this.count24h = count24h;
            this.count7d = count7d;
            this.lastSampleTime = lastSampleTime;
            this.loadTime = loadTime;
        }

        public long getSampleCountInWindow(long currentTime, long windowMs) {
            if (currentTime - lastSampleTime <= windowMs) {
                return windowMs <= 24 * 60 * 60 * 1000 ? count24h : count7d;
            }
            return 0;
        }

        public void incrementSampleCount() {
            totalCount++;
            count24h++;
            count7d++;
            lastSampleTime = System.currentTimeMillis();
        }

        public boolean isExpired(long expireMs) {
            return System.currentTimeMillis() - loadTime > expireMs;
        }
    }

    /**
     * 最近采样数据
     */
    private static class RecentSamples {
        private final List<Map<String, Object>> samples;
        private final long loadTime;

        public RecentSamples(List<Map<String, Object>> samples, long loadTime) {
            this.samples = new ArrayList<>(samples);
            this.loadTime = loadTime;
        }

        public List<Map<String, Object>> getSamples() {
            return new ArrayList<>(samples);
        }

        public void addSample(Map<String, Object> sample, int maxSize) {
            samples.add(0, sample); // 添加到开头
            if (samples.size() > maxSize) {
                samples.remove(samples.size() - 1); // 移除最旧的
            }
        }

        public boolean isExpired(long expireMs) {
            return System.currentTimeMillis() - loadTime > expireMs;
        }
    }
}