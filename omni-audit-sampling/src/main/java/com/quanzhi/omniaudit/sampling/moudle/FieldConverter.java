package com.quanzhi.omniaudit.sampling.moudle;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * create at 2025/4/29 00:26
 * @description:
 **/
public class FieldConverter<T> {

    private final Map<String, Function<Object, String>> fieldConverters = new HashMap<>();

    // 配置字段的转换函数
    public void configureConverter(String fieldName, Function<Object, String> converter) {
        fieldConverters.put(fieldName, converter);
    }

    public Function<Object, String> getConverter(String fieldName) {
        return fieldConverters.get(fieldName);
    }

}