package com.quanzhi.omniaudit.sampling.service;

import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.quanzhi.omniaudit.sampling.moudle.MultiDimensionalSamplingConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * create at 2025/5/28 11:02
 * @description:
 **/
public class SamplingStorageTest {


    private SamplingStorageService samplingStorageService;


    @BeforeEach
    public void setUp()  {
        JdbcConfig jdbcConfig = JdbcConfig.builder().username("postgres").jdbcUrl("*********************************************")
                .password("password123")
                .driverClassName("org.postgresql.Driver")
                .build();

        MultiDimensionalSamplingConfig multidimensionalSamplingConfig = new MultiDimensionalSamplingConfig();
        multidimensionalSamplingConfig.setSampleCount(100);
        multidimensionalSamplingConfig.setSimpleKindCount(20);
        multidimensionalSamplingConfig.setSingleSimpleKindCount(2);
        multidimensionalSamplingConfig.setDimensionTypes(Arrays.asList("reqDbName"));

        Properties sampleConfig=new Properties();
        sampleConfig.put("sample_unique_key","req_db_user");

        samplingStorageService=new SamplingStorageService(jdbcConfig,multidimensionalSamplingConfig,sampleConfig);

    }




    @Test
    public void testCheckSample() {
        Map<String,Object> stringObjectMap=new HashMap<>();
        stringObjectMap.put("reqDbUser","test");

        Map<String,Object> currentMap=new HashMap<>();
        stringObjectMap.put("reqDbUser","test");
        currentMap.put("reqDbUser","test2");
        currentMap.put("reqDbName","mysqlBb-test");

        boolean sample = samplingStorageService.checkSample(stringObjectMap, currentMap);
        System.out.println(sample);
    }







}