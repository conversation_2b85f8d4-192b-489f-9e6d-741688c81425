package com.quanzhi.omniaudit.sampling.service;



import com.quanzhi.omniaudit.sampling.manager.CacheManager;
import com.quanzhi.omniaudit.sampling.manager.ICacheManager;
import com.quanzhi.omniaudit.sampling.moudle.SamplingConfig;
import com.quanzhi.omniaudit.sampling.service.strategy.MemorySamplingStrategy;
import com.quanzhi.omniaudit.sampling.service.strategy.ProbabilisticSamplingStrategy;
import junit.framework.Assert;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 * create at 2025/4/29 14:04
 * @description:
 **/
public class SamplingStrategyTest {

    /**
     * 测试随机采样策略
     * 百分之20概率采样
     */
    @Test
    public void testProbabilisticSamplingStrategy() {
        ISamplingService strategy =new ProbabilisticSamplingStrategy();
        SamplingConfig samplingConfig = new SamplingConfig();
        samplingConfig.setProb(0.2f);
        strategy.init(samplingConfig);

        int c = 1000;
        int count = (int) (c * samplingConfig.getProb());
        int actual = 0;
        for (int i = 0; i < c; i++) {
            boolean s = strategy.sampling(null);
            if (s) {
                actual++;
            }
        }
        System.out.println("actual: " + actual + " count: " + count);
        Assert.assertTrue(Math.abs(actual - count) < 20);
    }

    private ISamplingService createMemorySamplingStrategy() {
        SamplingConfig samplingConfig = new SamplingConfig();
        samplingConfig.setBasisCount(16);
        samplingConfig.setMaxCount(32);
        samplingConfig.setProb(0.5f);
        samplingConfig.setSamplingCacheExpireMs(1*60*1000L);
        ICacheManager cacheManager=new CacheManager();
        ISamplingService strategy = new MemorySamplingStrategy(cacheManager, samplingConfig);
        strategy.init(samplingConfig);
        return strategy;
    }

    @Test
    public void testSamplingSmallScale() {
        ISamplingService memorySamplingStrategy = createMemorySamplingStrategy();
        // 小量级测试：循环 100 次
        int N = 100;
        int trueCount = 0;
        for (int i = 0; i < N; i++) {
            if (memorySamplingStrategy.sampling("testKey")) {
                trueCount++;
            }
        }
        // 预估采样数范围
        int expectedMin = 16 + 8 + (int) ((N - 32) * 0.4); // 下限：40% 的采样率
        int expectedMax = 16 + 8 + (int) ((N - 32) * 0.6); // 上限：60% 的采样率
        System.out.println("Small scale test - True count: " + trueCount);
        assertTrue(trueCount >= expectedMin && trueCount <= expectedMax,
                "Expected true count between " + expectedMin + " and " + expectedMax);
    }


    @Test
    public void testSamplingMediumScale() {
        ISamplingService memorySamplingStrategy = createMemorySamplingStrategy();
        // 中量级测试：循环 10,000 次
        int N = 10_000;
        int trueCount = 0;
        for (int i = 0; i < N; i++) {
            if (memorySamplingStrategy.sampling("testKey")) {
                trueCount++;
            }
        }
        // 预估采样数范围
        int expectedMin = 16 + 8 + (int) ((N - 32) * 0.4); // 下限：40% 的采样率
        int expectedMax = 16 + 8 + (int) ((N - 32) * 0.6); // 上限：60% 的采样率
        System.out.println("Medium scale test - True count: " + trueCount);
        assertTrue(trueCount >= expectedMin && trueCount <= expectedMax,
                "Expected true count between " + expectedMin + " and " + expectedMax);
    }

    @Test
    public void testSamplingLargeScale() {
        ISamplingService memorySamplingStrategy = createMemorySamplingStrategy();
        // 大量级测试：循环 1,000,000 次
        int N = 1_000_000;
        int trueCount = 0;
        for (int i = 0; i < N; i++) {
            if (memorySamplingStrategy.sampling("testKey")) {
                trueCount++;
            }
        }
        // 预估采样数范围
        int expectedMin = 16 + 8 + (int) ((N - 32) * 0.4); // 下限：40% 的采样率
        int expectedMax = 16 + 8 + (int) ((N - 32) * 0.6); // 上限：60% 的采样率
        System.out.println("Large scale test - True count: " + trueCount);
        assertTrue(trueCount >= expectedMin && trueCount <= expectedMax,
                "Expected true count between " + expectedMin + " and " + expectedMax);
    }


}