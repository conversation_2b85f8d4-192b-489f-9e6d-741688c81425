package com.quanzhi.omni.audit.datalabel.repository;

import com.alibaba.fastjson.JSON;
import com.quanzhi.omni.audit.datalabel.jdbc.CamelCaseJsonbBeanListHandler;
import com.quanzhi.omni.audit.datalabel.model.DataLabelPolicy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.ScalarHandler;
import org.postgresql.util.PGobject;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class DataLabelPolicyRepository {

    private final QueryRunner queryRunner;

    public DataLabelPolicyRepository(QueryRunner queryRunner) {
        this.queryRunner = queryRunner;
    }

    /**
     * 获取标签配置列表
     * 使用 CamelCaseJsonbBeanListHandler 处理器，可以同时：
     * 1. 将数据库中的下划线命名（如 dlp_policy）转换为驼峰命名（如 dlpPolicy）
     * 2. 将 jsonb 类型（如 dlp_policy）转换为对应的对象（如 DLPPolicy）
     *
     * @return 过滤配置列表，不会返回 null
     */
    public List<DataLabelPolicy> getAll() {
        try {
            // 使用新的 CamelCaseJsonbBeanListHandler 处理器
            CamelCaseJsonbBeanListHandler<DataLabelPolicy> handler =
                    new CamelCaseJsonbBeanListHandler<>(DataLabelPolicy.class);
            List<DataLabelPolicy> filterConfigModels = queryRunner.query("SELECT * FROM oad_data_label", handler);
            return filterConfigModels != null ? filterConfigModels : new ArrayList<>();
        } catch (Exception e) {
            log.error("query dataLabel config error:", e);
        }
        return new ArrayList<>();
    }

    public void save(DataLabelPolicy dataLabelPolicy) {
        try {
            PGobject dlpPolicyObject = new PGobject();
            dlpPolicyObject.setType("jsonb");
            dlpPolicyObject.setValue(JSON.toJSONString(dataLabelPolicy.getDlpPolicy()));
            Object[] params = {
                    dataLabelPolicy.getName(),
                    dataLabelPolicy.getType(),
                    dataLabelPolicy.getLevel(),
                    dataLabelPolicy.getDiscoverEnable(),
                    dataLabelPolicy.getDescription(),
                    dataLabelPolicy.getLocations(),
                    dataLabelPolicy.getCreateTime(),
                    dataLabelPolicy.getUpdateTime(),
                    dataLabelPolicy.getDelFlag(),
                    dlpPolicyObject
            };
            ScalarHandler<Long> rsh = new ScalarHandler<>();
            String sql = "INSERT INTO oad_data_label (name, type, level, discover_enable, description, locations, create_time, update_time, del_flag, dlp_policy) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            queryRunner.insert(sql, rsh, params);
        } catch (Exception e) {
            log.error("save dataLabel config error:", e);
        }
    }
}
