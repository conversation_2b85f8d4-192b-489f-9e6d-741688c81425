package com.quanzhi.omni.audit.datalabel.model;

import com.quanzhi.dlp4j.core.configuration.DLPPolicy;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class DataLabelPolicy implements Serializable {

    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 类型
     */
    private String type;

    /**
     * 识别位置：COLUMN-列，VALUE-值
     */
    private List<String> locations;

    /**
     * 识别状态
     */
    private Boolean discoverEnable;

    /**
     * 描述
     */
    private String description;

    /**
     * 标签策略
     */
    private DLPPolicy dlpPolicy;

    private Long createTime;

    private Long updateTime;

    private Boolean delFlag;
}
