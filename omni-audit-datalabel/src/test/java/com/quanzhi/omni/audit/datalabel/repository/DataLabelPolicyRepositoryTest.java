package com.quanzhi.omni.audit.datalabel.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quanzhi.omni.audit.datalabel.model.DataLabelPolicy;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import org.apache.commons.dbutils.QueryRunner;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

@RunWith(JUnit4.class)
public class DataLabelPolicyRepositoryTest {

    private DataLabelPolicyRepository dataLabelPolicyRepository;

    @Before
    public void setUp() {
        JdbcConfig jdbcConfig = JdbcConfig.builder().username("postgres").jdbcUrl("**********************************************")
                .password("uO0LpGbeQy2T0DWR")
                .driverClassName("org.postgresql.Driver")
                .build();
        QueryRunner queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        dataLabelPolicyRepository = new DataLabelPolicyRepository(queryRunner);
    }

    @Test
    public void getAll() {
        System.out.println(dataLabelPolicyRepository.getAll());
        System.out.println(dataLabelPolicyRepository.getAll().size());
    }

    @Test
    public void save() {
        List<DataLabelPolicy> dataLabelPolicies = getDataLabelPolicies();
        for (DataLabelPolicy dataLabelPolicy : dataLabelPolicies) {
            dataLabelPolicyRepository.save(dataLabelPolicy);
        }
    }

    /**
     * 辅助方法 - 创建FilterConfigModel
     */
    private List<DataLabelPolicy> getDataLabelPolicies() {
        List<DataLabelPolicy> dataLabelPolicies = new ArrayList<>();
        List<String> list = readAsStringList("data_label.json");
        for (String s : list) {
            DataLabelPolicy model = JSON.parseObject(s, DataLabelPolicy.class);
            dataLabelPolicies.add(model);
        }
        return dataLabelPolicies;
    }

    /**
     * 从functions.json文件读取内容并转换为字符串列表
     * 每一行作为一个字符串元素
     * @param filePath 文件路径，如果为null则从classpath中读取
     * @return 字符串列表
     */
    public static List<String> readAsStringList(String filePath) {
        List<String> objectStrings = new ArrayList<>();
        // 读取functions.json文件
        InputStream inputStream = DataLabelPolicyRepositoryTest.class.getClassLoader().getResourceAsStream(filePath);
        if (inputStream == null) {
            throw new RuntimeException("Could not find filter.config.json file");
        }
        String content = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                .lines().collect(Collectors.joining("\n"));
        // 解析JSON数组
        JSONArray jsonArray = JSON.parseArray(content);

        // 将每个JSON对象转换为字符串并添加到列表中
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            objectStrings.add(jsonObject.toJSONString());
        }
        return objectStrings;
    }
}
