package omni.audit.label.handle;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.quanzhi.re.core.RuleEngine;
import com.quanzhi.re.core.defaults.FeatureDefault;
import com.quanzhi.re.core.domain.entity.DecisionResult;
import com.quanzhi.re.core.domain.entity.EventContext;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import com.quanzhi.re.core.domain.entity.po.RelatedVariable;
import com.quanzhi.re.core.domain.facade.RuleFacade;
import com.quanzhi.re.core.initializer.RuleEngineInitializer;
import com.quanzhi.re.core.utils.SpringBeanUtil;
import com.quanzhi.re.core.variable.facade.VariableFacade;
import com.quanzhi.re.infrastructure.config.PostgresConfig;
import com.quanzhi.re.infrastructure.factory.RepositoryFactory;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import omni.audit.label.entity.LabelCompiledRule;
import omni.audit.label.entity.LabelRule;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.BeanListHandler;

import java.util.*;

@Slf4j
public class LabelHandle {

    private List<RelatedVariable> relatedLeftVarOrders = new ArrayList<>();

    private VariableFacade variableFacade;

    private final RuleEngine ruleEngine = new RuleEngine();

    private RuleFacade ruleFacade;

    private Map<String, List<LabelCompiledRule>> compiledRuleMap = new HashMap<>();

    @Getter
    public PostgresConfig postgresConfig;

    @Getter
    public RepositoryFactory repositoryFactory;

    public LabelHandle(Map<String, Object> labelParam) {
        postgresConfig = new PostgresConfig((String) labelParam.get("jdbcUrl"),
                                            (String) labelParam.get("jdbcUser"),
                                            (String) labelParam.get("jdbcPassword"),
                                            (String) labelParam.get("jdbcSchema"));
        repositoryFactory = new RepositoryFactory(postgresConfig);
        // 初始化决策引擎
        RuleEngineInitializer.initialize(repositoryFactory.getFunctionRepository(),
                                         repositoryFactory.getFeatureRepository(),
                                         repositoryFactory.getVariableRepository(),
                                         repositoryFactory.getRuleRepository());
        ruleFacade = SpringBeanUtil.getBean(RuleFacade.class);
        variableFacade = SpringBeanUtil.getBean(VariableFacade.class);
        init(labelParam);
    }

    public void init(Map<String, Object> labelParam) {
        JdbcConfig jdbcConfig = JdbcConfig.builder().username((String) labelParam.get("jdbcUser"))
                                          .jdbcUrl((String) labelParam.get("jdbcUrl"))
                                          .password((String) labelParam.get("jdbcPassword"))
                                          .schema(labelParam.get("jdbcSchema") == null ? "" : labelParam.get(
                                                  "jdbcSchema").toString())
                                          .driverClassName("org.postgresql.Driver")
                                          .build();
        QueryRunner queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        try {
            List<LabelRule> labelRules = queryRunner.query(
                    "select id , name , first_class AS firstClass, enabled, description, label_type AS labelType, match_rule AS matchRule from " + labelParam.get(
                            "jdbcSchema") + "." + labelParam.get("labelTableName"),
                    new BeanListHandler<>(LabelRule.class));
            for (LabelRule labelRule : labelRules) {
                if (labelRule.getEnabled()) {
                    String labelType = labelRule.getLabelType();
                    String matchRuleStr = labelRule.getMatchRule();
                    ObjectMapper objectMapper = new ObjectMapper();
                    if (!matchRuleStr.trim().isEmpty()) {
                        MatchRule matchRule = objectMapper.readValue(matchRuleStr, MatchRule.class);
                        relatedLeftVarOrders.addAll(ruleFacade.queryVarOrder(matchRule).getRelatedLeftVarOrder());
                        LabelCompiledRule labelCompiledRule = new LabelCompiledRule();
                        labelCompiledRule.setId(labelRule.getId());
                        labelCompiledRule.setCompiledRule(ruleEngine.compile(matchRule));
                        compiledRuleMap.computeIfAbsent(labelType, k -> new ArrayList<>()).add(labelCompiledRule);
                    }
                }
            }
        } catch (Exception e) {
            log.error("query label rules error:", e);
        }

    }

    private List<MatchRule> convert(Object matchRulesObj) {
        ObjectMapper objectMapper = new ObjectMapper();
        List<MatchRule> matchRules = new ArrayList<>();

        try {
            // 情况1：PostgreSQL 返回的是 java.sql.Array 类型
            if (matchRulesObj instanceof java.sql.Array) {
                // 转换为Java数组
                Object[] rulesArray = (Object[]) ((java.sql.Array) matchRulesObj).getArray();

                for (Object ruleObj : rulesArray) {
                    if (ruleObj != null) {
                        // 每个元素都是text类型，直接解析为CompiledRule
                        MatchRule rule = objectMapper.readValue(ruleObj.toString(), MatchRule.class);
                        matchRules.add(rule);
                    }
                }
            } else if (matchRulesObj instanceof String[]) {
                // 情况2：某些驱动可能已经转换为String数组
                String[] rulesArray = (String[]) matchRulesObj;
                for (String ruleJson : rulesArray) {
                    if (ruleJson != null && !ruleJson.isEmpty()) {
                        MatchRule rule = objectMapper.readValue(ruleJson, MatchRule.class);
                        matchRules.add(rule);
                    }
                }
            } else if (matchRulesObj instanceof String) {
                // 情况3：可能存储为单个JSON数组字符串
                String jsonStr = (String) matchRulesObj;
                if (!jsonStr.trim().isEmpty()) {
                    matchRules = objectMapper.readValue(
                            jsonStr,
                            new TypeReference<List<MatchRule>>() {

                            }
                    );
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("解析PostgreSQL match_rules字段失败", e);
        }
        return matchRules;
    }

    public List<Integer> handle(Map<String, Object> source, String labelType) {
        List<Integer> list = new ArrayList<>();
        try {
            if (!compiledRuleMap.isEmpty()) {
                EventContext context = new EventContext(source);
                Set<String> vars = new HashSet<>();
                if (relatedLeftVarOrders != null && !relatedLeftVarOrders.isEmpty()) {
                    for (RelatedVariable relatedVariable : relatedLeftVarOrders) {
                        if (FeatureDefault.GENERAL_VARIABLE.equals(relatedVariable.getType())) {
                            if (!vars.contains(relatedVariable.getValue())) {
                                context.assignValue(relatedVariable.getValue(),
                                                    variableFacade.calculatedValue(relatedVariable.getValue(),
                                                                                   context));
                                vars.add(relatedVariable.getValue());
                            }
                        }
                    }
                }
                List<LabelCompiledRule> labelCompiledRules = compiledRuleMap.get(labelType);
                if (labelCompiledRules == null) {
                    return list;
                }
                for (LabelCompiledRule labelCompiledRule : labelCompiledRules) {
                    DecisionResult decisionResult = ruleEngine.eval(labelCompiledRule.getCompiledRule(), context);
                    if (decisionResult.isSuccess()) {
                        list.add(labelCompiledRule.getId());
                    }
                }
            }
        } catch (Exception e) {
            log.error("query label rules error:", e);
        }
        return list;
    }

}
