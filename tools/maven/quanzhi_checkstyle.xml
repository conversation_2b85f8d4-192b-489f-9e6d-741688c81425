<?xml version="1.0"?>
<!-- Quanzhi Checkstyle Configuration -->

<!DOCTYPE module PUBLIC "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
        "https://checkstyle.org/dtds/configuration_1_3.dtd">

<module name="Checker">
    <property name="charset" value="UTF-8"/>
    <property name="severity" value="error"/>
    <property name="fileExtensions" value="java, properties, xml"/>

    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value="(HttpDefinedEvent\.java|AdaptiveCounting\.java|MurmurHash\.java|Lookup3Hash\.java|LogLog\.java)$"/>
    </module>

    <!-- <module name="BeforeExecutionExclusionFileFilter"> -->
    <!--     <property name="fileNamePattern" value="module\-info\.java$"/> -->
    <!-- </module> -->

    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
    </module>
    <module name="LineLength">
        <property name="fileExtensions" value="java"/>
        <property name="severity" value="warning"/>
        <property name="max" value="150"/>
        <property name="ignorePattern"
                  value="^implements.*|^extends.*|^package.*|^import.*|a href|href|http://|https://|ftp://"/>
    </module>
    <module name="SuppressWarningsFilter"/>

    <module name="TreeWalker">
        <module name="SuppressionCommentFilter"/>
        <module name="SuppressWarningsHolder"/>

        <!-- Name Checker -->
        <module name="OuterTypeFilename"/>
        <module name="PackageName">
            <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
            <message key="name.invalidPattern"
                     value="Package name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <module name="TypeName"/>
        <module name="MemberName"/>
        <module name="ParameterName"/>
        <module name="LambdaParameterName"/>
        <module name="CatchParameterName"/>
        <module name="LocalVariableName">
            <property name="format" value="^[a-z][a-zA-Z0-9_]*$"/>
        </module>
        <module name="ClassTypeParameterName"/>
        <module name="MethodTypeParameterName"/>
        <module name="InterfaceTypeParameterName">
            <property name="format" value="^[A-Z]+$"/>
        </module>

        <module name="MethodName"/>
        <module name="ConstantName">
            <property name="format"
                      value="^log(ger)?$|^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$"/>
        </module>
        <module name="StaticVariableName">
            <property name="format" value="^[a-zA-Z][a-zA-Z0-9_]*$"/>
        </module>
        <module name="AbbreviationAsWordInName">
            <property name="ignoreFinal" value="false"/>
            <property name="allowedAbbreviationLength" value="4"/>
            <property name="allowedAbbreviations" value="VO,DTO,DO,IP,JSON,XML,HTML"/>
        </module>

        <!-- Import Checker -->
        <!-- <module name="AvoidStarImport"/>-->
        <module name="UnusedImports"/>
        <module name="RedundantImport"/>

        <!-- Block Checker -->
        <module name="EmptyBlock">
            <property name="option" value="TEXT"/>
            <property name="tokens"
                      value="LITERAL_TRY, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH"/>
        </module>
        <module name="EmptyCatchBlock">
            <property name="exceptionVariableName" value="expected|ignore(d)?"/>
        </module>
        <module name="LeftCurly"/>
        <module name="RightCurly"/>
        <module name="NeedBraces"/>

        <!-- Javadoc Checker -->
        <!-- <module name="JavadocMethod"> -->
        <!--     <property name="accessModifiers" value="public"/> -->
        <!--     <property name="allowMissingParamTags" value="true"/> -->
        <!--     <property name="allowMissingReturnTag" value="true"/> -->
        <!--     <property name="allowedAnnotations" -->
        <!--               value="Override, Test, Before, After, BeforeClass, AfterClass, Parameterized, Parameters, Bean"/> -->
        <!--     <property name="tokens" value="METHOD_DEF, CTOR_DEF, ANNOTATION_FIELD_DEF"/> -->
        <!-- </module> -->
        <!-- <module name="MissingJavadocMethod"> -->
        <!--     <property name="scope" value="public"/> -->
        <!--     <property name="minLineCount" value="2"/> -->
        <!--     <property name="allowedAnnotations" -->
        <!--               value="Override, Test, Before, After, BeforeClass, AfterClass, Parameterized, Parameters, Bean"/> -->
        <!--     <property name="ignoreMethodNamesRegex" value="^set[A-Z].*|^get[A-Z].*|main"/> -->
        <!--     <property name="tokens" value="METHOD_DEF, ANNOTATION_FIELD_DEF"/> -->
        <!-- </module> -->
        <!-- <module name="SingleLineJavadoc"> -->
        <!--     <property name="ignoreInlineTags" value="false"/> -->
        <!-- </module> -->
        <!-- <module name="InvalidJavadocPosition"/> -->
        <!-- <module name="SummaryJavadoc"> -->
        <!--     <property name="forbiddenSummaryFragments" -->
        <!--               value="^@return the *|^This method returns |^A [{]@code [a-zA-Z0-9]+[}]( is a )"/> -->
        <!-- </module> -->
        <!-- <module name="JavadocParagraph"/> -->
        <!-- <module name="NonEmptyAtclauseDescription"/> -->

        <!-- Coding Checker -->
        <module name="IllegalTokenText">
            <property name="tokens" value="STRING_LITERAL, CHAR_LITERAL"/>
            <property name="format"
                      value="\\u00(09|0(a|A)|0(c|C)|0(d|D)|22|27|5(C|c))|\\(0(10|11|12|14|15|42|47)|134)"/>
            <property name="message"
                      value="Consider using special escape sequence instead of octal value or Unicode escaped value."/>
        </module>
        <module name="OneStatementPerLine"/>
        <module name="MultipleVariableDeclarations"/>
        <!-- <module name="MissingSwitchDefault"/> -->
        <module name="FallThrough"/>
        <module name="NoFinalizer"/>
        <module name="OverloadMethodsDeclarationOrder"/>
        <!-- <module name="VariableDeclarationUsageDistance"/> -->
        <!-- <module name="AtclauseOrder"> -->
        <!--     <property name="tagOrder" value="@param, @return, @throws, @deprecated"/> -->
        <!-- </module> -->

        <!-- Miscellaneous Checker -->
        <module name="AvoidEscapedUnicodeCharacters">
            <property name="allowEscapesForControlCharacters" value="true"/>
            <property name="allowByTailComment" value="true"/>
            <property name="allowNonPrintableEscapes" value="true"/>
        </module>
        <!-- <module name="Indentation"> -->
        <!--     <property name="arrayInitIndent" value="8"/> -->
        <!--     <property name="lineWrappingIndentation" value="8"/> -->
        <!-- </module> -->
        <module name="CommentsIndentation">
            <property name="tokens" value="SINGLE_LINE_COMMENT, BLOCK_COMMENT_BEGIN"/>
        </module>
        <module name="ArrayTypeStyle"/>
        <module name="UpperEll"/>

        <!-- Design Checker -->
        <module name="OneTopLevelClass"/>

        <!-- Whitespace -->
        <module name="NoLineWrap"/>
        <module name="WhitespaceAfter"/>
        <module name="WhitespaceAround">
            <property name="allowEmptyConstructors" value="true"/>
        </module>
        <module name="EmptyLineSeparator">
            <property name="allowMultipleEmptyLines" value="false"/>
            <property name="allowMultipleEmptyLinesInsideClassMembers" value="false"/>
        </module>
        <module name="SeparatorWrap">
            <property name="id" value="SeparatorWrapDot"/>
            <property name="tokens" value="DOT"/>
            <property name="option" value="nl"/>
        </module>
        <!-- <module name="SeparatorWrap"> -->
        <!--     <property name="id" value="SeparatorWrapComma"/> -->
        <!--     <property name="tokens" value="COMMA"/> -->
        <!--     <property name="option" value="EOL"/> -->
        <!-- </module> -->
        <module name="SeparatorWrap">
            <property name="id" value="SeparatorWrapEllipsis"/>
            <property name="tokens" value="ELLIPSIS"/>
            <property name="option" value="EOL"/>
        </module>
        <module name="SeparatorWrap">
            <property name="id" value="SeparatorWrapArrayDeclarator"/>
            <property name="tokens" value="ARRAY_DECLARATOR"/>
            <property name="option" value="EOL"/>
        </module>
        <module name="SeparatorWrap">
            <property name="id" value="SeparatorWrapMethodRef"/>
            <property name="tokens" value="METHOD_REF"/>
            <property name="option" value="nl"/>
        </module>
        <module name="GenericWhitespace">
            <message key="ws.followed"
                     value="GenericWhitespace ''{0}'' is followed by whitespace."/>
            <message key="ws.preceded"
                     value="GenericWhitespace ''{0}'' is preceded with whitespace."/>
            <message key="ws.illegalFollow"
                     value="GenericWhitespace ''{0}'' should followed by whitespace."/>
            <message key="ws.notPreceded"
                     value="GenericWhitespace ''{0}'' is not preceded with whitespace."/>
        </module>
        <module name="MethodParamPad"/>
        <!-- <module name="NoWhitespaceBefore"/> -->
        <module name="ParenPad"/>
        <module name="OperatorWrap">
            <property name="option" value="NL"/>
            <property name="tokens"
                      value="BAND, BOR, BSR, BXOR, DIV, EQUAL, GE, GT, LAND, LE, LITERAL_INSTANCEOF, LOR,
                    LT, MINUS, MOD, NOT_EQUAL, PLUS, QUESTION, SL, SR, STAR, METHOD_REF "/>
        </module>

        <!-- Modifier Checker -->
        <module name="ModifierOrder"/>

        <!-- Annotation Checker -->
        <module name="AnnotationLocation">
            <property name="id" value="AnnotationLocationMostCases"/>
            <property name="tokens"
                      value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF"/>
        </module>
        <module name="AnnotationLocation">
            <property name="id" value="AnnotationLocationVariables"/>
            <property name="tokens" value="VARIABLE_DEF"/>
            <property name="allowSamelineMultipleAnnotations" value="true"/>
        </module>
    </module>


    <!-- Quanzhi Custom Checkstyle -->

    <module name="TreeWalker">

        <module name="Regexp">
            <property name="format" value="org\.apache\.commons\.lang\."/>
            <property name="illegalPattern" value="true"/>
            <property name="message" value="Use commons-lang3 instead of commons-lang."/>
        </module>

        <module name="Regexp">
            <property name="format" value="org\.apache\.commons\.collections\."/>
            <property name="illegalPattern" value="true"/>
            <property name="message" value="Use commons-collections4 instead of commons-collections."/>
        </module>
    </module>


</module>