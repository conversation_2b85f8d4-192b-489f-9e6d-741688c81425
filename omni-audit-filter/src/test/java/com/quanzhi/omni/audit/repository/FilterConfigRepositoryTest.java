package com.quanzhi.omni.audit.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quanzhi.omni.audit.filter.FilterConfigModel;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import org.apache.commons.dbutils.QueryRunner;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RunWith(JUnit4.class)
public class FilterConfigRepositoryTest {

    private FilterConfigRepository filterConfigRepository;


    @Before
    public void setUp() {
        JdbcConfig jdbcConfig = JdbcConfig.builder().username("postgres").jdbcUrl("**********************************************")
                .password("uO0LpGbeQy2T0DWR")
                .driverClassName("org.postgresql.Driver")
                .build();
        QueryRunner queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        filterConfigRepository = new FilterConfigRepository(queryRunner);
    }

    @Test
    public void getFilterConfigs() {
        System.out.println(JSON.toJSONString(filterConfigRepository.getFilterConfigs()));
    }

    @Test
    public void getFilterConfigList() {
        System.out.println(JSON.toJSONString(filterConfigRepository.getFilterConfigList()));
    }

    @Test
    public void saveFilterConfig() {
        List<FilterConfigModel> filterConfigModels = getFilterConfigModels();
        for (FilterConfigModel filterConfigModel : filterConfigModels) {
            filterConfigRepository.saveFilterConfig(filterConfigModel);
        }
    }

    /**
     * 辅助方法 - 创建FilterConfigModel
     */
    private List<FilterConfigModel> getFilterConfigModels() {
        List<FilterConfigModel> filterConfigModels = new ArrayList<>();
        List<String> list = readAsStringList("filter.config.json");
        for (String s : list) {
            FilterConfigModel model = JSON.parseObject(s, FilterConfigModel.class);
            filterConfigModels.add(model);
        }
        return filterConfigModels;
    }

    /**
     * 从functions.json文件读取内容并转换为字符串列表
     * 每一行作为一个字符串元素
     * @param filePath 文件路径，如果为null则从classpath中读取
     * @return 字符串列表
     */
    public static List<String> readAsStringList(String filePath) {
        List<String> objectStrings = new ArrayList<>();
        // 读取functions.json文件
        InputStream inputStream = FilterConfigRepositoryTest.class.getClassLoader().getResourceAsStream(filePath);
        if (inputStream == null) {
            throw new RuntimeException("Could not find filter.config.json file");
        }
        String content = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                .lines().collect(Collectors.joining("\n"));
        // 解析JSON数组
        JSONArray jsonArray = JSON.parseArray(content);

        // 将每个JSON对象转换为字符串并添加到列表中
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            objectStrings.add(jsonObject.toJSONString());
        }
        return objectStrings;
    }
}
