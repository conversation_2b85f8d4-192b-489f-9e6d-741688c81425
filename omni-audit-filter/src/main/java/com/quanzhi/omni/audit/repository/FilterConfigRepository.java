package com.quanzhi.omni.audit.repository;

import com.alibaba.fastjson.JSON;
import com.quanzhi.omni.audit.filter.FilterConfigModel;
import com.quanzhi.omni.audit.jdbc.CamelCaseJsonbBeanListHandler;
import com.quanzhi.omni.audit.query.handler.CamelCaseBeanListHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.ScalarHandler;
import org.postgresql.util.PGobject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class FilterConfigRepository implements Serializable {

    private final QueryRunner queryRunner;

    public FilterConfigRepository(QueryRunner queryRunner) {
        this.queryRunner = queryRunner;
    }

    /**
     * 获取过滤配置列表
     * 使用 CamelCaseJsonbBeanListHandler 处理器，可以同时：
     * 1. 将数据库中的下划线命名（如 keep_enable）转换为驼峰命名（如 keepEnable）
     * 2. 将 jsonb 类型（如 match_rule）转换为对应的对象（如 MatchRule）
     *
     * @return 过滤配置列表，不会返回 null
     */
    public List<FilterConfigModel> getFilterConfigs() {
        try {
            // 使用新的 CamelCaseJsonbBeanListHandler 处理器
            CamelCaseJsonbBeanListHandler<FilterConfigModel> handler =
                    new CamelCaseJsonbBeanListHandler<>(FilterConfigModel.class);
            List<FilterConfigModel> filterConfigModels = queryRunner.query("SELECT * FROM public.oad_filter", handler);
            return filterConfigModels != null ? filterConfigModels : new ArrayList<>();
        } catch (Exception e) {
            log.error("query filter config error:", e);
        }
        return new ArrayList<>();
    }

    public List<FilterConfigModel> getFilterConfigList() {
        try {
            // 使用新的 CamelCaseJsonbBeanListHandler 处理器
            CamelCaseBeanListHandler<FilterConfigModel> handler = new CamelCaseBeanListHandler<>(FilterConfigModel.class);
            List<FilterConfigModel> filterConfigModels = queryRunner.query(
                    "SELECT id,title,type,keep_enable,enabled,priority,create_time,update_time,del_flag FROM public.oad_filter",
                    handler);
            return filterConfigModels != null ? filterConfigModels : new ArrayList<>();
        } catch (Exception e) {
            log.error("query filter config error:", e);
        }
        return new ArrayList<>();
    }

    public void saveFilterConfig(FilterConfigModel filterConfigModel) {
        try {
            PGobject matchRuleObject = new PGobject();
            matchRuleObject.setType("jsonb");
            matchRuleObject.setValue(JSON.toJSONString(filterConfigModel.getMatchRule()));
            Object[] params = {
                    filterConfigModel.getTitle(),
                    filterConfigModel.getType(),
                    filterConfigModel.getPriority(),
                    filterConfigModel.getKeepEnable(),
                    filterConfigModel.getEnabled(),
                    filterConfigModel.getCreateTime(),
                    filterConfigModel.getUpdateTime(),
                    filterConfigModel.getDelFlag(),
                    matchRuleObject
            };
            ScalarHandler<Long> rsh = new ScalarHandler<>();
            String sql = "INSERT INTO public.oad_filter (title, type, priority, keep_enable, enabled, create_time, update_time, del_flag, match_rule) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            queryRunner.insert(sql, rsh, params);
        } catch (Exception e) {
            log.error("save filter config error:", e);
        }
    }

}
