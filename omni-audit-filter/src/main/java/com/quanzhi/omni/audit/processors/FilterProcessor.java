package com.quanzhi.omni.audit.processors;

import com.quanzhi.omni.audit.filter.FilterConfigModel;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.quanzhi.omni.audit.repository.FilterConfigRepository;
import com.quanzhi.re.core.RuleEngine;
import com.quanzhi.re.core.defaults.FeatureDefault;
import com.quanzhi.re.core.domain.engine.CompiledRule;
import com.quanzhi.re.core.domain.entity.Decision;
import com.quanzhi.re.core.domain.entity.DecisionResult;
import com.quanzhi.re.core.domain.entity.EventContext;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import com.quanzhi.re.core.domain.entity.po.RelatedVariable;
import com.quanzhi.re.core.domain.facade.RuleFacade;
import com.quanzhi.re.core.initializer.RuleEngineInitializer;
import com.quanzhi.re.core.utils.SpringBeanUtil;
import com.quanzhi.re.core.variable.facade.VariableFacade;
import com.quanzhi.re.infrastructure.config.PostgresConfig;
import com.quanzhi.re.infrastructure.factory.RepositoryFactory;
import lombok.Setter;
import org.apache.commons.dbutils.QueryRunner;

import java.io.Serializable;
import java.util.*;

public class FilterProcessor implements Serializable {

    private final RuleFacade ruleFacade;

    private final VariableFacade variableFacade;

    @Setter
    private Map<Long, CompiledRule> compiledRuleMap;

    @Setter
    private List<FilterConfigModel> saveList = new ArrayList<>();

    @Setter
    private List<FilterConfigModel> filterList = new ArrayList<>();

    private List<RelatedVariable> relatedLeftVarOrders;

    private final RuleEngine ruleEngine = new RuleEngine();

    public FilterProcessor(
            String jdbcUrl,
            String username,
            String password,
            String schema,
            String... functionScanPackages
    ) {
        PostgresConfig postgresConfig = new PostgresConfig(jdbcUrl, username, password, schema);
        RepositoryFactory repositoryFactory = new RepositoryFactory(postgresConfig);
        // 初始化决策引擎
        RuleEngineInitializer.initialize(repositoryFactory.getFunctionRepository(),
                                         repositoryFactory.getFeatureRepository(),
                                         repositoryFactory.getVariableRepository(),
                                         repositoryFactory.getRuleRepository(),
                                         functionScanPackages);
        ruleFacade = SpringBeanUtil.getBean(RuleFacade.class);
        variableFacade = SpringBeanUtil.getBean(VariableFacade.class);
        updateConfig(jdbcUrl, username, password);
    }

    private void updateConfig(String jdbcUrl, String username, String password) {
        JdbcConfig jdbcConfig = JdbcConfig.builder().username(username).jdbcUrl(jdbcUrl)
                                          .password(password)
                                          .driverClassName("org.postgresql.Driver")
                                          .build();
        QueryRunner queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        FilterConfigRepository filterConfigRepository = new FilterConfigRepository(queryRunner);
        List<FilterConfigModel> configModels = filterConfigRepository.getFilterConfigs();
        // 按优先级排序（数字越小，优先级越高）
        List<FilterConfigModel> sortedModels = new ArrayList<>(configModels);
        sortedModels.sort(Comparator.comparing(FilterConfigModel::getPriority));
        Map<Long, CompiledRule> map = new HashMap<>();
        List<RelatedVariable> relatedLeftVarOrderList = new ArrayList<>();
        List<FilterConfigModel> saveConfigList = new ArrayList<>();
        List<FilterConfigModel> filterConfigList = new ArrayList<>();
        for (FilterConfigModel configModel : sortedModels) {
            if (configModel.getMatchRule() == null || !Boolean.TRUE.equals(configModel.getEnabled())) {
                continue;
            }
            if (FilterConfigModel.FilterType.SAVE.equals(configModel.getType())) {
                saveConfigList.add(configModel);
            } else if (FilterConfigModel.FilterType.FILTER.equals(configModel.getType())) {
                filterConfigList.add(configModel);
            }
            MatchRule matchRule = configModel.getMatchRule();
            relatedLeftVarOrderList.addAll(ruleFacade.queryVarOrder(matchRule).getRelatedLeftVarOrder());
            map.put(configModel.getId(), ruleEngine.compile(matchRule));
        }
        relatedLeftVarOrders = relatedLeftVarOrderList;
        compiledRuleMap = map;
        saveList = saveConfigList;
        filterList = filterConfigList;
    }

    public boolean execute(Map<String, Object> event) {
        // 应用过滤逻辑
        return passFilter(event);
    }

    /**
     * 过滤
     *
     * @param event 流文件属性，用于条件判断
     * @return true-通过过滤（保留），false-被过滤掉（丢弃）
     */
    protected boolean passFilter(Map<String, Object> event) {
        if (event == null || event.isEmpty()) {
            return false;
        }
        EventContext<Map<String, Object>> context = new EventContext<>(event);
        Set<String> vars = new HashSet<>();
        if (relatedLeftVarOrders != null && !relatedLeftVarOrders.isEmpty()) {
            for (RelatedVariable relatedVariable : relatedLeftVarOrders) {
                if (FeatureDefault.GENERAL_VARIABLE.equals(relatedVariable.getType())) {
                    if (!vars.contains(relatedVariable.getValue())) {
                        try {
                            context.assignValue(relatedVariable.getValue(),
                                                variableFacade.calculatedValue(relatedVariable.getValue(), context));
                            vars.add(relatedVariable.getValue());
                        } catch (Exception ignored) {
                        }
                    }
                }
            }
        }
        // 先处理排除名单（优先排除）
        boolean shouldFilter = processFilterList(filterList, context);
        if (shouldFilter) {
            // 如果命中了排除名单，直接过滤掉
            return false;
        }

        // 再处理通过名单
        boolean shouldSave = processSaveList(saveList, context);

        // 如果有通过名单规则，则必须命中通过名单才能通过；如果没有通过名单规则，则默认通过
        return saveList.isEmpty() || shouldSave;
    }

    /**
     * 处理排除名单
     *
     * @param filterList 排除名单列表
     * @param context    流文件属性
     * @return true-应该被过滤掉，false-不应该被过滤
     */
    protected boolean processFilterList(List<FilterConfigModel> filterList, EventContext<Map<String, Object>> context) {
        if (filterList.isEmpty()) {
            return false;
        }
        int i = 0;
        for (FilterConfigModel model : filterList) {
            i++;
            boolean match = evaluateConditions(compiledRuleMap.get(model.getId()), context);
            if (match && (!Boolean.TRUE.equals(model.getKeepEnable()) || i == filterList.size())) {
                // 命中排除规则，应该被过滤掉
                return true;
            }
        }
        return false;
    }

    /**
     * 处理通过名单
     *
     * @param saveList 通过名单列表
     * @param context  流文件属性
     * @return true-应该被保留，false-不应该被保留
     */
    protected boolean processSaveList(List<FilterConfigModel> saveList, EventContext<Map<String, Object>> context) {
        if (saveList.isEmpty()) {
            return true;
        }

        int i = 0;
        for (FilterConfigModel model : saveList) {
            i++;
            boolean match = evaluateConditions(compiledRuleMap.get(model.getId()), context);
            if (match && (!Boolean.TRUE.equals(model.getKeepEnable()) || i == saveList.size())) {
                // 命中通过规则，应该被保留
                return true;
            }
        }
        return false;
    }

    /**
     * 评估过滤条件
     *
     * @param rule    过滤配置模型
     * @param context 流文件属性
     * @return true-条件匹配，false-条件不匹配
     */
    protected boolean evaluateConditions(CompiledRule rule, EventContext<Map<String, Object>> context) {
        if (rule == null || rule.getConditions() == null || rule.getConditions().isEmpty()) {
            return false;
        }
        Decision decision = rule.getDecision();
        if (decision == null || decision.getLogic() == null || decision.getLogic().isEmpty()) {
            return false;
        }
        // 使用决策逻辑表达式进行评估
        try {
            DecisionResult decisionResult = ruleEngine.eval(rule, context);
            if (decisionResult != null) {
                return decisionResult.isSuccess();
            }
        } catch (Exception ignored) {
        }
        return false;
    }

}
