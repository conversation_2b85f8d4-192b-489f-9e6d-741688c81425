package com.quanzhi.omni.audit.jdbc;

import org.apache.commons.dbutils.BasicRowProcessor;
import org.apache.commons.dbutils.BeanProcessor;

/**
 * 使用 CamelCaseJsonbBeanProcessor 的 RowProcessor
 */
public class CamelCaseJsonbRowProcessor extends BasicRowProcessor {

    /**
     * 创建一个使用 CamelCaseJsonbBeanProcessor 的 RowProcessor
     */
    public CamelCaseJsonbRowProcessor() {
        super(new CamelCaseJsonbBeanProcessor());
    }

    /**
     * 创建一个使用指定 BeanProcessor 的 RowProcessor
     */
    public CamelCaseJsonbRowProcessor(BeanProcessor beanProcessor) {
        super(beanProcessor);
    }

}
