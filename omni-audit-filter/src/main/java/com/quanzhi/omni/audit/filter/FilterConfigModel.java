package com.quanzhi.omni.audit.filter;

import com.quanzhi.re.core.domain.entity.po.MatchRule;
import lombok.Data;

import java.io.Serializable;

/**
 * 过滤节点配置模型
 * 根据设计文档中的过滤配置表格创建
 */
@Data
public class FilterConfigModel implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 过滤标题
     */
    private String title;

    /**
     * 类型：枚举：SAVE-通过名单，FILTER-排除名单
     */
    private String type;

    /**
     * 优先级：数字越小，优先级越大
     */
    private Integer priority;

    /**
     * 需不需要继续判断下一个
     */
    private Boolean keepEnable;

    /**
     * 开关
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 逻辑删除标识
     */
    private Boolean delFlag;

    /**
     * 过滤条件集合
     */
    private MatchRule matchRule;

    /**
     * 过滤类型枚举
     */
    public static class FilterType {

        /**
         * 通过名单
         */
        public static final String SAVE = "SAVE";

        /**
         * 排除名单
         */
        public static final String FILTER = "FILTER";

    }

}
