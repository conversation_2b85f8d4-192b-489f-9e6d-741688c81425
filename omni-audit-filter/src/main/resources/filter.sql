CREATE TABLE IF NOT EXISTS public.filter (
                        id VARCHAR(255) PRIMARY KEY,
                        "type" VARCHAR(50) NOT NULL,
                        title VARCHAR(255) NOT NULL,
                        priority INTEGER NOT NULL,
                        keep_enable BOOLEAN,
                        enabled BOOLEAN DEFAULT TRUE,
                        create_time BIGINT,
                        update_time BIGINT,
                        del_flag BOOLEAN DEFAULT FALSE,
                        match_rule JSONB
);
