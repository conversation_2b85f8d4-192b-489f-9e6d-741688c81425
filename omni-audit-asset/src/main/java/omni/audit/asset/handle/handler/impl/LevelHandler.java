package omni.audit.asset.handle.handler.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.quanzhi.re.core.RuleEngine;
import com.quanzhi.re.core.defaults.FeatureDefault;
import com.quanzhi.re.core.domain.engine.CompiledRule;
import com.quanzhi.re.core.domain.entity.DecisionResult;
import com.quanzhi.re.core.domain.entity.EventContext;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import com.quanzhi.re.core.domain.entity.po.RelatedVariable;
import com.quanzhi.re.core.domain.facade.RuleFacade;
import com.quanzhi.re.core.initializer.RuleEngineInitializer;
import com.quanzhi.re.core.utils.SpringBeanUtil;
import com.quanzhi.re.core.variable.facade.VariableFacade;
import com.quanzhi.re.infrastructure.config.PostgresConfig;
import com.quanzhi.re.infrastructure.factory.RepositoryFactory;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import omni.audit.asset.handle.entity.HandleMap;
import omni.audit.asset.handle.entity.LevelCompiledRule;
import omni.audit.asset.handle.entity.LevelRule;
import omni.audit.asset.handle.handler.AbstractHandler;
import omni.audit.common.util.HandleUtil;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.BeanListHandler;

import java.util.*;

@Slf4j
public class LevelHandler extends AbstractHandler {

    Map<String, Object> paramMap;

    private List<RelatedVariable> relatedLeftVarOrders = new ArrayList<>();

    private VariableFacade variableFacade;

    private final RuleEngine ruleEngine = new RuleEngine();

    private RuleFacade ruleFacade;

    private Map<String, List<LevelCompiledRule>> compiledRuleMaps = new HashMap<>(3);

    @Getter
    public PostgresConfig postgresConfig;

    @Getter
    public RepositoryFactory repositoryFactory;

    public LevelHandler(Map<String, Object> paramMap) {
        this.paramMap = paramMap;
        relatedLeftVarOrders = new ArrayList<>();
        postgresConfig = new PostgresConfig((String) paramMap.get("jdbcUrl"),
                                            (String) paramMap.get("jdbcUser"),
                                            (String) paramMap.get("jdbcPassword"),
                                            (String) paramMap.get("jdbcSchema"));
        repositoryFactory = new RepositoryFactory(postgresConfig);
        // 初始化决策引擎
        RuleEngineInitializer.initialize(repositoryFactory.getFunctionRepository(),
                                         repositoryFactory.getFeatureRepository(),
                                         repositoryFactory.getVariableRepository(),
                                         repositoryFactory.getRuleRepository());
        ruleFacade = SpringBeanUtil.getBean(RuleFacade.class);
        variableFacade = SpringBeanUtil.getBean(VariableFacade.class);
        init(paramMap);
    }

    private void init(Map<String, Object> paramMap) {
        JdbcConfig jdbcConfig = JdbcConfig.builder().username((String) paramMap.get("jdbcUser"))
                                          .jdbcUrl((String) paramMap.get("jdbcUrl"))
                                          .password((String) paramMap.get("jdbcPassword"))
                                          .schema(paramMap.get("jdbcSchema") == null ? "" : paramMap.get("jdbcSchema")
                                                                                                    .toString())
                                          .driverClassName("org.postgresql.Driver")
                                          .build();
        QueryRunner queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        try {
            List<LevelRule> levelRules = queryRunner.query(
                    "select id, level_type as levelType , name , sort , match_rule as matchRule from " + paramMap.get(
                            "jdbcSchema") + "." + paramMap.get("levelTableName"),
                    new BeanListHandler<>(LevelRule.class));
            if (levelRules != null && !levelRules.isEmpty()) {
                for (LevelRule levelRule : levelRules) {
                    String levelType = levelRule.getLevelType();
                    if (levelType == null || levelType.isEmpty()) {
                        continue;
                    }
                    String matchRuleObj = levelRule.getMatchRule();
                    ObjectMapper objectMapper = new ObjectMapper();
                    MatchRule matchRule = objectMapper.readValue(matchRuleObj, MatchRule.class);
                    relatedLeftVarOrders.addAll(ruleFacade.queryVarOrder(matchRule).getRelatedLeftVarOrder());
                    CompiledRule compiledRule = ruleEngine.compile(matchRule);
                    LevelCompiledRule levelCompiledRule = new LevelCompiledRule();
                    levelCompiledRule.setId(levelRule.getId());
                    levelCompiledRule.setCompiledRule(compiledRule);
                    levelCompiledRule.setSort(levelRule.getSort());
                    compiledRuleMaps.computeIfAbsent(levelType, k -> new ArrayList<>()).add(levelCompiledRule);
                }
            }
        } catch (Exception e) {
            log.error("query level rules error:", e);
        }
    }

    @Override
    public String name() {
        return "LevelHandler";
    }

    @Override
    public void handle(HandleMap handleMap) {
        Map<String, Object> eventMap = handleMap.getEventMap();
        List<Map<String, String>> assetLevelParam = handleMap.getAssetLevelParam();

        if (!compiledRuleMaps.isEmpty()) {
            compiledRuleMaps.forEach(
                    (k, v) -> {
                        for (Map<String, String> map : assetLevelParam) {
                            try {
                                if (map.get("levelType").equals(k)) {
                                    Integer level = 0;
                                    Integer id = 0;

                                    EventContext context = new EventContext(eventMap);
                                    Set<String> vars = new HashSet<>();
                                    if (relatedLeftVarOrders != null && !relatedLeftVarOrders.isEmpty()) {
                                        for (RelatedVariable relatedVariable : relatedLeftVarOrders) {
                                            if (FeatureDefault.GENERAL_VARIABLE.equals(relatedVariable.getType())) {
                                                if (!vars.contains(relatedVariable.getValue())) {
                                                    context.assignValue(relatedVariable.getValue(),
                                                                        variableFacade.calculatedValue(relatedVariable.getValue(),
                                                                                                       context));
                                                    vars.add(relatedVariable.getValue());
                                                }
                                            }
                                        }
                                    }

                                    for (LevelCompiledRule levelCompiledRule : v) {
                                        DecisionResult decisionResult = ruleEngine.eval(levelCompiledRule.getCompiledRule(),
                                                                                        context);
                                        if (decisionResult.isSuccess()) {
                                            if (levelCompiledRule.getSort() > level) {
                                                level = levelCompiledRule.getSort();
                                                id = levelCompiledRule.getId();
                                            }
                                        }
                                    }

                                    String path = map.get("assetLevelPath");
                                    HandleUtil.putMapField(eventMap, path, id);
                                }
                            } catch (Exception e) {
                                log.error("level handler error:", e);
                            }
                        }
                    }
            );
        }
    }

}
