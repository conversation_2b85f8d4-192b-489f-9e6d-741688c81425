package omni.audit.asset.handle.handler.impl;

import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import lombok.extern.slf4j.Slf4j;
import omni.audit.asset.handle.entity.HandleMap;
import omni.audit.asset.handle.handler.AbstractHandler;
import omni.audit.common.util.HandleUtil;
import org.apache.commons.dbutils.QueryRunner;

import java.util.List;
import java.util.Map;

@Slf4j
public class MajorKeyHandler extends AbstractHandler {

    QueryRunner queryRunner;

    public MajorKeyHandler(Map<String, Object> paramMap) {
        JdbcConfig jdbcConfig = JdbcConfig.builder().username((String) paramMap.get("jdbcUser"))
                                          .jdbcUrl((String) paramMap.get("jdbcUrl"))
                                          .password((String) paramMap.get("jdbcPassword"))
                                          .schema(paramMap.get("jdbcSchema") == null ? "" : paramMap.get("jdbcSchema").toString())
                                          .driverClassName("org.postgresql.Driver")
                                          .build();
        queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
    }

    @Override
    public String name() {
        return "MajorKeyHandler";
    }

    @Override
    public void handle(HandleMap handleMap) {
        Map<String, Object> eventMap = handleMap.getEventMap();
        Map<String, Object> assetIdParam = handleMap.getAssetIdParam();
        for (String tableName : assetIdParam.keySet()) {
            StringBuilder sb = new StringBuilder();
            Map<String, Object> assetMap = (Map<String, Object>) assetIdParam.get(tableName); // 获取如 "a,b.c.d,e.?[f!g!h]" 的表达式
            String pathExpression = (String) assetMap.get("assetIdPath");
            String assetIdEvent = (String) assetMap.get("assetIdEvent");
            // 1. 用逗号分割多个独立路径
            String[] paths = pathExpression.split(",");
            for (int i = 0; i < paths.length; i++) {
                String path = paths[i];
                path = path.trim(); // 去除首尾空格

                if (path.contains(".?.")) {
                    int bracketPos = path.indexOf(".?.");
                    String mapPath = path.substring(0, bracketPos); // 获取 ? 前的路径
                    String key = path.substring(bracketPos + 3);
                    // 获取目标映射（可能是嵌套的多层map）
                    Object target = HandleUtil.getNestedValue(eventMap, mapPath);

                    if (target instanceof List) {
                        List<?> list = (List<?>) target;
                        for (Object value : list) {
                            if (value instanceof Map) {
                                Map<String, Object> map1 = (Map<String, Object>) value;
                                String[] split = key.split("、");
                                StringBuilder sb1 = new StringBuilder();
                                for (String string : split) {
                                    Object object = map1.get(string);
                                    sb1.append(object);
                                }
                                if (i == paths.length - 1) {
                                    // 最后一位
                                    String assetId = sb + sb1.toString();
                                    HandleUtil.putMapField(map1, assetIdEvent, assetId);
                                } else {
                                    sb.append(sb1);
                                }
                            }
                        }
                    }
                } else {
                    // 普通路径处理（如 "a" 或 "b.c.d"）
                    Object value = HandleUtil.getNestedValue(eventMap, path);
                    sb.append(value);
                    if (i == paths.length - 1) {
                        // 最后一位
                        String assetId = sb.toString();
                        HandleUtil.putMapField(eventMap, assetIdEvent, assetId);
                    }
                }
            }

        }
    }

}
