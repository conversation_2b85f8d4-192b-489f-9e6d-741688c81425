package omni.audit.asset.handle.handler.impl;

import com.fasterxml.jackson.databind.node.ObjectNode;
import omni.audit.asset.handle.entity.HandleMap;
import omni.audit.asset.handle.entity.HandlerEntity;
import omni.audit.asset.handle.handler.AbstractHandler;
import omni.audit.common.util.HandleUtil;

import java.util.List;
import java.util.Map;

public class DiscoverTimeHandler extends AbstractHandler {
    Map<String,Object> paramMap;

    public DiscoverTimeHandler(Map<String,Object> paramMap){
        this.paramMap = paramMap;
    }
    @Override
    public String name() {
        return "DiscoverTimeHandler";
    }

    @Override
    public void handle(HandleMap handleMap) {
        Map<String, Object> eventMap = handleMap.getEventMap();
        Map<String, String> assetTimeParam = handleMap.getAssetTimeParam();
        Object timestamp = eventMap.get(assetTimeParam.get("timestamp"));
        for (String key : assetTimeParam.keySet()) {
            if (key.contains("discoverTime") || key.contains("DiscoverTime")){
                String path = assetTimeParam.get(key);
                Map<String,Object> temp = eventMap;
                HandleUtil.updateMapValue(temp,path.split("\\."),0,timestamp,false);
            }
        }
    }
}
