package omni.audit.asset.handle.processors;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import omni.audit.asset.handle.entity.HandleMap;
import omni.audit.asset.handle.entity.HandlerEntity;
import omni.audit.asset.handle.handler.AbstractHandler;
import lombok.extern.slf4j.Slf4j;
import omni.audit.asset.handle.handler.impl.*;
import org.reflections.Reflections;

import java.util.*;

@Slf4j
public class AssetHandlerInit {
    private List<AbstractHandler> abstractHandlers = new ArrayList<>();


    public AssetHandlerInit(List<String> handlerNames,Map<String,Object> paramMap){
        register(handlerNames,paramMap);

    }

    private void register(List<String> handlerNames,Map<String,Object> paramMap){
        for (String handlerName : handlerNames) {
            if (handlerName.equals("ActiveTimeHandler")) {
                abstractHandlers.add(new ActiveTimeHandler(paramMap));
            }else if (handlerName.equals("LevelHandler")) {
                abstractHandlers.add(new LevelHandler(paramMap));
            } else if (handlerName.equals("AssignmentHandler")) {
                abstractHandlers.add(new AssignmentHandler(paramMap));
            } else if (handlerName.equals("MajorKeyHandler")) {
                abstractHandlers.add(new MajorKeyHandler(paramMap));
            } else if (handlerName.equals("DiscoveryHandler")) {
                abstractHandlers.add(new DiscoverTimeHandler(paramMap));
            }
        }
    }

    public void handle(HandleMap handleMap){
        for (AbstractHandler abstractHandler : abstractHandlers) {
            abstractHandler.handle(handleMap);
        }
    }

}
