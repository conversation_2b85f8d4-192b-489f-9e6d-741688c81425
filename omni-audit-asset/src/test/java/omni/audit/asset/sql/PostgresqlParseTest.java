//package omni.audit.asset.sql;
//
//import com.quanzhi.sqlparser.SqlParser;
//import com.quanzhi.sqlparser.view.SqlParserResult;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.junit.runners.JUnit4;
//
//@RunWith(JUnit4.class)
//public class PostgresqlParseTest {
//
//    SqlParser sqlParser = SqlParser.getInstance();
//
//    @Test
//    public void testParse() {
//        // 示例SQL
//        String sql = "SELECT * FROM db1.temp WHERE id = 1 AND name = '<PERSON>'";
//        SqlParserResult sqlParserResult = sqlParser.parsePostgreSQL(sql);
//        Assert.assertEquals("temp",sqlParserResult.get().getTableInfo().getName());
//    }
//}
