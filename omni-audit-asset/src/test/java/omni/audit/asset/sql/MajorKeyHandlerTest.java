//package omni.audit.asset.sql;
//import omni.audit.asset.handle.handler.impl.MajorKeyHandler;
//import org.junit.Test;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import static org.junit.Assert.assertEquals;
//import static org.junit.Assert.assertTrue;
//
//public class MajorKeyHandlerTest {
//
//    @Test
//    public void testFindValues_SimpleKeyPath() {
//        Map<String, Object> map = new HashMap<>();
//        map.put("a", "value1");
//        Map<String, Object> nestedMap = new HashMap<>();
//        nestedMap.put("c", "value2");
//        map.put("b", nestedMap);
//
//        List<Object> values = new ArrayList<>();
//        MajorKeyHandler handler = new MajorKeyHandler();
//        //handler.findValues(map, new String[]{"a"}, 0, values);
//
//        assertEquals(1, values.size());
//        assertEquals("value1", values.get(0));
//    }
//
//    @Test
//    public void testFindValues_NestedKeyPath() {
//        Map<String, Object> map = new HashMap<>();
//        Map<String, Object> nestedMap1 = new HashMap<>();
//        Map<String, Object> nestedMap2 = new HashMap<>();
//        Map<String, Object> nestedMap3 = new HashMap<>();
//        nestedMap2.put("c", "value");
//        nestedMap2.put("e", "value1");
//        nestedMap2.put("f", "value2");
//        nestedMap3.put("c", "value");
//        nestedMap3.put("e", "value1");
//        nestedMap3.put("f", "value2");
//        nestedMap1.put("b", nestedMap2);
//        nestedMap1.put("c", nestedMap3);
//        map.put("a", nestedMap1);
//
//        List<Object> values = new ArrayList<>();
//        MajorKeyHandler handler = new MajorKeyHandler();
//        handler.findValues(map, new String[]{"a", "?", "c"}, 0, values);
//        System.out.println(values);
////        assertEquals(1, values.size());
////        assertEquals("value", values.get(0));
//    }
//
//    @Test
//    public void testFindValues_WildcardAtFirstLevel() {
//        Map<String, Object> map = new HashMap<>();
//        map.put("a", "value1");
//        map.put("b", "value2");
//
//        List<Object> values = new ArrayList<>();
//        MajorKeyHandler handler = new MajorKeyHandler();
//        handler.findValues(map, new String[]{"?"}, 0, values);
//
//        assertEquals(2, values.size());
//        assertTrue(values.contains("value1"));
//        assertTrue(values.contains("value2"));
//    }
//
//    @Test
//    public void testFindValues_WildcardAtNestedLevel() {
////        Map<String, Object> map = new HashMap<>();
////        map.put("a", Map.of("x", "value1"));
////        map.put("b", Map.of("x", "value2"));
////
////        List<Object> values = new ArrayList<>();
////        MajorKeyHandler handler = new MajorKeyHandler();
////        handler.findValues(map, new String[]{"?", "x"}, 0, values);
////
////        assertEquals(2, values.size());
////        assertTrue(values.contains("value1"));
////        assertTrue(values.contains("value2"));
//    }
//
//    @Test
//    public void testFindValues_KeyNotFound() {
//        Map<String, Object> map = new HashMap<>();
//        map.put("a", "value");
//
//        List<Object> values = new ArrayList<>();
//        MajorKeyHandler handler = new MajorKeyHandler();
//        handler.findValues(map, new String[]{"b"}, 0, values);
//
//        assertTrue(values.isEmpty());
//    }
//
//    @Test
//    public void testFindValues_EmptyMap() {
//        Map<String, Object> map = new HashMap<>();
//
//        List<Object> values = new ArrayList<>();
//        MajorKeyHandler handler = new MajorKeyHandler();
//        handler.findValues(map, new String[]{"a"}, 0, values);
//
//        assertTrue(values.isEmpty());
//    }
//
//    @Test
//    public void testFindValues_IndexOutOfBounds() {
//        Map<String, Object> map = new HashMap<>();
//        map.put("a", "value");
//
//        List<Object> values = new ArrayList<>();
//        MajorKeyHandler handler = new MajorKeyHandler();
//        handler.findValues(map, new String[]{"a"}, 1, values);
//
//        assertTrue(values.isEmpty());
//    }
//
//    @Test
//    public void testFindValues_MixedWildcardAndSpecificKeys() {
////        Map<String, Object> map = new HashMap<>();
////        map.put("a", Map.of("x", "value1", "y", "value2"));
////        map.put("b", Map.of("x", "value3"));
////
////        List<Object> values = new ArrayList<>();
////        MajorKeyHandler handler = new MajorKeyHandler();
////        handler.findValues(map, new String[]{"?", "x"}, 0, values);
////
////        assertEquals(2, values.size());
////        assertTrue(values.contains("value1"));
////        assertTrue(values.contains("value3"));
//    }
//}
