<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.quanzhi.omni.audit</groupId>
        <artifactId>omni-audit-parent</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>omni-audit-handler</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <dependencies>
        <dependency>
            <groupId>com.google.auto.service</groupId>
            <artifactId>auto-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omni.audit</groupId>
            <artifactId>omni-audit-apex-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omniaudit</groupId>
            <artifactId>omni-audit-weakness</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omniaudit</groupId>
            <artifactId>omni-audit-sampling</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omni.audit</groupId>
            <artifactId>omni-audit-network-segment</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omni.audit</groupId>
            <artifactId>omni-audit-label</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omni.audit</groupId>
            <artifactId>omni-audit-asset</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.auto.service</groupId>
            <artifactId>auto-service-annotations</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omni.audit</groupId>
            <artifactId>omni-audit-filter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omni.audit</groupId>
            <artifactId>omni-audit-sqlparse</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omni.audit</groupId>
            <artifactId>connector-kafka</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omni.audit</groupId>
            <artifactId>connector-console</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omni.audit</groupId>
            <artifactId>connector-blackhole</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <version>1.7.36</version>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omni.audit</groupId>
            <artifactId>connector-jdbc</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>omni.audit.apex.connectors.extract.jdbc</groupId>
            <artifactId>connector-extract-and-jdbc</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.omni.audit</groupId>
            <artifactId>connector-clickhouse</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.xerial.snappy</groupId>
            <artifactId>snappy-java</artifactId>
            <version>1.1.10.7</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.google.auto.service</groupId>
                            <artifactId>auto-service</artifactId>
                            <version>1.1.1</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.36</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-checkstyle-plugin</artifactId>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.4.1</version>
                <configuration>
                    <shadedArtifactAttached>false</shadedArtifactAttached>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                    <!-- Make sure the transitive dependencies are written to the generated pom under <dependencies> -->
                    <promoteTransitiveDependencies>true</promoteTransitiveDependencies>
                    <filters>
                        <filter>
                            <artifact>*:*</artifact>
                            <excludes>
                                <exclude>META-INF/*.SF</exclude>
                                <exclude>META-INF/*.DSA</exclude>
                                <exclude>META-INF/*.RSA</exclude>
                            </excludes>
                        </filter>
                    </filters>
                </configuration>

                <executions>
                    <execution>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <transformers combine.children="append">
                                <!-- The service transformer is needed to merge META-INF/services files -->
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer" />
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <manifestEntries>
                                        <Main-Class>omni.audit.handler.Application</Main-Class>
                                        <X-Compile-Source-JDK>21</X-Compile-Source-JDK>
                                        <X-Compile-Target-JDK>21</X-Compile-Target-JDK>
                                    </manifestEntries>
                                </transformer>

                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>
