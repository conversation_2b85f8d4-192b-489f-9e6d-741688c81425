package omni.audit.handler;

import omni.audit.apex.api.config.ApexConfig;
import omni.audit.apex.core.dag.Job;
import omni.audit.apex.core.starter.Apex;
import omni.audit.apex.shade.com.typesafe.config.Config;
import omni.audit.apex.shade.com.typesafe.config.ConfigFactory;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;


public class ApplicationTest {

    @Test
    public void testProto() throws Exception {
        // 修改为从类路径加载配置文件
        String filePath = "conf/test_proto.conf";
        String[] paths;
        if (filePath.contains(",")) {
            paths = filePath.split(",");
        } else {
            paths = new String[]{filePath};
        }

        ApexConfig apexConfig = new ApexConfig();
        List<Config> configs = new ArrayList<>();
        for (String path : paths) {
            Config config = ConfigFactory.parseResources(path);
            configs.add(config);
        }
        apexConfig.setJobConfigs(configs);
        List<Job> jobs = Apex.run(apexConfig);
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            for (Job job : jobs) {
                job.stop();
            }
        }));
    }

}
