env {
  job.name = "test-protobuf"
  job.mode = "debug"
  parallelism = 1
}

source {
  kafka {
    parallelism = 1
    topic = "DBEvent"
    bootstrap.servers = "*************:9094"
    consumer.group = "test-protobuf"
    kafka.config = {
      max.poll.records = 10
      auto.offset.reset = "earliest"
    }
    plugin_output = "db_event"
    format = json
  }
}

transform {
  Normalizer {
    plugin_input = "db_event"
    plugin_output = "db_event_flatten"

    fields = {
      timestamp = "meta.tm"
      type = "meta.type"
      metaAppName = "meta.app_name"
      metaServerVersion = "meta.server_version"
      netSrcIp = "net.src_ip"
      netSrcPort = "net.src_port"
      netDstIp = "net.dst_ip"
      netDstPort = "net.dst_port"
      netFlowSource = "net.flow_source"
      mac = "mac.mac"
      eventId = "unique_id.event_id"
      reqDbName = "req.db_name"
      reqDbUser = "req.db_user"
      reqDbPassword = "req.db_password"
      reqSqlCmdType = "req.sql_cmd_type"
      reqSql = "req.sql"
      rspStatus = "rsp.status"
      rspStartTime = "rsp.start_time"
      rspCloseTime = "rsp.close_time"
      rspRowCount = "rsp.row_count"
      rspResult = "rsp.result"
    }

    output_schema = {
      timestamp = bigint
      type = string
      metaAppName = string
      metaServerVersion = string
      netSrcIp = string
      netSrcPort = int
      netDstIp = string
      netDstPort = int
      netFlowSource = string
      mac = string
      eventId = string
      reqDbName = string
      reqDbUser = string
      reqDbPassword = string
      reqSqlCmdType = string
      reqSql = string
      rspStatus = int
      rspStartTime = bigint
      rspCloseTime = bigint
      rspRowCount = int
      rspResult = string
      srvAddress = string
      srvName = string
      srvType = string
      srvLevel = string
      srvRiskLevel = string
      accessDomains = "array<int>"
      deployDomains = "array<int>"
      srvAccessDomains = "array<int>"
      srvDeployDomains = "array<int>"
      reqDataLabels = "array<int>"
      rspDataLabels = "array<int>"
      srvReqDataLabels = "array<int>"
      srvRspDataLabels = "array<int>"
      srvLifeFlag = "array<int>"
      srvDiscoverTime = bigint
      srvActiveTime = bigint
      srvBizSystem = string
      dbVersion = string
      dbType = string
      dbLevel = string
      dbRiskLevel = string
      dbAccessDomains = "array<int>"
      dbDeployDomains = "array<int>"
      dbReqDataLabels = "array<int>"
      dbRspDataLabels = "array<int>"
      dbBizSystem = string
      dbLifeFlag = "array<int>"
      dbDiscoverTime = bigint
      dbActiveTime = bigint
      eventTypes = "array<int>"
      optionMethod = string
      rspTime = bigint
      srvId = string
      dbId = string
    }

  }

}

sink {
  kafka {
    topic = DBEventProtobuf
    bootstrap.servers = "*************:9094"
    format = protobuf
    protobuf_schema = """
    syntax = "proto3";
    
    message DBEvent {
      string eventId = 1;
      int64 timestamp = 2;
      string netSrcIp = 3;
      int32 netSrcPort = 4;
      string netDstIp = 5;
      int32 netDstPort = 6;
      string netFlowSource = 7;
      string mac = 8;
      string reqDbName = 9;
      string reqDbUser = 10;
      string reqDbPassword = 11;
      string reqSql = 12;
      int32 rspStatus = 13;
      int64 rspStartTime = 14;
      int64 rspCloseTime = 15;
      int32 rspRowCount = 16;
      string rspResult = 17;
      string srvId = 18;
      string dbId = 19;
      repeated int32 accessDomains = 20;
      repeated int32 deployDomains = 21;
      repeated int32 reqDataLabels = 22;
      repeated int32 rspDataLabels = 23;
    }
    """
    protobuf_message_name = "DBEvent"
  }
}