package omni.audit.handler.insert;

import com.fasterxml.jackson.core.type.TypeReference;
import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;

import java.util.List;
import java.util.Map;

public class InsertTransformConfig {

    public static final Option<Map<String, Object>> PROPERTIES_PARAM =
            Options.key("properties")
                   .type(new TypeReference<Map<String, Object>>() {

                   })
                   .noDefaultValue()
                   .withDescription("properties");

    public static final Option<List<Map<String, String>>> FIELD_PARAM =
            Options.key("field")
                   .type(new TypeReference<List<Map<String, String>>>() {

                   })
                   .noDefaultValue()
                   .withDescription("properties");

}
