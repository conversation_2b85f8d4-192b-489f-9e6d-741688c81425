package omni.audit.handler.insert;

import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.transform.AbstractApexTransform;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.asset.handle.entity.LevelRule;
import omni.audit.common.util.HandleUtil;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.BeanListHandler;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class InsertTransform extends AbstractApexTransform {

    public static final String PLUGIN_NAME = "insert";

    private final Map<String, Object> propertiesMap;

    private final List<Map<String, String>> fieldList;

    private QueryRunner queryRunner;

    private List<LevelRule> levelRules = new ArrayList<>();

    public InsertTransform(ReadonlyConfig readonlyConfig) {
        this.propertiesMap = readonlyConfig.get(InsertTransformConfig.PROPERTIES_PARAM);
        this.fieldList = readonlyConfig.get(InsertTransformConfig.FIELD_PARAM);
        JdbcConfig jdbcConfig = JdbcConfig.builder().username((String) propertiesMap.get("jdbcUser"))
                                          .jdbcUrl((String) propertiesMap.get("jdbcUrl"))
                                          .password((String) propertiesMap.get("jdbcPassword"))
                                          .schema(propertiesMap.get("jdbcSchema") == null ? "" : propertiesMap.get(
                                                  "jdbcSchema").toString())
                                          .driverClassName("org.postgresql.Driver")
                                          .build();
        queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        try {
            levelRules =
                    queryRunner.query("select id,  sort from "
                                      + propertiesMap.get("jdbcSchema") + "." + propertiesMap.get("levelTableName")
                                      + " where level_type = '" + propertiesMap.get("levelType") + "'",
                                      new BeanListHandler<>(LevelRule.class));
        } catch (SQLException e) {
            log.error("level sql error ", e);
        }
    }

    @Override
    public void open() {
        super.open();
    }

    @Override
    public ApexRow transform(ApexRow row) {
        Map<String, Object> eventMap = row.toMap();
        Object samplingFlag = eventMap.get("samplingFlag");
        if (samplingFlag == null || !(Boolean) samplingFlag) {
            return row;
        }
        Object object = propertiesMap.get("forTables");
        if (object != null) {
            Object listObj = HandleUtil.getMapField(eventMap, (String) object);
            if (listObj instanceof List) {
                List<?> tables = (List<String>) listObj;
                for (int i = 0; i < tables.size(); i++) {
                    Object[] objects = new Object[fieldList.size()];
                    String sql = getSql(eventMap, objects, i);
                    insert(sql, objects, eventMap, i);
                }
            }
        } else {
            Object[] objects = new Object[fieldList.size()];
            String sql = getSql(eventMap, objects);
            insert(sql, objects, eventMap);
        }
        return row;
    }

    private void insert(String sql, Object[] objects, Map<String, Object> eventMap) {
        insert(sql, objects, eventMap, 0);
    }

    private void insert(String sql, Object[] objects, Map<String, Object> eventMap, int index) {
        try {
            Long id = queryRunner.insert(sql, rs -> {
                if (rs.next()) {
                    return rs.getLong(1); // 获取第一个列的值作为主键
                }
                return null;
            }, objects);
            if (id != null) {
                String idPath = (String) propertiesMap.get("idPath");
                if (idPath != null) {
                    if (idPath.contains("[?]")) {
                        idPath = idPath.replace("[?]", "[" + index + "]");
                    }
                    HandleUtil.putMapField(eventMap, idPath, id);
                }
            }
        } catch (Exception e) {
            log.error("insert error", e);
        }
    }

    private String getSql(Map<String, Object> eventMap, Object[] objects) {
        return getSql(eventMap, objects, 0);
    }

    private String getSql(Map<String, Object> eventMap, Object[] objects, int index) {
        StringBuilder stringBuilder1 = new StringBuilder("INSERT INTO ");
        stringBuilder1.append(propertiesMap.get("jdbcSchema")).append(".");
        stringBuilder1.append(propertiesMap.get("tableName"));
        stringBuilder1.append("( ");
        StringBuilder stringBuilder2 = new StringBuilder();
        stringBuilder2.append("VALUES ( ");
        StringBuilder stringBuilder3 = new StringBuilder();
        stringBuilder3.append("ON CONFLICT ( ");
        stringBuilder3.append(propertiesMap.get("majorKey"));
        stringBuilder3.append(" ) DO UPDATE SET ");
        for (int i = 0; i < fieldList.size(); i++) {
            Map<String, String> field = fieldList.get(i);
            String fieldName = field.get("fieldName");
            String fieldPath = field.get("fieldPath");
            if (fieldPath.contains("[?]")) {
                fieldPath = fieldPath.replace("[?]", "[" + index + "]");
            }
            Object object;
            String[] split = fieldPath.split(",");
            if (split.length == 3) {
                String sourceKey = split[0];
                Object compareValue = HandleUtil.getMapField(eventMap, sourceKey);
                object = HandleUtil.getFieldFromEvent(eventMap, split[1], split[2], compareValue);
            } else {
                object = HandleUtil.getMapField(eventMap, fieldPath);
            }
            objects[i] = object;
            stringBuilder1.append(fieldName).append(" ,");
            stringBuilder2.append("? ,");
            if (!fieldName.equals(propertiesMap.get("majorKey"))) {
                if (fieldName.equals(propertiesMap.get("levelKey"))) {
                    // sensi_level = CASE
                    //    WHEN oad_db_table.sensi_level IS NULL THEN EXCLUDED.sensi_level
                    //    WHEN (SELECT sort FROM rd_oad_1aa0aa0.oad_level_rule WHERE id = EXCLUDED.sensi_level) >
                    //         (SELECT sort FROM rd_oad_1aa0aa0.oad_level_rule WHERE id = oad_db_table.sensi_level)
                    //    THEN EXCLUDED.sensi_level
                    //    ELSE oad_db_table.sensi_level
                    // END
                    stringBuilder3.append(fieldName)
                                  .append(" = CASE WHEN ")
                                  .append(propertiesMap.get("tableName")).append(".")
                                  .append(fieldName)
                                  .append(" IS NULL THEN EXCLUDED. ")
                                  .append(fieldName)
                                  .append(" WHEN ( SELECT sort FROM ")
                                  .append(propertiesMap.get("jdbcSchema")).append(".")
                                  .append(propertiesMap.get("levelTableName"))
                                  .append(" WHERE id = EXCLUDED.").append(fieldName).append(" ) > (SELECT sort FROM ")
                                  .append(propertiesMap.get("jdbcSchema")).append(".")
                                  .append(propertiesMap.get("levelTableName"))
                                  .append(" WHERE id = ").append(propertiesMap.get("tableName")).append(".")
                                  .append(fieldName)
                                  .append(" ) THEN EXCLUDED.")
                                  .append(fieldName).append(" ELSE ")
                                  .append(propertiesMap.get("tableName"))
                                  .append(".")
                                  .append(fieldName)
                                  .append(" END ,");
                } else if (fieldName.equals(propertiesMap.get("discoverKey"))) {
                    stringBuilder3.append(fieldName)
                                  .append(" = CASE WHEN ").append(propertiesMap.get("tableName")).append(".")
                                  .append(fieldName)
                                  .append(" IS NULL AND EXCLUDED.")
                                  .append(fieldName)
                                  .append(" IS NOT NULL THEN EXCLUDED.")
                                  .append(fieldName).append(" ELSE ")
                                  .append(propertiesMap.get("tableName"))
                                  .append(".")
                                  .append(fieldName)
                                  .append(" END ,");
                } else {
                    stringBuilder3.append(fieldName)
                                  .append(" = CASE WHEN EXCLUDED.")
                                  .append(fieldName)
                                  .append(" IS NOT NULL THEN EXCLUDED.")
                                  .append(fieldName).append(" ELSE ")
                                  .append(propertiesMap.get("tableName"))
                                  .append(".")
                                  .append(fieldName)
                                  .append(" END ,");
                }
            }
        }
        stringBuilder1.delete(stringBuilder1.length() - 1, stringBuilder1.length()).append(") ")
                      .append(stringBuilder2.delete(stringBuilder2.length() - 1, stringBuilder2.length()).append(") "))
                      .append(stringBuilder3.delete(stringBuilder3.length() - 1, stringBuilder3.length()));
        return stringBuilder1.toString();
    }

    @Override
    public void close() {
        super.close();
    }

}
