package omni.audit.handler.label;

import com.fasterxml.jackson.core.type.TypeReference;
import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;

import java.util.List;
import java.util.Map;

public class LabelTransformConfig {

    public static final Option<List<Map<String, String>>> LABEL_PARAM =
            Options.key("label")
                   .type(new TypeReference<List<Map<String, String>>>() {

                   })
                   .noDefaultValue()
                   .withDescription("label param");

    public static final Option<Map<String, Object>> LABEL_PROPERTIES_PARAM =
            Options.key("properties")
                   .type(new TypeReference<Map<String, Object>>() {

                   })
                   .noDefaultValue()
                   .withDescription("label properties");

}
