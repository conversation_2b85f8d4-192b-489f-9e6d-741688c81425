package omni.audit.handler.label;

import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.transform.AbstractApexTransform;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.common.util.HandleUtil;
import omni.audit.label.handle.LabelHandle;

import java.util.List;
import java.util.Map;

@Slf4j
public class LabelTransform extends AbstractApexTransform {
    public static final String PLUGIN_NAME = "Label";
    private LabelHandle labelHandle;
    private final List<Map<String, String>> labelParam;
    private final Map<String, Object> labelPropertiesParam;

    public LabelTransform(ReadonlyConfig readonlyConfig) {
        this.labelParam = readonlyConfig.get(LabelTransformConfig.LABEL_PARAM);
        this.labelPropertiesParam = readonlyConfig.get(LabelTransformConfig.LABEL_PROPERTIES_PARAM);
    }

    @Override
    public void open() {
        labelHandle = new LabelHandle((String) labelPropertiesParam.get("jdbcUrl"),(String) labelPropertiesParam.get("jdbcUser"),(String) labelPropertiesParam.get("jdbcPassword"),(String) labelPropertiesParam.get("jdbcSchema"));
    }


    @Override
    public ApexRow transform(ApexRow row) {
        Map<String, Object> eventMap = row.toMap();
        for (Map<String, String> stringStringMap : labelParam) {
            List<Integer> labelIds = labelHandle.handle(eventMap, stringStringMap.get("labelType"));
            String[] split = stringStringMap.get("labelPath").split("\\.");
            Integer[] array = labelIds.stream().toArray(Integer[]::new);
            HandleUtil.updateMapValue(eventMap, split,0,array,true);
        }
        return row;
    }

    @Override
    public void close() {
        super.close();
    }
}
