package omni.audit.handler.label;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexTransformFactory;
import omni.audit.apex.api.factory.ApexTransformFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.transform.ApexTransform;
import omni.audit.apex.api.type.ApexRow;

@AutoService(Factory.class)
public class LabelTransformFactory implements ApexTransformFactory {

    @Override
    public String factoryIdentifier() {
        return LabelTransform.PLUGIN_NAME;
    }

    @Override
    public <T> ApexTransform<ApexRow> createTransform(ApexTransformFactoryContext context) {
        return new LabelTransform(context.getOptions());
    }

}
