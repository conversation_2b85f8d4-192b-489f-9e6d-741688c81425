package omni.audit.handler;

import omni.audit.apex.api.config.ApexConfig;
import omni.audit.apex.core.dag.Job;
import omni.audit.apex.core.starter.Apex;
import omni.audit.apex.shade.com.typesafe.config.Config;
import omni.audit.apex.shade.com.typesafe.config.ConfigFactory;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @class Application
 * @created 2025/6/24 10:14
 * @desc
 **/

public class Application {

    private static final String CONFIG_FILE_PATH = "/tmp/apex.conf";
    private static final String CONFIG_FILE_PATH_ENV = "APEX_CONF_PATH";

    public static void main(String[] args) throws Exception {
        // TODO 祁灵 2025/6/24 10:18: 从文件中读取配置
        String filePath = StringUtils.isNotEmpty(System.getenv(CONFIG_FILE_PATH_ENV)) ? System.getenv(CONFIG_FILE_PATH_ENV) : CONFIG_FILE_PATH;
        String[] paths;
        if (filePath.contains(",")) {
            paths = filePath.split(",");
        } else {
            paths = new String[]{filePath};
        }

        ApexConfig apexConfig = new ApexConfig();
        List<Config> configs = new ArrayList<>();
        for (String path : paths) {

            Config config = ConfigFactory.parseFile(getFileFromResources(path));
            configs.add(config);
        }
        apexConfig.setJobConfigs(configs);
        List<Job> jobs = Apex.run(apexConfig);
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            for (Job job : jobs) {
                job.stop();
            }
        }));
    }

    public static File getFileFromResources(String fileName) throws URISyntaxException {
        return new File(fileName);
    }

}
