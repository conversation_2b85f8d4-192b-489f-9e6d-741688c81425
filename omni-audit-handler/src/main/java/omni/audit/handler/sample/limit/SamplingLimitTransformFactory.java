package omni.audit.handler.sample.limit;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexTransformFactory;
import omni.audit.apex.api.factory.ApexTransformFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.transform.ApexTransform;
import omni.audit.apex.api.type.ApexRow;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:33
 * @description:
 **/
@AutoService(Factory.class)
public class SamplingLimitTransformFactory implements ApexTransformFactory {


    @Override
    public String factoryIdentifier() {
        return SamplingLimitTransform.PLUGIN_NAME;
    }


    @Override
    public <T> ApexTransform<ApexRow> createTransform(ApexTransformFactoryContext context) {
        return new SamplingLimitTransform(SamplingLimitTransformConfig.of(context.getOptions()));
    }

}