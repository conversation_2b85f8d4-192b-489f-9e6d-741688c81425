package omni.audit.handler.sample.storage;

import com.quanzhi.omniaudit.sampling.service.SamplingStorageService;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.transform.ApexTransform;
import omni.audit.apex.api.type.ApexRow;

import java.util.*;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:51
 * @description:
 **/
@Slf4j
public class SamplingStorageTransform implements ApexTransform<ApexRow> {

    public static final String PLUGIN_NAME = "SamplingStorage";

    private final SamplingStorageTransformConfig samplingStorageTransformConfig;

    private transient SamplingStorageService samplingStorageService;

    private List<String> outputSampleFieldNames;

    public SamplingStorageTransform(SamplingStorageTransformConfig samplingStorageTransformConfig) {
        this.samplingStorageTransformConfig = samplingStorageTransformConfig;
        Set<String> sampleFieldNames = samplingStorageTransformConfig.getSampleScheme().keySet();
        outputSampleFieldNames = new ArrayList<>(sampleFieldNames);
    }

    @Override
    public void open() {
        this.samplingStorageService = new SamplingStorageService(samplingStorageTransformConfig.getJdbcConfig(),
                                                                 samplingStorageTransformConfig.getMultiDimensionalSamplingConfig(),
                                                                 samplingStorageTransformConfig.getSampleConfig());
    }

    private Map<String, Object> buildSample(Map<String, Object> sourceMap) {
        Map<String, Object> map = new HashMap<>();
        outputSampleFieldNames.forEach(fieldName -> {
            map.put(fieldName, sourceMap.get(fieldName));
        });
        return map;
    }

    @Override
    public ApexRow transform(ApexRow row) {
        Map<String, Object> map = row.toMap();
        Map<String, Object> sample = buildSample(map);
        boolean needSampling = samplingStorageService.checkSample(map, sample);
        if (needSampling) {
            row.set("sample", sample);
        }
        return row;
    }

}