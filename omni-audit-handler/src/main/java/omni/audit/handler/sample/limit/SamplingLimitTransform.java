package omni.audit.handler.sample.limit;

import com.quanzhi.omniaudit.sampling.manager.CacheManager;
import com.quanzhi.omniaudit.sampling.manager.ICacheManager;
import com.quanzhi.omniaudit.sampling.moudle.SamplingConfig;
import com.quanzhi.omniaudit.sampling.service.ISamplingService;
import com.quanzhi.omniaudit.sampling.service.strategy.DefaultSamplingStrategy;
import com.quanzhi.omniaudit.sampling.service.strategy.MemorySamplingStrategy;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.transform.ApexTransform;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.common.client.redis.RedisConfig;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:50
 * @description:
 **/
@Slf4j
public class SamplingLimitTransform implements ApexTransform<ApexRow> {

    public static final String PLUGIN_NAME = "SamplingLimit";

    private final SamplingLimitTransformConfig samplingLimitTransformConfig;

    private transient ISamplingService samplingService;

    public SamplingLimitTransform(SamplingLimitTransformConfig samplingLimitTransformConfig) {
        this.samplingLimitTransformConfig = samplingLimitTransformConfig;
    }

    @Override
    public void open() {
        ICacheManager cacheManager = new CacheManager();
        SamplingConfig samplingConfig = new SamplingConfig();
        RedisConfig redisConfig = samplingLimitTransformConfig.getRedisConfig();
        samplingConfig.setRedisConfig(redisConfig);
        try {
            this.samplingService = new DefaultSamplingStrategy(cacheManager, samplingConfig);
        } catch (Exception e) {
            this.samplingService = new MemorySamplingStrategy(cacheManager, samplingConfig);
        }
    }

    @Override
    public ApexRow transform(ApexRow row) {
        String key = splicingKey(row);
        if (StringUtils.isEmpty(key)) {
            log.error("splicing sample key is null");
            return row;
        }
        boolean sampling = samplingService.sampling(key);
        if (sampling) {
            row.set("samplingFlag", true);
        }
        return row;
    }

    private String splicingKey(ApexRow row) {
        Map<String, Object> rowData = row.toMap();
        List<String> samplingKeys = samplingLimitTransformConfig.getSamplingKeys();
        StringBuilder sb = new StringBuilder();
        for (String key : samplingKeys) {
            Object value = rowData.get(key);
            if (value == null) {
                continue;
            }
            sb.append(value).append("_");
        }
        return sb.length() > 0 ? sb.deleteCharAt(sb.length() - 1).toString() : "";
    }

}