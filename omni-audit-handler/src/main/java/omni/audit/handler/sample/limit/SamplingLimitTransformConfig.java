package omni.audit.handler.sample.limit;

import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import omni.audit.apex.api.config.*;
/**
 * <AUTHOR>
 * create at 2025/5/13 11:32
 * @description:
 **/
@Getter
@Setter
public class SamplingLimitTransformConfig implements Serializable {

    public static final Option<List<String>> SAMPLING_KEYS=
            Options.key("sampling_keys")
                    .listType()
                    .noDefaultValue()
                    .withDescription("Which fields to sample from");

    private List<String> samplingKeys;

    public static SamplingLimitTransformConfig of(ReadonlyConfig config) {
        List<String> keys=new ArrayList<>();
        Optional<List<String>> optional = config.getOptional(SAMPLING_KEYS);
        if(optional.isPresent()){
            keys.addAll(config.get(SAMPLING_KEYS));
        }
        SamplingLimitTransformConfig samplingLimitTransformConfig = new SamplingLimitTransformConfig();
        samplingLimitTransformConfig.setSamplingKeys(keys);
        return samplingLimitTransformConfig;
    }

}