package omni.audit.handler.sample.limit;

import lombok.Getter;
import lombok.Setter;
import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.common.client.redis.RedisConfig;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:32
 * @description:
 **/
@Getter
@Setter
public class SamplingLimitTransformConfig implements Serializable {

    public static final Option<List<String>> SAMPLING_KEYS =
            Options.key("sampling_keys")
                   .listType()
                   .noDefaultValue()
                   .withDescription("Which fields to sample from");

    private List<String> samplingKeys;

    private RedisConfig redisConfig;

    public static SamplingLimitTransformConfig of(ReadonlyConfig config) {
        List<String> keys = new ArrayList<>();
        Optional<List<String>> optional = config.getOptional(SAMPLING_KEYS);
        if (optional.isPresent()) {
            keys.addAll(config.get(SAMPLING_KEYS));
        }
        SamplingLimitTransformConfig samplingLimitTransformConfig = new SamplingLimitTransformConfig();
        samplingLimitTransformConfig.setSamplingKeys(keys);
        samplingLimitTransformConfig.setRedisConfig(buildRedisConfig(config));
        return samplingLimitTransformConfig;
    }

    private static RedisConfig buildRedisConfig(ReadonlyConfig config) {
        RedisConfig redisConfig = RedisConfig.builder()
                                             .host("*************")
                                             .port(6380)
                                             .password("qz@123#pwd")
                                             .timeout(3000)
                                             .build();
        return redisConfig;
    }

}