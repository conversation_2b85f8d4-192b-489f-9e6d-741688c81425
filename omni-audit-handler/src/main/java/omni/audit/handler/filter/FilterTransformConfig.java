package omni.audit.handler.filter;

import lombok.Getter;
import lombok.Setter;
import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;
import omni.audit.apex.api.config.ReadonlyConfig;

import java.io.Serializable;
import java.util.Optional;

@Setter
@Getter
public class FilterTransformConfig implements Serializable {

    private String id;

    private String username;

    private String password;

    private String url;

    private String scheme;

    public static final Option<String> EVENT_ID =
            Options.key("id").stringType().noDefaultValue()
                   .withDescription("Specify the field append between input and output");

    public static final Option<String> USERNAME =
            Options.key("username").stringType().defaultValue("postgres")
                   .withDescription("Specify the field append between input and output");

    public static final Option<String> PASSWORD =
            Options.key("password").stringType().noDefaultValue()
                   .withDescription("Specify the field append between input and output");

    public static final Option<String> URL =
            Options.key("url").stringType().noDefaultValue()
                   .withDescription("Specify the field append between input and output");

    public static final Option<String> SCHEME =
            Options.key("scheme").stringType().defaultValue("public")
                   .withDescription("Specify the field append between input and output");

    public static FilterTransformConfig of(ReadonlyConfig config) {
        FilterTransformConfig filterTransformConfig = new FilterTransformConfig();
        Optional<String> optional = config.getOptional(EVENT_ID);
        optional.ifPresent(filterTransformConfig::setId);
        Optional<String> username = config.getOptional(USERNAME);
        username.ifPresent(filterTransformConfig::setUsername);
        Optional<String> password = config.getOptional(PASSWORD);
        password.ifPresent(filterTransformConfig::setPassword);
        Optional<String> url = config.getOptional(URL);
        url.ifPresent(filterTransformConfig::setUrl);
        Optional<String> scheme = config.getOptional(SCHEME);
        scheme.ifPresent(filterTransformConfig::setScheme);
        return filterTransformConfig;
    }

}
