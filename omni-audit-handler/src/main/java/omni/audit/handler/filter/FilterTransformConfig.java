package omni.audit.handler.filter;

import lombok.Getter;
import lombok.Setter;
import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;
import omni.audit.apex.api.config.ReadonlyConfig;

import java.io.Serializable;
import java.util.Optional;

@Setter
@Getter
public class FilterTransformConfig implements Serializable {

    private String id;

    public static final Option<String> EVENT_ID =
            Options.key("id").stringType().noDefaultValue().withDescription("Specify the field append between input and output");

    public static FilterTransformConfig of(ReadonlyConfig config) {
        FilterTransformConfig filterTransformConfig = new FilterTransformConfig();
        Optional<String> optional = config.getOptional(EVENT_ID);
        optional.ifPresent(filterTransformConfig::setId);
        return filterTransformConfig;
    }
}
