package omni.audit.handler.filter;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexTransformFactory;
import omni.audit.apex.api.factory.ApexTransformFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.transform.ApexTransform;
import omni.audit.apex.api.type.ApexRow;

@AutoService(Factory.class)
public class FilterTransformFactory implements ApexTransformFactory {

    @Override
    public String factoryIdentifier() {
        return FilterTransform.PLUGIN_NAME;
    }

    @Override
    public <T> ApexTransform<ApexRow> createTransform(ApexTransformFactoryContext context) {
        return new FilterTransform(FilterTransformConfig.of(context.getOptions()));
    }

}
