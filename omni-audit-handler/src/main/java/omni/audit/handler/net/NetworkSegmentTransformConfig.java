package omni.audit.handler.net;
import java.util.Map;
import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;

public class NetworkSegmentTransformConfig {
    public static final Option<Map<String, String>> NET_PARAM =
            Options.key("net")
                    .mapType()
                    .noDefaultValue()
                    .withDescription("label param");
}
