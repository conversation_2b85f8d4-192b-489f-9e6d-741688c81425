package omni.audit.handler.net;

import com.fasterxml.jackson.core.type.TypeReference;
import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;

import java.util.Map;

public class NetworkSegmentTransformConfig {

    public static final Option<Map<String, String>> NET_PARAM =
            Options.key("net")
                   .mapType()
                   .noDefaultValue()
                   .withDescription("label param");

    public static final Option<Map<String, Object>> PROPERTIES_PARAM =
            Options.key("properties")
                   .type(new TypeReference<Map<String, Object>>() {

                   })
                   .noDefaultValue()
                   .withDescription("properties");

}
