package omni.audit.handler.net;

import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.transform.AbstractApexTransform;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.common.util.HandleUtil;
import omni.audit.network.segment.handle.processors.NetWorkHandle;

import java.util.Map;

@Slf4j
public class NetworkSegmentTransform extends AbstractApexTransform {

    public static final String PLUGIN_NAME = "NetworkSegment";

    private transient NetWorkHandle netWorkHandle;

    private final Map<String, String> netParam;

    private final Map<String, Object> paramMap;

    public NetworkSegmentTransform(ReadonlyConfig readonlyConfig) {
        this.netParam = readonlyConfig.get(NetworkSegmentTransformConfig.NET_PARAM);
        this.paramMap = readonlyConfig.get(NetworkSegmentTransformConfig.PROPERTIES_PARAM);
    }

    @Override
    public void open() {
        netWorkHandle = new NetWorkHandle(paramMap);
    }

    @Override
    public ApexRow transform(ApexRow row) {
        Map<String, Object> eventMap = row.toMap();
        Object netSrcIp = HandleUtil.getMapField(eventMap, netParam.get("netSrcIp"));
        Object netDstIp = HandleUtil.getMapField(eventMap, netParam.get("netDstIp"));
        if (netSrcIp != null) {
            HandleUtil.putMapField(eventMap, netParam.get("visitDomains"), handleNetWork((String) netSrcIp));
        }
        if (netDstIp != null) {
            HandleUtil.putMapField(eventMap, netParam.get("deployDomains"), handleNetWork((String) netDstIp));
        }
        return row;
    }

    private Integer[] handleNetWork(String ip) {
        return netWorkHandle.handle(ip);
    }

    @Override
    public void close() {
        super.close();
    }

}
