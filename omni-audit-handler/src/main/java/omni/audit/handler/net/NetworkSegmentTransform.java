package omni.audit.handler.net;

import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.transform.AbstractApexTransform;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.common.util.HandleUtil;
import omni.audit.network.segment.handle.entity.NetworkDomain;
import omni.audit.network.segment.handle.processors.NetWorkHandle;

import java.util.List;
import java.util.Map;

public class NetworkSegmentTransform extends AbstractApexTransform {
    public static final String PLUGIN_NAME = "NetworkSegment";
    private transient NetWorkHandle netWorkHandle;
    private final Map<String, String> netParam;

    public NetworkSegmentTransform(ReadonlyConfig readonlyConfig) {
        this.netParam = readonlyConfig.get(NetworkSegmentTransformConfig.NET_PARAM);
    }

    @Override
    public void open() {
        netWorkHandle = new NetWorkHandle();
    }

    @Override
    public ApexRow transform(ApexRow row) {
        Map<String, Object> eventMap = row.toMap();
        Object netSrcIp = HandleUtil.getMapField(eventMap, netParam.get("netSrcIp"));
        Object netDstIp = HandleUtil.getMapField(eventMap, netParam.get("netDstIp"));
        if (netSrcIp != null) {
            Integer[] netWork = handleNetWork((String) netSrcIp);
            HandleUtil.putMapField(eventMap, netParam.get("visitDomains"), netWork);
        }
        if (netDstIp != null) {
            Integer[] netWork = handleNetWork((String) netDstIp);
            HandleUtil.putMapField(eventMap, netParam.get("visitDomains"), netWork);
        }
        return row;
    }

    private Integer[] handleNetWork(String ip){
        Map<String, Object> ipRes = netWorkHandle.handle(ip);
        if (ipRes != null && ipRes.get("network") != null ) {
            List<NetworkDomain> network = (List<NetworkDomain>) ipRes.get("network");
            Integer[] array = network.stream().map(s->{
                String id = s.getId();
                if (id.equals("局域网-其他")){
                    return 1;
                }else if (id.equals("局域网-局域网")){
                    return 2;
                }else if (id.equals("互联网-局域网")){
                    return 3;
                }else if (id.equals("互联网-互联网")){
                    return 4;
                }else if (id.equals("互联网-其他")){
                    return 5;
                }else if (id.equals("局域网-其他")){
                    return 6;
                }else {
                    return 0;
                }
            }).toArray(Integer[]::new);
            return array;
        }
        return new Integer[]{};
    }

    @Override
    public void close() {
        super.close();
    }
}
