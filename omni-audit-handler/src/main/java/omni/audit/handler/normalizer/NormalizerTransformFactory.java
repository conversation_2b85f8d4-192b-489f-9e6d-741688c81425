package omni.audit.handler.normalizer;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexTransformFactory;
import omni.audit.apex.api.factory.ApexTransformFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.transform.ApexTransform;

/**
 * <AUTHOR>
 * @class NormalizerTransformFactory
 * @created 2025/6/27 10:55
 * @desc
 **/
@AutoService(Factory.class)
public class NormalizerTransformFactory implements ApexTransformFactory {

    @Override
    public ApexTransform createTransform(ApexTransformFactoryContext context) {
        return new NormalizerTransform(NormalizerTransformConfig.of(context.getOptions()));
    }

    @Override
    public String factoryIdentifier() {
        return "normalizer";
    }

}
