package omni.audit.handler.normalizer;

import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.transform.AbstractApexTransform;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.api.util.ApexRowUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @class NormalizerTransform
 * @created 2025/6/27 10:55
 * @desc
 **/
@Slf4j
public class NormalizerTransform extends AbstractApexTransform {

    private NormalizerTransformConfig config;

    public NormalizerTransform(NormalizerTransformConfig options) {
        this.config = options;
    }

    @Override
    public ApexRow transform(ApexRow row) {
        ApexRow newRow = ApexRowUtil.create();
        try {
            for (Map.Entry<String, String> kv : config.getFields().entrySet()) {
                String value = kv.getValue();
                if (value.contains(".")) {
                    String[] paths = value.split("\\.");
                    Map map = row.toMap();

                    for (int k = 0; k < paths.length; k++) {
                        if (k == 0) {
                            map = row.get(paths[k], Map.class);
                            continue;
                        }
                        if (k == paths.length - 1) {
                            newRow.set(kv.getKey(), map.get(paths[k]));
                        } else {
                            map = (Map) map.get(paths[k]);
                        }

                    }
                } else {
                    newRow.set(kv.getKey(), row.get(kv.getValue()));
                }
            }
        } catch (Exception e) {
            log.error("transform error", e);
            return null;
        }

        return newRow;
    }
}
