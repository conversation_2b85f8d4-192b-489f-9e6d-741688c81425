package omni.audit.handler.normalizer;

import lombok.Getter;
import lombok.Setter;
import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;
import omni.audit.apex.api.config.ReadonlyConfig;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @class NormalizerTransformConfig
 * @created 2025/6/27 10:55
 * @desc
 **/
@Getter
@Setter
public class NormalizerTransformConfig {

    public static final Option<Map<String, String>> FIELDS =
            Options.key("fields")
                   .mapType()
                   .noDefaultValue()
                   .withDescription("Specify the field append between input and output");

    public static final Option<Map<String, String>> OUTPUT_SCHEMA =
            Options.key("output_schema")
                    .mapType()
                    .noDefaultValue()
                    .withDescription("Specify the output schema");

    private Map<String, String> fields;

    private Integer append;

    private Map<String, String> outputSchema;

    public static NormalizerTransformConfig of(ReadonlyConfig config) {
        LinkedHashMap<String, String> fields = new LinkedHashMap<>();
        Optional<Map<String, String>> fieldsOption = config.getOptional(FIELDS);
        if (fieldsOption.isPresent()) {
            fields.putAll(config.get(FIELDS));
        }

        LinkedHashMap<String, String> outputSchema = new LinkedHashMap<>();
        Optional<Map<String, String>> outputSchemaOption = config.getOptional(OUTPUT_SCHEMA);
        if (outputSchemaOption.isPresent()) {
            outputSchema.putAll(config.get(OUTPUT_SCHEMA));
        }

        NormalizerTransformConfig transformConfig = new NormalizerTransformConfig();
        transformConfig.setFields(fields);
        transformConfig.setOutputSchema(outputSchema);
        transformConfig.setAppend(outputSchema.size() - fields.size());
        return transformConfig;
    }
}
