package omni.audit.handler.weakness;

import com.quanzhi.audit.weakness.constant.WeaknessConstant;
import com.quanzhi.audit.weakness.service.WeaknessService;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.transform.ApexTransform;
import omni.audit.apex.api.type.ApexRow;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:15
 * @description:
 **/
@Slf4j
public class WeaknessTransform implements ApexTransform<ApexRow> {
    public static final String PLUGIN_NAME = "Weakness";
    private final WeaknessTransformConfig config;
    private transient WeaknessService weaknessService;

    public WeaknessTransform(WeaknessTransformConfig weaknessTransformConfig) {
        this.config = weaknessTransformConfig;
        LinkedHashMap<String, String> fieldMap = weaknessTransformConfig.getFieldMap();
        config.getWeaknessConfig().put(WeaknessConstant.FIELD_MAP, fieldMap);

        LinkedHashMap<String, String> outputFieldMap = weaknessTransformConfig.getOutputFieldMap();
        config.getWeaknessConfig().put(WeaknessConstant.OUTPUT_FIELD_MAP, outputFieldMap);
    }

    @Override
    public void open() {
        this.weaknessService = new WeaknessService(config.getJdbcConfig(), config.getWeaknessConfig());
    }

    private void tryOpen() {
        if (weaknessService == null) {
            open();
        }
    }
    @Override
    public ApexRow transform(ApexRow row) {
        tryOpen();
        Map<String, Object> map = row.toMap();
        List<Map<String, Object>> weakness = weaknessService.process(map);
        if (!weakness.isEmpty()) {
            row.set("weakness", weakness);
        }
        return row;
    }
}