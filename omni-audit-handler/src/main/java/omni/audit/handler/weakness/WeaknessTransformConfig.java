package omni.audit.handler.weakness;

import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import omni.audit.apex.api.config.*;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:32
 * @description:
 **/
@Getter
@Setter
public class WeaknessTransformConfig implements Serializable {


    public static final Option<Map<String, String>> WEAKNESS_CONFIG =
            Options.key("weakness_config")
                    .mapType()
                    .noDefaultValue()
                    .withDescription(
                            "Specify the field copy relationship between input and output");



    public static final Option<Map<String, String>> WEAKNESS_SCHEME =
            Options.key("weakness_scheme")
                    .mapType()
                    .noDefaultValue()
                    .withDescription(
                            "weakness table scheme info");


    public static final Option<Map<String, String>> FIELD_MAP =
            Options.key("field_map")
                    .mapType()
                    .noDefaultValue()
                    .withDescription(
                            "weakness db table scheme info");

    public static final Option<Map<String, String>> OUTPUT_FIELD_MAP =
            Options.key("output_field_map")
                    .mapType()
                    .noDefaultValue()
                    .withDescription(
                            "weakness memory output field");


    private Properties weaknessConfig;
    private JdbcConfig jdbcConfig;
    private LinkedHashMap<String, String> weaknessScheme;
    private LinkedHashMap<String, String> fieldMap;
    private LinkedHashMap<String, String> outputFieldMap;


    public static WeaknessTransformConfig of(ReadonlyConfig config) {
        Properties weaknessConfig = new Properties();
        Optional<Map<String, String>> optional = config.getOptional(WEAKNESS_CONFIG);
        if (optional.isPresent()) {
            optional.get().forEach((k, v) -> {
                weaknessConfig.setProperty(k, v);
            });
        }
        LinkedHashMap<String, String> weaknessScheme = new LinkedHashMap<>();
        Optional<Map<String, String>> o2 = config.getOptional(WEAKNESS_SCHEME);
        o2.ifPresent(weaknessScheme::putAll);


        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
        Optional<Map<String, String>> o3 = config.getOptional(FIELD_MAP);
        o3.ifPresent(fieldMap::putAll);


        LinkedHashMap<String, String> outputFieldMap = new LinkedHashMap<>();
        Optional<Map<String, String>> o4 = config.getOptional(OUTPUT_FIELD_MAP);
        o4.ifPresent(outputFieldMap::putAll);

        WeaknessTransformConfig weaknessTransformConfig = new WeaknessTransformConfig();
        weaknessTransformConfig.setWeaknessConfig(weaknessConfig);
        weaknessTransformConfig.setWeaknessScheme(weaknessScheme);
        weaknessTransformConfig.setJdbcConfig(buildJdbcConfig(config));
        weaknessTransformConfig.setFieldMap(fieldMap);
        weaknessTransformConfig.setOutputFieldMap(outputFieldMap);
        return weaknessTransformConfig;
    }

    private static JdbcConfig buildJdbcConfig(ReadonlyConfig config) {
        JdbcConfig jdbcConfig = JdbcConfig.builder().username("postgres").jdbcUrl("*********************************************")
                .password("password123")
                .driverClassName("org.postgresql.Driver")
                .build();
        return jdbcConfig;
    }
}