package omni.audit.handler.asset;

import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.transform.AbstractApexTransform;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.asset.handle.entity.HandleMap;
import omni.audit.asset.handle.processors.AssetHandlerInit;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AssetTransform extends AbstractApexTransform {

    public static final String PLUGIN_NAME = "Asset";

    private transient AssetHandlerInit assetHandlerInit;

    private List<String> fieldNames;

    private final Map<String, Object> assetIdParam;

    private final Map<String, Object> assetPropertiesParam;

    private final Map<String, String> assetTimeParam;

    private final List<Map<String, String>> assetLevelParam;

    private final List<Map<String, String>> assetAssignmentParam;

    private final List<String> handleNameList;

    private final Map<String, Map<Integer, String>> fieldNameToIndexMap = new HashMap<>();

    public AssetTransform(ReadonlyConfig readonlyConfig) {
        this.assetIdParam = readonlyConfig.get(AssetTransformConfig.ASSET_ID_PARAM);
        this.assetPropertiesParam = readonlyConfig.get(AssetTransformConfig.ASSET_PROPERTIES_PARAM);
        this.assetTimeParam = readonlyConfig.get(AssetTransformConfig.ASSET_TIME_PARAM);
        this.assetLevelParam = readonlyConfig.get(AssetTransformConfig.ASSET_LEVEL_PARAM);
        this.assetAssignmentParam = readonlyConfig.get(AssetTransformConfig.ASSET_ASSIGNMENT_PARAM);
        this.handleNameList = readonlyConfig.get(AssetTransformConfig.ASSET_HANDLE);
    }

    @Override
    public void open() {
        this.assetHandlerInit = new AssetHandlerInit(handleNameList, assetPropertiesParam);
    }

    @Override
    public ApexRow transform(ApexRow row) {
        Map<String, Object> map = row.toMap();
        HandleMap handleMap = new HandleMap();
        handleMap.setEventMap(map);
        handleMap.setAssetIdParam(assetIdParam);
        handleMap.setAssetTimeParam(assetTimeParam);
        handleMap.setAssetLevelParam(assetLevelParam);
        handleMap.setAssetAssignmentParam(assetAssignmentParam);
        assetHandlerInit.handle(handleMap);
        return row;
    }

    @Override
    public void close() {
        super.close();
    }

}
