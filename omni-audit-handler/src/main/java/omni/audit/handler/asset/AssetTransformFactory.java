package omni.audit.handler.asset;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.factory.ApexTransformFactory;
import omni.audit.apex.api.factory.ApexTransformFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.transform.ApexTransform;
import omni.audit.apex.api.type.ApexRow;

@AutoService(Factory.class)
public class AssetTransformFactory implements ApexTransformFactory {

    @Override
    public String factoryIdentifier() {
        return AssetTransform.PLUGIN_NAME;
    }

    @Override
    public <T> ApexTransform<ApexRow> createTransform(ApexTransformFactoryContext context) {
        ReadonlyConfig readonlyConfig = context.getOptions();
        return new AssetTransform(readonlyConfig);
    }

}
