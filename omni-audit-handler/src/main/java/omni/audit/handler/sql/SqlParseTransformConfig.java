package omni.audit.handler.sql;

import lombok.Getter;
import lombok.Setter;
import omni.audit.apex.api.config.Option;
import omni.audit.apex.api.config.Options;
import omni.audit.apex.api.config.ReadonlyConfig;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

@Setter
@Getter
public class SqlParseTransformConfig implements Serializable {

    private String sql;

    private LinkedHashMap<String, String> tableScheme;

    private LinkedHashMap<String, String> tableInfo;

    private String tables;

    private LinkedHashMap<String, String> columnInfo;

    private String columns;

    private String dbType;

    private String optionMethod;

    /**
     * 原字段
     */
    public static final Option<String> SQL =
            Options.key("sql").stringType().noDefaultValue()
                   .withDescription("Specify the field append between input and output");

    public static final Option<String> OPTION_METHOD =
            Options.key("option_method").stringType().noDefaultValue()
                   .withDescription("Specify the field append between input and output");

    public static final Option<String> DB_TYPE =
            Options.key("dbType").stringType().noDefaultValue()
                   .withDescription("Specify the field append between input and output");

    public static final Option<String> TABLES =
            Options.key("tables").stringType().noDefaultValue()
                   .withDescription("Specify the field append between input and output");

    public static final Option<Map<String, String>> TABLE_SCHEME =
            Options.key("tableScheme").mapType().noDefaultValue()
                   .withDescription("Specify the field append between input and output");

    public static final Option<Map<String, String>> TABLE_INFO =
            Options.key("tableInfo").mapType().noDefaultValue()
                   .withDescription("Specify the field append between input and output");

    public static final Option<String> COLUMNS =
            Options.key("columns").stringType().noDefaultValue()
                   .withDescription("Specify the field append between input and output");

    public static final Option<Map<String, String>> COLUMN_INFO =
            Options.key("columnInfo").mapType().noDefaultValue()
                   .withDescription("Specify the field append between input and output");

    public static SqlParseTransformConfig of(ReadonlyConfig config) {
        SqlParseTransformConfig sqlParseTransformConfig = new SqlParseTransformConfig();
        Optional<String> optionalSql = config.getOptional(SQL);
        optionalSql.ifPresent(sqlParseTransformConfig::setSql);
        Optional<String> optionMethod = config.getOptional(OPTION_METHOD);
        optionMethod.ifPresent(sqlParseTransformConfig::setOptionMethod);
        Optional<String> dbType = config.getOptional(DB_TYPE);
        dbType.ifPresent(sqlParseTransformConfig::setDbType);
        Optional<String> optionalTables = config.getOptional(TABLES);
        optionalTables.ifPresent(sqlParseTransformConfig::setTables);
        LinkedHashMap<String, String> tableScheme = new LinkedHashMap<>();
        Optional<Map<String, String>> optional = config.getOptional(TABLE_SCHEME);
        optional.ifPresent(tableScheme::putAll);
        sqlParseTransformConfig.setTableScheme(tableScheme);
        LinkedHashMap<String, String> tableInfo = new LinkedHashMap<>();
        Optional<Map<String, String>> optionalTableInfo = config.getOptional(TABLE_INFO);
        optionalTableInfo.ifPresent(tableInfo::putAll);
        sqlParseTransformConfig.setTableInfo(tableInfo);

        LinkedHashMap<String, String> columnInfo = new LinkedHashMap<>();
        Optional<Map<String, String>> optionalColumnInfo = config.getOptional(COLUMN_INFO);
        optionalColumnInfo.ifPresent(columnInfo::putAll);
        sqlParseTransformConfig.setColumnInfo(columnInfo);

        Optional<String> optionalColumns = config.getOptional(COLUMNS);
        optionalColumns.ifPresent(sqlParseTransformConfig::setColumns);
        return sqlParseTransformConfig;
    }

}
