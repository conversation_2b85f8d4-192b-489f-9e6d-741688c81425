package omni.audit.handler.sql;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.quanzhi.omni.audit.parse.SqlParseProcessor;
import com.quanzhi.sqlparser.context.TableInfo;
import com.quanzhi.sqlparser.view.SqlParserResult;
import omni.audit.apex.api.transform.ApexTransform;
import omni.audit.apex.api.type.ApexRow;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class SqlParseTransform implements ApexTransform<ApexRow> {
    public static String PLUGIN_NAME = "SqlParse";
    private final SqlParseTransformConfig sqlParseTransformConfig;

    private final SqlParseProcessor sqlParseProcessor;

    public SqlParseTransform(SqlParseTransformConfig sqlParseTransformConfig) {
        this.sqlParseTransformConfig = sqlParseTransformConfig;
        this.sqlParseProcessor = new SqlParseProcessor();
    }

    @Override
    public void open() {
    }

    private void tryOpen() {
        if (sqlParseProcessor == null) {
            open();
        }
    }

    @Override
    public ApexRow transform(ApexRow row) {
//        tryOpen();
        String sql = row.get(sqlParseTransformConfig.getSql(),String.class);
        String configTables = sqlParseTransformConfig.getTables();
        String dbType = row.get(sqlParseTransformConfig.getDbType(),String.class);
        LinkedHashMap<String, String> tableScheme = sqlParseTransformConfig.getTableScheme();
        LinkedHashMap<String, String> tableInfo = sqlParseTransformConfig.getTableInfo();
        SqlParserResult sqlParserResult = sqlParseProcessor.process(sql,dbType);
        if (sqlParserResult != null && sqlParserResult.getAllTableInfo() != null && sqlParserResult.getAllTableInfo().getTables() != null && !sqlParserResult.getAllTableInfo().getTables().isEmpty()) {
            Map<Integer,Map<String,Object>> tables = new HashMap<>();
            int size = sqlParserResult.getAllTableInfo().getTables().size();
            for (int j = 0; j < size; j++) {
                TableInfo info = sqlParserResult.getAllTableInfo().getTables().get(j);
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> infoMap = mapper.convertValue(info, Map.class);
                if(info.getAllColumnNames() != null && !info.getAllColumnNames().isEmpty()){
                    List<String> columns = info.getAllColumnNames().stream().filter(i -> i != null && !"*".equals(i)).collect(Collectors.toList());
                    infoMap.put("columns", columns);
                }
                //TODO 处理sqlParserResult
                Map<String,Object> tableMap = new HashMap<>();
                tableInfo.forEach((k, v) -> {
                    tableMap.put(k, infoMap.get(v));
                });
                tables.put(j, tableMap);
            }
            row.set(configTables, tables);
        }
        return row;
    }

    @Override
    public void close() {
    }
}
