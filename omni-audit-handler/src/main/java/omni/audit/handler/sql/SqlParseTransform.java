package omni.audit.handler.sql;

import com.quanzhi.omni.audit.parse.SqlParseProcessor;
import com.quanzhi.sqlparser.context.TableInfo;
import com.quanzhi.sqlparser.view.SqlParserResult;
import omni.audit.apex.api.transform.ApexTransform;
import omni.audit.apex.api.type.ApexRow;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class SqlParseTransform implements ApexTransform<ApexRow> {

    public static String PLUGIN_NAME = "SqlParse";

    private final SqlParseTransformConfig sqlParseTransformConfig;

    private final SqlParseProcessor sqlParseProcessor;

    public SqlParseTransform(SqlParseTransformConfig sqlParseTransformConfig) {
        this.sqlParseTransformConfig = sqlParseTransformConfig;
        this.sqlParseProcessor = new SqlParseProcessor();
    }

    @Override
    public void open() {
    }

    private void tryOpen() {
        if (sqlParseProcessor == null) {
            open();
        }
    }

    @Override
    public ApexRow transform(ApexRow row) {
        // tryOpen();
        String sql = row.get(sqlParseTransformConfig.getSql(), String.class);
        String configTables = sqlParseTransformConfig.getTables();
        String dbType = row.get(sqlParseTransformConfig.getDbType(), String.class);
        String optionMethod = sqlParseTransformConfig.getOptionMethod();
        // LinkedHashMap<String, String> tableScheme = sqlParseTransformConfig.getTableScheme();
        // LinkedHashMap<String, String> tableInfo = sqlParseTransformConfig.getTableInfo();
        SqlParserResult sqlParserResult = sqlParseProcessor.process(sql, dbType);

        String configColumns = sqlParseTransformConfig.getColumns();
        // LinkedHashMap<String, String> configColumnInfo = sqlParseTransformConfig.getColumnInfo();
        if (sqlParserResult != null && sqlParserResult.getAllTableInfo() != null
            && CollectionUtils.isNotEmpty(sqlParserResult.getAllTableInfo().getTables())) {
            List<Map<String, Object>> tables = new ArrayList<>();
            List<Map<String, Object>> columnInfos = new ArrayList<>();
            int size = sqlParserResult.getAllTableInfo().getTables().size();
            String queryType = sqlParserResult.getQueryType();
            if (queryType != null) {
                row.set(optionMethod, queryType);
            }
            for (int j = 0; j < size; j++) {
                TableInfo info = sqlParserResult.getAllTableInfo().getTables().get(j);
                if (info.getAllColumnNames() != null && !info.getAllColumnNames().isEmpty()) {
                    List<String> columns = info.getAllColumnNames().stream()
                                               .filter(i -> i != null && !"*".equals(i.trim()) && !i.trim().isEmpty())
                                               .collect(Collectors.toList());
                    for (String col : columns) {
                        Map<String, Object> columnInfoMap = new HashMap<>();
                        columnInfoMap.put("name", col);
                        columnInfoMap.put("tableName", info.getName());
                        columnInfos.add(columnInfoMap);
                    }
                }
                // TODO 处理sqlParserResult
                Map<String, Object> tableInfoMap = new HashMap<>();
                tableInfoMap.put("name", info.getName());
                tables.add(tableInfoMap);
            }
            row.set(configTables, tables);
            row.set(configColumns, columnInfos);
        }
        return row;
    }

    @Override
    public void close() {
    }

}
