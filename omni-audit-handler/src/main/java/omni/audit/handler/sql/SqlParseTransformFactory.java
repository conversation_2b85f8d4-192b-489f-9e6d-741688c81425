package omni.audit.handler.sql;

import com.google.auto.service.AutoService;
import omni.audit.apex.api.factory.ApexTransformFactory;
import omni.audit.apex.api.factory.ApexTransformFactoryContext;
import omni.audit.apex.api.factory.Factory;
import omni.audit.apex.api.transform.ApexTransform;
import omni.audit.apex.api.type.ApexRow;

@AutoService(Factory.class)
public class SqlParseTransformFactory implements ApexTransformFactory {

    @Override
    public String factoryIdentifier() {
        return SqlParseTransform.PLUGIN_NAME;
    }

    @Override
    public <T> ApexTransform<ApexRow> createTransform(ApexTransformFactoryContext context) {
        return new SqlParseTransform(SqlParseTransformConfig.of(context.getOptions()));
    }

}
