env {
  parallelism = 1
  job.name = "oad-handler"
  job.mode = "debug"
}

source {
  kafka {
    parallelism = 1
    topic = "DBEvent"
    bootstrap.servers = "*************:9094"
    consumer.group = "oad-handler"
    kafka.config = {
      max.poll.records = 100
      auto.offset.reset = "earliest"
    }
    plugin_output = "db_event"
    format = json
  }
}

transform {
  Normalizer {
    plugin_input = "db_event"
    plugin_output = "db_event_flatten"

    fields = {
      timestamp = "meta.tm"
      type = "meta.type"
      metaAppName = "meta.app_name"
      metaServerVersion = "meta.server_version"
      netSrcIp = "net.src_ip"
      netSrcPort = "net.src_port"
      netDstIp = "net.dst_ip"
      netDstPort = "net.dst_port"
      netFlowSource = "net.flow_source"
      mac = "mac.mac"
      eventId = "unique_id.event_id"
      reqDbName = "req.db_name"
      reqDbUser = "req.db_user"
      reqDbPassword = "req.db_password"
      reqSqlCmdType = "req.sql_cmd_type"
      reqSql = "req.sql"
      rspStatus = "rsp.status"
      rspStartTime = "rsp.start_time"
      rspCloseTime = "rsp.close_time"
      rspRowCount = "rsp.row_count"
      rspResult = "rsp.result"
    }

    output_schema = {
      timestamp = bigint
      type = string
      metaAppName = string
      metaServerVersion = string
      netSrcIp = string
      netSrcPort = int
      netDstIp = string
      netDstPort = int
      netFlowSource = string
      mac = string
      eventId = string
      reqDbName = string
      reqDbUser = string
      reqDbPassword = string
      reqSqlCmdType = string
      reqSql = string
      rspStatus = int
      rspStartTime = bigint
      rspCloseTime = bigint
      rspRowCount = int
      rspResult = string
      srvAddress = string
      srvName = string
      srvType = string
      srvLevel = string
      srvRiskLevel = string
      accessDomains = "array<int>"
      deployDomains = "array<int>"
      srvAccessDomains = "array<int>"
      srvDeployDomains = "array<int>"
      reqDataLabels = "array<int>"
      rspDataLabels = "array<int>"
      srvReqDataLabels = "array<int>"
      srvRspDataLabels = "array<int>"
      srvLifeFlag = "array<int>"
      srvDiscoverTime = bigint
      srvActiveTime = bigint
      srvBizSystem = string
      dbVersion = string
      dbType = string
      dbLevel = string
      dbRiskLevel = string
      dbAccessDomains = "array<int>"
      dbDeployDomains = "array<int>"
      dbReqDataLabels = "array<int>"
      dbRspDataLabels = "array<int>"
      dbBizSystem = string
      dbLifeFlag = "array<int>"
      dbDiscoverTime = bigint
      dbActiveTime = bigint
      eventTypes = "array<int>"
      optionMethod = string
      rspTime = bigint
      tables = "map<int, {\"name\":\"string\",\"dbType\":\"string\",\"dbVersion\":\"string\",\"level\":\"string\",\"riskLevel\":\"string\",\"dbName\":\"string\",\"srvAddress\":\"string\",\"srvName\":\"string\",\"fieldCount\":\"int\",\"bizSystem\":\"string\",\"accessDomains\":\"array<string>\",\"deployDomains\":\"array<string>\",\"reqDataLabels\":\"array<string>\",\"rspDataLabels\":\"array<string>\",\"lifeFlag\":\"array<string>\",\"discoverTime\":\"bigint\",\"activeTime\":\"bigint\",\"featureLabels\":\"array<int>\",\"tableId\":\"string\"}>"
      weaknesses = "map<int,{\"name\":\"string\",\"type\":\"string\",\"level\":\"int\",\"state\":\"string\",\"discoverTime\":\"bigint\",\"activeTime\":\"bigint\",\"srvId\":\"string\",\"dbId\":\"string\",\"tableId\":\"string\",\"weaknessId\":\"int\",\"operationId\":\"string\"}>"
      srvId = string
      dbId = string
      sample ="map<int,{\"eventId\":\"string\",\"timestamp\":\"bigint\",\"netSrcIp\":\"string\",\"netSrcPort\":\"int\",\"netDstIp\":\"string\",\"netDstPort\":\"int\",\"netFlowSource\":\"string\",\"mac\":\"string\",\"reqDbName\":\"string\",\"reqDbUser\":\"string\",\"reqDbPassword\":\"string\",\"reqSql\":\"string\",\"rspStatus\":\"int\",\"rspStartTime\":\"bigint\",\"rspCloseTime\":\"bigint\",\"rspRowCount\":\"int\",\"rspResult\":\"string\",\"srvId\":\"string\",\"dbId\":\"string\",\"accessDomains\":\"array<int>\",\"deployDomains\":\"array<int>\",\"reqDataLabels\":\"array<int>\",\"rspDataLabels\":\"array<int>\"}>"
    }


  }
  EventFilter {
    id = "eventId"
    url = "***********************************************"
    username = "postgres"
    password = "aXvKfoAeSARQTPlx"
    scheme = "public"
  }
  SamplingLimit {
    sampling_keys = ["srvAddress","netDstPort","srvType","reqDbName"]
  }
  SqlParse {
    sql = "reqSql"
    dbType = "type"
    tables = "tables"
    option_method = "optionMethod"
    columns = "columns"
    columnInfo = {
      name = "name"
      tableName = "tableName"
    }
    tableInfo = {
      name = "name"
    }
  }
  Asset {
    asset = {
      properties={
        jdbcUrl = "***********************************************"
        jdbcUser = "postgres"
        jdbcPassword = "aXvKfoAeSARQTPlx"
        jdbcSchema = "public"
        levelTableName = "oad_level_rule"
      }
      assignment = [
        {
          sourceKey = "${netSrcIp}:${netSrcPort}"
          targetKey = "srvAddress"
        }
        {
          sourceKey = "jdbc:${type}://${netSrcIp}:${netSrcPort}"
          targetKey = "srvName"
        }
      ]
      time = {
        timestamp = "timestamp"
        activeTime = "activeTime"
        discoverTime = "discoverTime"
      }
      level = [
        {
          assetLevelPath = "srvLevel"
          levelType = "srvLevel"
        }
        {
          assetLevelPath = "dbLevel"
          levelType = "dbLevel"
        }
        {
          assetLevelPath = "tables.tableLevel"
          levelType = "tableLevel"
        }
      ]
      id = {
        db_srv = {
          assetIdPath = "srvAddress"
          assetIdEvent = "srvUid"
        }
        db_assets = {
          assetIdPath = "srvAddress,reqDbName"
          assetIdEvent = "dbUid"
        }
        db_table = {
          assetIdPath = "srvAddress,reqDbName,tables.?.name"
          assetIdEvent = "tableUid"
        }
        db_field = {
          assetIdPath = "srvAddress,reqDbName,columns.?.tableName、name"
          assetIdEvent = "fieldUid"
        }
      }
      handle = [
        AssignmentHandler
        ActiveTimeHandler
        DiscoverTimeHandler
        LevelHandler
        MajorKeyHandler
      ]
    }
  }
  Label {
    properties={
      jdbcUrl = "***********************************************"
      jdbcUser = "postgres"
      jdbcPassword = "aXvKfoAeSARQTPlx"
      jdbcSchema = "public"
      labelTableName = "oad_label_rule"
    }
    label = [
      {
        labelType = "tableLabel"
        labelPath = "tables.?.featureLabels"
      }
      {
        labelType = "dbLabel"
        labelPath = "eventTypes"
      }
    ]
  }
  NetworkSegment {
    properties={
      jdbcUrl = "***********************************************"
      jdbcUser = "postgres"
      jdbcPassword = "aXvKfoAeSARQTPlx"
      jdbcSchema = "public"
      labelTableName = "oad_net"
    }
    net = {
      netSrcIp = "netSrcIp"
      netDstIp = "netDstIp"
      visitDomains = "accessDomains"
      deployDomains = "deployDomains"
    }
  }
  Insert{
    properties={
      jdbcUrl = "***********************************************"
      jdbcUser = "postgres"
      jdbcPassword = "aXvKfoAeSARQTPlx"
      jdbcSchema = "public"
      tableName = "oad_db_srv"
      levelTableName = "oad_level_rule"
      majorKey = "srv_uid"
      idPath = "srvId"
      levelKey = "sensi_level"
      discoverKey = "first_at"
      levelType = "srvLevel"
    }
    field = [
      {
        fieldName = "srv_uid"
        fieldPath = "srvUid"
      }
      {
        fieldName = "addr"
        fieldPath = "srvAddress"
      }
      {
        fieldName = "name"
        fieldPath = "srvName"
      }
      {
        fieldName = "type"
        fieldPath = "type"
      }
      {
        fieldName = "sensi_level"
        fieldPath = "srvLevel"
      }
      {
        fieldName = "access_domains"
        fieldPath = "accessDomains"
      }
      {
        fieldName = "deploy_domains"
        fieldPath = "deployDomains"
      }
      {
        fieldName = "flow_sources"
        fieldPath = "netFlowSource"
      }
      {
        fieldName = "first_at"
        fieldPath = "discoverTime"
      }
      {
        fieldName = "last_at"
        fieldPath = "activeTime"
      }
    ]
  }
  Insert{
    properties={
      jdbcUrl = "***********************************************"
      jdbcUser = "postgres"
      jdbcPassword = "aXvKfoAeSARQTPlx"
      jdbcSchema = "public"
      tableName = "oad_db_assets"
      levelTableName = "oad_level_rule"
      majorKey = "db_uid"
      idPath = "dbId"
      levelKey = "sensi_level"
      discoverKey = "first_at"
      levelType = "dbLevel"
    }
    field = [
      {
        fieldName = "db_uid"
        fieldPath = "dbUid"
      }
      {
        fieldName = "name"
        fieldPath = "reqDbName"
      }
      {
        fieldName = "version"
        fieldPath = "metaServerVersion"
      }
      {
        fieldName = "type"
        fieldPath = "type"
      }
      {
        fieldName = "sensi_level"
        fieldPath = "dbLevel"
      }
      {
        fieldName = "srv_id"
        fieldPath = "srvId"
      }
      {
        fieldName = "first_at"
        fieldPath = "discoverTime"
      }
      {
        fieldName = "last_at"
        fieldPath = "activeTime"
      }
    ]
  }
  Insert{
    properties={
      jdbcUrl = "***********************************************"
      jdbcUser = "postgres"
      jdbcPassword = "aXvKfoAeSARQTPlx"
      jdbcSchema = "public"
      tableName = "oad_db_table"
      levelTableName = "oad_level_rule"
      majorKey = "table_uid"
      idPath = "tables[?].tableId"
      levelKey = "sensi_level"
      discoverKey = "first_at"
      levelType = "tableLevel"
      forTables = "tables"
    }
    field = [
      {
        fieldName = "table_uid"
        fieldPath = "tables[?].tableUid"
      }
      {
        fieldName = "name"
        fieldPath = "tables[?].name"
      }
      {
        fieldName = "db_id"
        fieldPath = "dbId"
      }
      {
        fieldName = "srv_id"
        fieldPath = "srvId"
      }
      {
        fieldName = "sensi_level"
        fieldPath = "tables[?].tableLevel"
      }
      {
        fieldName = "first_at"
        fieldPath = "discoverTime"
      }
      {
        fieldName = "last_at"
        fieldPath = "activeTime"
      }
    ]
  }
  Insert{
    properties={
      jdbcUrl = "***********************************************"
      jdbcUser = "postgres"
      jdbcPassword = "aXvKfoAeSARQTPlx"
      jdbcSchema = "public"
      tableName = "oad_db_field"
      levelTableName = "oad_level_rule"
      majorKey = "field_uid"
      levelKey = "sensi_level"
      discoverKey = "first_at"
      levelType = "fieldLevel"
      forTables = "columns"
    }
    field = [
      {
        fieldName = "field_uid"
        fieldPath = "columns[?].fieldUid"
      }
      {
        fieldName = "name"
        fieldPath = "columns[?].name"
      }
      {
        fieldName = "type"
        fieldPath = "type"
      }
      {
        fieldName = "table_id"
        fieldPath = "columns[?].tableName,tables.?.name,tableId"
      }
      {
        fieldName = "db_id"
        fieldPath = "dbId"
      }
      {
        fieldName = "srv_id"
        fieldPath = "srvId"
      }
      {
        fieldName = "sensi_level"
        fieldPath = "columns[?].fieldLevel"
      }
      {
        fieldName = "first_at"
        fieldPath = "discoverTime"
      }
      {
        fieldName = "last_at"
        fieldPath = "activeTime"
      }
    ]
  }
  Weakness {
    weakness_config = {
      WEAKNESS_RULE_TABLE = "oad_weakness_rule"
      PRIMARY_KEYS=""
    }
    output_field_map = {
      "tableId" = "tableId",
      "srvId" = "srvId",
      "dbId" = "dbId"
    }

    field_map = {
      "weaknessId" = "weakness_id",
      "accessDomains" = "access_domains",
      "deployDomains" = "deploy_domains",
      "operationId" = "operation_id"
    }
    weakness_scheme = {
      name = "string"
      type = "string"
      level = "int"
      state = "string"
      discoverTime = "bigint"
      activeTime = "bigint"
      srvId = "string"
      dbId = "string"
      tableId = "string"
      weaknessId="int"
      operationId="string"
    }
  }
  SamplingStorage {
    sample_config = {
      sample_unique_key="dbId"
    }
    sample_scheme = {
      eventId = string
      timestamp = bigint
      netSrcIp = string
      netSrcPort = int
      netDstIp = string
      netDstPort = int
      netFlowSource = string
      mac = string
      reqDbName = string
      reqDbUser = string
      reqDbPassword = string
      reqSql = string
      rspStatus = int
      rspStartTime = bigint
      rspCloseTime = bigint
      rspRowCount = int
      rspResult = string
      srvId = string
      dbId = string
      accessDomains = "array<int>"
      deployDomains = "array<int>"
      reqDataLabels = "array<int>"
      rspDataLabels = "array<int>"
    }
  }
}

sink {

  clickhouse {
    batch_enable = true
    bulk_size = 20000
    init_seconds = 10
    delay_seconds = 20
    retry_count = 1
    retry_interval = 1
    host = "*************:18123"
    database = audit
    username = audit
    password = X_OD)675()jShf6(
    fields = [
      {
        name = "timestamp"
        type = "UInt64"
        path = "timestamp"
      }
      {
        name = "id"
        type = "String"
        path = "eventId"
      }
      {
        name = "eventTypes"
        type = "Array(UInt8)"
        path = "eventTypes"
      }
      {
        name = "net_dstIp_v4"
        type = "IPv4"
        path = "netDstIp"
      }
      {
        name = "net_dstIp_v6"
        type = "IPv6"
        path = "netDstIp"
      }
      {
        name = "net_dstPort"
        type = "UInt16"
        path = "netDstPort"
      }
      {
        name = "net_srcIp_v4"
        type = "IPv4"
        path = "netSrcIp"
      }
      {
        name = "net_srcIp_v6"
        type = "IPv6"
        path = "netSrcIp"
      }
      {
        name = "net_srcPort"
        type = "UInt16"
        path = "netSrcPort"
      }
      {
        name = "accessDomains"
        type = "Array(UInt8)"
        path = "accessDomains"
      }
      {
        name = "account"
        type = "LowCardinality(String)"
        path = "reqDbUser"
      }
      {
        name = "accountType"
        type = "LowCardinality(String)"
        path = "accountType"
      }
      {
        name = "srvAddress"
        type = "LowCardinality(String)"
        path = "srvAddress"
      }
      {
        name = "srvName"
        type = "LowCardinality(String)"
        path = "srvName"
      }
      {
        name = "srvType"
        type = "LowCardinality(String)"
        path = "srvType"
      }
      {
        name = "srvLevel"
        type = "UInt8"
        path = "srvLevel"
      }
      {
        name = "dbName"
        type = "LowCardinality(String)"
        path = "reqDbName"
      }
      {
        name = "dbType"
        type = "LowCardinality(String)"
        path = "dbType"
      }
      {
        name = "dbLevel"
        type = "UInt8"
        path = "dbLevel"
      }
      {
        name = "tableName"
        type = "LowCardinality(String)"
        path = "tableName"
      }
      {
        name = "tableLevel"
        type = "LowCardinality(String)"
        path = "tableLevel"
      }
      {
        name = "optionMethod"
        type = "LowCardinality(String)"
        path = "optionMethod"
      }
      {
        name = "deployDomains"
        type = "Array(UInt8)"
        path = "deployDomains"
      }
      {
        name = "reqDataLabelIds"
        type = "Array(UInt16)"
        path = "reqDataLabels"
      }
      {
        name = "rspDataLabelIds"
        type = "Array(UInt16)"
        path = "rspDataLabels"
      }
      {
        name = "rspTime"
        type = "UInt64"
        path = "rspTime"
      }
      {
        name = "rspStatus"
        type = "UInt32"
        path = "rspStatus"
      }
    ]
    sql = """
     INSERT INTO oad_db_event (
    timestamp, id, eventTypes, net_dstIp_v4, net_dstIp_v6,
    net_dstPort, net_srcIp_v4, net_srcIp_v6, net_srcPort, accessDomains,
    account, accountType, srvAddress, srvName, srvType, srvLevel,
    dbName, dbType, dbLevel, tableName, tableLevel, optionMethod,
    deployDomains, reqDataLabelIds, rspDataLabelIds, rspTime, rspStatus
) VALUES (
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?
)
    """
  }

  extract-jdbc {
    extractField = "weakness"
    url = "***********************************************"
    driver = "org.postgresql.Driver"
    user = postgres
    password = aXvKfoAeSARQTPlx
    field_mapping = {
      "name" = "name",
      "type" = "type",
      "level" = "level",
      "state" = "state",
      "discoverTime" = "first_at",
      "activeTime" = "last_at",
      "srvId" = "srv_id",
      "dbId" = "db_id",
      "tableId" = "table_id",
      "weaknessId" = "weakness_id",
      "operationId" = "operation_id"
    }
    query ="""
   INSERT INTO oad_weakness (
        name, type, level, state,
        first_at,last_at,srv_id,db_id,
        table_id,weakness_id,operation_id
      ) VALUES (
        ?, ?, ?, ?,
        ?, ?, ?, ?,
        ?, ?, ?
      )
        ON CONFLICT (operation_id) DO UPDATE SET
        name = EXCLUDED.name,
        type = EXCLUDED.type,
        level = EXCLUDED.level,
        state = EXCLUDED.state,
        first_at = EXCLUDED.first_at,
        last_at = EXCLUDED.last_at
    """
  }

  jdbc {
    url = "***********************************************"
    driver = "org.postgresql.Driver"
    user = postgres
    password = aXvKfoAeSARQTPlx
    field_mapping = {
      "eventId" = "event_id",
      "timestamp" = "timestamp",
      "netSrcIp" = "net_src_ip",
      "netSrcPort" = "net_src_port",
      "netDstIp" = "net_dst_ip",
      "netDstPort" = "net_dst_port",
      "netFlowSource" = "net_flow_source",
      "mac" = "mac",
      "reqDbName" = "req_db_name",
      "reqDbUser" = "req_db_user",
      "reqDbPassword" = "req_db_password",
      "reqSql" = "req_sql",
      "rspStatus" = "rsp_status",
      "rspStartTime" = "rsp_start_time",
      "rspCloseTime" = "rsp_close_time",
      "rspRowCount" = "rsp_row_count",
      "rspResult" = "rsp_result",
      "srvId" = "srv_id",
      "dbId" = "db_id",
      "accessDomains" = "access_domains",
      "deployDomains" = "deploy_domains",
      "reqDataLabels" = "req_data_labels",
      "rspDataLabels" = "rsp_data_labels"
    }
    query ="""
     INSERT INTO oad_sample (
    event_id, timestamp, net_src_ip, net_src_port, net_dst_ip,
    net_dst_port, net_flow_source, mac, req_db_name, req_db_user,
    req_db_password, req_sql, rsp_status, rsp_start_time, rsp_close_time,
    rsp_row_count, rsp_result, srv_id, db_id, access_domains,
    deploy_domains, req_data_labels, rsp_data_labels
) VALUES (
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?
)
    """
  }


}
