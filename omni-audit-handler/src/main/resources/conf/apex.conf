env {
  parallelism = 1
  job.name = "oad-handler"
  job.mode = "debug"
}

source {
  kafka {
    parallelism = 1
    topic = "DBEvent"
    bootstrap.servers = "*************:9094"
    consumer.group = "oad-handler"
    kafka.config = {
      max.poll.records = 100
      auto.offset.reset = "earliest"
    }
    plugin_output = "db_event"
    format = json
  }
}

transform {
  Normalizer {
    plugin_input = "db_event"
    plugin_output = "db_event_flatten"

    fields = {
      timestamp = "meta.tm"
      type = "meta.type"
      metaAppName = "meta.app_name"
      metaServerVersion = "meta.server_version"
      netSrcIp = "net.src_ip"
      netSrcPort = "net.src_port"
      netDstIp = "net.dst_ip"
      netDstPort = "net.dst_port"
      netFlowSource = "net.flow_source"
      mac = "mac.mac"
      eventId = "unique_id.event_id"
      reqDbName = "req.db_name"
      reqDbUser = "req.db_user"
      reqDbPassword = "req.db_password"
      reqSqlCmdType = "req.sql_cmd_type"
      reqSql = "req.sql"
      rspStatus = "rsp.status"
      rspStartTime = "rsp.start_time"
      rspCloseTime = "rsp.close_time"
      rspRowCount = "rsp.row_count"
      rspResult = "rsp.result"
    }

  }
  EventFilter {
    id = "eventId"
  }
  SamplingLimit {
    sampling_keys = ["reqDbName"]
  }
  SqlParse {
    sql = "reqSql"
    dbType = "type"
    tables = "tables"
    tableInfo = {
      name = "name"
      columns = "columns"
    }
  }
  Asset {
    asset = {
      properties={
        jdbcUrl = "**********************************************"
        jdbcUser = "postgres"
        jdbcPassword = "uO0LpGbeQy2T0DWR"
        jdbcSchema = "public"
      }
      assignment = [
        {
          sourceKey = "${netSrcIp}:${netSrcPort}"
          targetKey = "srvAddress"
        }
        {
          sourceKey = "${type}"
          targetKey = "srvType"
        }
        {
          sourceKey = "${type}"
          targetKey = "dbType"
        }
        {
          sourceKey = "${metaServerVersion}"
          targetKey = "dbVersion"
        }
      ]
      time = {
        timestamp = "timestamp"
        dbActiveTime = "dbActiveTime"
        dbDiscoverTime = "dbDiscoverTime"
        srvActiveTime = "srvActiveTime"
        srvDiscoverTime = "srvDiscoverTime"
        discoverTime = "tables.?.discoverTime"
        activeTime = "tables.?.activeTime"
      }
      level = [
        {
          assetLevelPath = "srvLevel"
          levelType = "srcLevel"
        }
        {
          assetLevelPath = "dbLevel"
          levelType = "dbLevel"
        }
        {
          assetLevelPath = "tables.?.level"
          levelType = "tableLevel"
        }
      ]
      id = {
        db_srv = {
          assetIdPath = "srvAddress"
          assetIdName = "srv_id"
          assetIdEvent = "srvId"
          sqlKeyAndEventKey = {
          }
        }
        db_assets = {
          assetIdPath = "srvAddress,reqDbName"
          assetIdName = "db_id"
          assetIdEvent = "dbId"
          sqlKeyAndEventKey = {
          }
        }
        db_table = {
          assetIdPath = "srvAddress,reqDbName,tables.?.name"
          assetIdName = "table_id"
          assetIdEvent = "tableId"
          sqlKeyAndEventKey = {
          }
        }
      }
      handle = [
        AssignmentHandler
        ActiveTimeHandler
        DiscoverTimeHandler
        LevelHandler
        MajorKeyHandler
      ]
    }
  }
  Label {
    properties={
      jdbcUrl = "**********************************************"
      jdbcUser = "postgres"
      jdbcPassword = "uO0LpGbeQy2T0DWR"
      jdbcSchema = "public"
    }
    label = [
      {
        labelType = "tableLabel"
        labelPath = "tables.?.featureLabels"
      }
      {
        labelType = "dbLabel"
        labelPath = "eventTypes"
      }
    ]
  }
  NetworkSegment {
    net = {
      netSrcIp = "netSrcIp"
      netDstIp = "netDstIp"
      visitDomains = "accessDomains"
      deployDomains = "deployDomains"
    }
  }
  Weakness {
    weakness_config = {
      WEAKNESS_RULE_TABLE = "weakness_rule"
      PRIMARY_KEYS=""
    }
    output_field_map = {
      "tableId" = "tableId",
      "srvId" = "srvId",
      "dbId" = "dbId"
    }

    field_map = {
      "weaknessId" = "weakness_id",
      "accessDomains" = "access_domains",
      "deployDomains" = "deploy_domains",
      "operationId" = "operation_id"
    }
    weakness_scheme = {
      name = "string"
      type = "string"
      level = "int"
      state = "string"
      discoverTime = "bigint"
      activeTime = "bigint"
      srvId = "string"
      dbId = "string"
      tableId = "string"
      weaknessId="int"
      operationId="string"
    }
  }
  SamplingStorage {
    sample_config = {
      sample_unique_key="dbId"
    }
    sample_scheme = {
      eventId = string
      timestamp = bigint
      netSrcIp = string
      netSrcPort = int
      netDstIp = string
      netDstPort = int
      netFlowSource = string
      mac = string
      reqDbName = string
      reqDbUser = string
      reqDbPassword = string
      reqSql = string
      rspStatus = int
      rspStartTime = bigint
      rspCloseTime = bigint
      rspRowCount = int
      rspResult = string
      srvId = string
      dbId = string
      accessDomains = "array<int>"
      deployDomains = "array<int>"
      reqDataLabels = "array<int>"
      rspDataLabels = "array<int>"
    }
  }
}

sink {

  jdbc {
    batch_enable = true
    bulk_size = 100
    init_seconds = 10
    delay_seconds = 10
    retry_count = 1
    retry_interval = 1
    host = "*************:18123"
    database = audit
    username = audit
    password = X_OD)675()jShf6(
    fields = [
      {
        name = "timestamp"
        type = "UInt32"
        path = "timestamp"
      }
      {
        name = "id"
        type = "String"
        path = "eventId"
      }
      {
        name = "eventTypes"
        type = "Array(UInt8)"
        path = "eventTypes"
      }
      {
        name = "net_dstIp_v4"
        type = "IPv4"
        path = "netDstIp"
      }
      {
        name = "net_dstIp_v6"
        type = "IPv6"
        path = "netDstIp"
      }
      {
        name = "net_dstPort"
        type = "UInt16"
        path = "netDstPort"
      }
      {
        name = "net_srcIp_v4"
        type = "IPv4"
        path = "netSrcIp"
      }
      {
        name = "net_srcIp_v6"
        type = "IPv6"
        path = "netSrcIp"
      }
      {
        name = "net_srcPort"
        type = "UInt16"
        path = "netSrcPort"
      }
      {
        name = "accessDomains"
        type = "Array(UInt8)"
        path = "accessDomains"
      }
      {
        name = "account"
        type = "LowCardinality(String)"
        path = "reqDbUser"
      }
      {
        name = "accountType"
        type = "UInt8"
        path = "accountType"
      }
      {
        name = "srvAddress"
        type = "LowCardinality(String)"
        path = "srvAddress"
      }
      {
        name = "srvName"
        type = "LowCardinality(String)"
        path = "srvName"
      }
      {
        name = "srvType"
        type = "UInt8"
        path = "srvType"
      }
      {
        name = "srvLevel"
        type = "UInt8"
        path = "srvLevel"
      }
      {
        name = "dbName"
        type = "LowCardinality(String)"
        path = "reqDbName"
      }
      {
        name = "dbType"
        type = "UInt8"
        path = "srvType"
      }
      {
        name = "srvLevel"
        type = "UInt8"
        path = "srvLevel"
      }
      {
        name = "dbName"
        type = "LowCardinality(String)"
        path = "reqDbName"
      }
    ]
    sql ="""
     INSERT INTO event_log (
    timestamp, id, eventTypes, net_dstIp_v4, net_dstIp_v6,
    net_dstPort, net_srcIp_v4, net_srcIp_v6, net_srcPort, accessDomains,
    account, accountType, srvAddress, srvName, srvType, srvLevel,
    dbName, dbType, dbLevel, tableName, tableLevel, optionMethod,
    deployDomains, reqDataLabelIds, rspDataLabelIds, rspTime, rspStatus
) VALUES (
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?
)
    """
  }

  extract-jdbc {
    extractField = "weakness"
    url = "**********************************************"
    driver = "org.postgresql.Driver"
    user = postgres
    password = uO0LpGbeQy2T0DWR
    field_mapping = {
      "name" = "name",
      "type" = "type",
      "level" = "level",
      "state" = "state",
      "discoverTime" = "first_at",
      "activeTime" = "last_at",
      "srvId" = "srv_id",
      "dbId" = "db_id",
      "tableId" = "table_id",
      "weaknessId" = "weakness_id",
      "operationId" = "operation_id"
    }
    query ="""
   INSERT INTO weakness (
        name, type, level, state,
        first_at,last_at,srv_id,db_id,
        table_id,weakness_id,operation_id
      ) VALUES (
        ?, ?, ?, ?,
        ?, ?, ?, ?,
        ?, ?, ?
      )
        ON CONFLICT (operation_id) DO UPDATE SET
        name = EXCLUDED.name,
        type = EXCLUDED.type,
        level = EXCLUDED.level,
        state = EXCLUDED.state,
        first_at = EXCLUDED.first_at,
        last_at = EXCLUDED.last_at
    """
  }

  jdbc {
    url = "**********************************************"
    driver = "org.postgresql.Driver"
    user = postgres
    password = uO0LpGbeQy2T0DWR
    field_mapping = {
      "eventId" = "event_id",
      "timestamp" = "timestamp",
      "netSrcIp" = "net_src_ip",
      "netSrcPort" = "net_src_port",
      "netDstIp" = "net_dst_ip",
      "netDstPort" = "net_dst_port",
      "netFlowSource" = "net_flow_source",
      "mac" = "mac",
      "reqDbName" = "req_db_name",
      "reqDbUser" = "req_db_user",
      "reqDbPassword" = "req_db_password",
      "reqSql" = "req_sql",
      "rspStatus" = "rsp_status",
      "rspStartTime" = "rsp_start_time",
      "rspCloseTime" = "rsp_close_time",
      "rspRowCount" = "rsp_row_count",
      "rspResult" = "rsp_result",
      "srvId" = "srv_id",
      "dbId" = "db_id",
      "accessDomains" = "access_domains",
      "deployDomains" = "deploy_domains",
      "reqDataLabels" = "req_data_labels",
      "rspDataLabels" = "rsp_data_labels"
    }
    query ="""
     INSERT INTO sample (
    event_id, timestamp, net_src_ip, net_src_port, net_dst_ip,
    net_dst_port, net_flow_source, mac, req_db_name, req_db_user,
    req_db_password, req_sql, rsp_status, rsp_start_time, rsp_close_time,
    rsp_row_count, rsp_result, srv_id, db_id, access_domains,
    deploy_domains, req_data_labels, rsp_data_labels
) VALUES (
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?, ?, ?,
    ?, ?, ?
)
    """
  }


}
