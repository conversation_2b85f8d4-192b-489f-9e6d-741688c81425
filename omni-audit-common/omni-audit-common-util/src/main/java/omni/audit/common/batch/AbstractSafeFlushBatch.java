package omni.audit.common.batch;

import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * @Author: HaoJun
 * @Date: 2021/7/1 6:47 下午
 */
public abstract class AbstractSafeFlushBatch<T> implements AutoFlushBatch<T> {

    protected Lock flushLock = new ReentrantLock();

    @Override
    public final boolean flush() {
        if (!flushLock.tryLock()) {
            return false;
        }
        List<T> items;
        try {
            items = poll();
        } finally {
            flushLock.unlock();
        }
        if (items != null && !items.isEmpty()) {
            onFlush(items);
        }
        return true;
    }

    protected abstract void onFlush(List<T> items);

    protected abstract List<T> poll();

}
