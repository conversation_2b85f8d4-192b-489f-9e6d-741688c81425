package omni.audit.common.util;

import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 *
 */
@Slf4j
public final class DateUtil {

    private DateUtil() {
    }

    /**
     * 日期格式
     **/
    public interface DatePattern {

        String YYYY_MM_DD_HH = "yyyy-MM-dd HH";

        String HHMMSS = "HHmmss";

        String HH_MM_SS = "HH:mm:ss";

        String YYYYMMDD = "yyyyMMdd";

        String YYYY_MM_DD = "yyyy-MM-dd";

        String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

        String YYYYMMDDHHMMSSSSS = "yyyyMMddHHmmssSSS";

        String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

        String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

        String YYYY_MM_DDT_HH_MM_SS_SSS_Z = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    }

    public enum DateFormat {
        yyyyMMdd(DateTimeFormatter.ofPattern(DatePattern.YYYYMMDD)),
        yyyy_MM_dd(DateTimeFormatter.ofPattern(DatePattern.YYYY_MM_DD)),
        yyyy_MM_dd_HH_mm_ss(DateTimeFormatter.ofPattern(DatePattern.YYYY_MM_DD_HH_MM_SS)),
        HH_mm_ss(DateTimeFormatter.ofPattern(DatePattern.HH_MM_SS));

        private DateTimeFormatter formatter;

        DateFormat(DateTimeFormatter formatter) {
            this.formatter = formatter;
        }

        public DateTimeFormatter getFormatter() {
            return this.formatter;
        }

    }

    public static long getDateStartSeconds(String date) {
        try {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date parseDate = sdf.parse(date);
            calendar.setTime(parseDate);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            return calendar.getTimeInMillis() / 1000;
        } catch (Exception e) {
            return System.currentTimeMillis() / 1000;
        }
    }

    public static long getDateEndSeconds(String date) {
        try {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date parseDate = sdf.parse(date);
            calendar.setTime(parseDate);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            return calendar.getTimeInMillis() / 1000;
        } catch (Exception e) {
            return System.currentTimeMillis() / 1000;
        }
    }

    public static String format(Long time) {

        SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.YYYY_MM_DD_HH_MM_SS);
        // 关键所在
        TimeZone gmt = TimeZone.getTimeZone("GMT+8");
        sdf.setTimeZone(gmt);
        sdf.setLenient(true);
        return sdf.format(time);
    }

    public static String format(Long time, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        TimeZone gmt = TimeZone.getTimeZone("GMT+8");
        sdf.setTimeZone(gmt);
        sdf.setLenient(true);
        return sdf.format(time);
    }

    /**
     * 格式化日期
     *
     * @param date
     * @param
     * @return
     */
    public static String format(Object date) {
        return format(date, DatePattern.YYYY_MM_DD);
    }

    /**
     * 格式化日期
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String format(Object date, String pattern) {
        if (date == null) {
            return null;
        }
        if (pattern == null) {
            return format(date);
        }
        return new SimpleDateFormat(pattern).format(date);
    }

    /**
     * 时间格式转换，从{fromPattern}格式转化为{toPattern}格式
     *
     * @param time
     * @param fromPattern
     * @param toPattern
     * @return
     */
    public static String format(String time, String fromPattern, String toPattern) {
        SimpleDateFormat format = new SimpleDateFormat(fromPattern);
        Long ts = null;
        try {
            ts = format.parse(time).getTime();
        } catch (ParseException e) {
            log.warn("时间转换出错，time: {}, fromPattern: {}, toPattern: {}", new Object[]{time, fromPattern});
            throw new RuntimeException("时间转换出错");
        }
        return format(ts, toPattern);
    }

    /**
     * 获取日期
     *
     * @return
     */
    public static String getDate() {
        return format(new Date());
    }

    /**
     * 获取日期时间
     *
     * @return
     */
    public static String getDateTime() {
        return format(new Date(), DatePattern.YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 获取日期
     *
     * @param pattern
     * @return
     */
    public static String getDateTime(String pattern) {
        return format(new Date(), pattern);
    }

    /**
     * 日期计算
     *
     * @param date
     * @param field
     * @param amount
     * @return
     */
    public static Date addDate(Date date, int field, int amount) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(field, amount);
        return calendar.getTime();
    }

    /**
     * 字符串转换为日期:不支持yyM[M]d[d]格式
     *
     * @param date
     * @return
     */
    public static Date stringToDate(String date) {
        if (date == null) {
            return null;
        }
        String separator = String.valueOf(date.charAt(4));
        String pattern = "yyyyMMdd";
        if (!separator.matches("\\d*")) {
            pattern = "yyyy" + separator + "MM" + separator + "dd";
            if (date.length() < 10) {
                pattern = "yyyy" + separator + "M" + separator + "d";
            }
        } else if (date.length() < 8) {
            pattern = "yyyyMd";
        }
        pattern += " HH:mm:ss.SSS";
        pattern = pattern.substring(0, Math.min(pattern.length(), date.length()));
        try {
            return new SimpleDateFormat(pattern).parse(date);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 间隔天数
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static Integer getDayBetween(Date startDate, Date endDate) {
        Calendar start = Calendar.getInstance();
        start.setTime(startDate);
        start.set(Calendar.HOUR_OF_DAY, 0);
        start.set(Calendar.MINUTE, 0);
        start.set(Calendar.SECOND, 0);
        start.set(Calendar.MILLISECOND, 0);
        Calendar end = Calendar.getInstance();
        end.setTime(endDate);
        end.set(Calendar.HOUR_OF_DAY, 0);
        end.set(Calendar.MINUTE, 0);
        end.set(Calendar.SECOND, 0);
        end.set(Calendar.MILLISECOND, 0);

        long n = end.getTimeInMillis() - start.getTimeInMillis();
        return (int) (n / (60 * 60 * 24 * 1000L));
    }

    /**
     * 间隔月
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static Integer getMonthBetween(Date startDate, Date endDate) {
        if (startDate == null || endDate == null || !startDate.before(endDate)) {
            return null;
        }
        Calendar start = Calendar.getInstance();
        start.setTime(startDate);
        Calendar end = Calendar.getInstance();
        end.setTime(endDate);
        int year1 = start.get(Calendar.YEAR);
        int year2 = end.get(Calendar.YEAR);
        int month1 = start.get(Calendar.MONTH);
        int month2 = end.get(Calendar.MONTH);
        int n = (year2 - year1) * 12;
        n = n + month2 - month1;
        return n;
    }

    /**
     * 间隔月，多一天就多算一个月
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static Integer getMonthBetweenWithDay(Date startDate, Date endDate) {
        if (startDate == null || endDate == null || !startDate.before(endDate)) {
            return null;
        }
        Calendar start = Calendar.getInstance();
        start.setTime(startDate);
        Calendar end = Calendar.getInstance();
        end.setTime(endDate);
        int year1 = start.get(Calendar.YEAR);
        int year2 = end.get(Calendar.YEAR);
        int month1 = start.get(Calendar.MONTH);
        int month2 = end.get(Calendar.MONTH);
        int n = (year2 - year1) * 12;
        n = n + month2 - month1;
        int day1 = start.get(Calendar.DAY_OF_MONTH);
        int day2 = end.get(Calendar.DAY_OF_MONTH);
        if (day1 <= day2) {
            n++;
        }
        return n;
    }

    public static String date2str(Date date, String pattern) {
        return format(date, pattern);
    }

    /**
     * String 类型的时间转换为时间戳
     *
     * @param time
     * @return
     */
    public static Long getTime(String time) {
        try {
            SimpleDateFormat format = new SimpleDateFormat(DatePattern.YYYY_MM_DD_HH_MM_SS);
            return format.parse(time).getTime();
        } catch (ParseException e) {
            throw new ServiceException("时间转换错误");
        }

    }

    public static long getTime(String date, String pattern) {
        try {
            SimpleDateFormat format = new SimpleDateFormat(pattern);
            return format.parse(date).getTime();
        } catch (ParseException e) {
            throw new ServiceException("时间转换错误");
        }
    }

    /**
     * String 类型的时间转换为时间戳
     *
     * @param time
     * @return
     */
    public static Long getTime2(String time) {
        if (time == null) {
            return 0L;
        }
        try {
            SimpleDateFormat format = new SimpleDateFormat(DatePattern.YYYYMMDD);
            return format.parse(time).getTime();
        } catch (ParseException e) {
            throw new ServiceException("时间转换错误");
        }

    }

    /**
     * String 类型的时间转换为时间戳
     *
     * @param time
     * @return
     */
    public static Long getTime3(String time) {
        try {
            SimpleDateFormat format = new SimpleDateFormat(DatePattern.YYYY_MM_DD_HH_MM);
            return format.parse(time).getTime();
        } catch (ParseException e) {
            throw new ServiceException("时间转换错误");
        }

    }

    /**
     * 时间转时间戳
     *
     * @param tm
     * @return
     */
    public static Long time2tm(String tm) {
        return getTime(tm);
    }

    /**
     * 时间转日期
     *
     * @param time
     * @return
     */
    public static String time2date(String time) {
        return time.split(" ")[0];
    }

    /**
     * 时间戳转为日期
     *
     * @param timestamp
     * @return
     */
    public static String tm2date(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.YYYYMMDD);
        return sdf.format(timestamp);
    }

    /**
     * 时间戳转为日期
     *
     * @param timestamp
     * @return
     */
    public static String tm2date2(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.YYYY_MM_DD);
        return sdf.format(timestamp);
    }

    /**
     * 时间戳转时间
     *
     * @param timestamp
     * @return
     */
    public static String tm2time(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.YYYY_MM_DD_HH_MM_SS);
        return sdf.format(timestamp);
    }

    public static String tm2time(Long timestamp, String pattern) {
        if (timestamp == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(timestamp);
    }

    /**
     * 时间戳转日期
     *
     * @param timestamp
     * @return
     */
    public static Date tm2Date(Long timestamp) {
        String dateStr = tm2time(timestamp);
        return DateUtil.stringToDate(dateStr);
    }

    public static Long date2tm(String date) {
        try {
            SimpleDateFormat format = new SimpleDateFormat(DatePattern.YYYYMMDD);
            return format.parse(date).getTime();
        } catch (ParseException e) {
            log.warn("事件转换出错, date: {}", date);
            throw new RuntimeException("时间转换出错");
        }
    }

    /**
     * 获取当前时间
     *
     * @param pattern 日期样式
     * @return
     */
    public static String getCurrentTimeStr(String pattern) {
        return DateTimeFormatter.ofPattern(pattern).format(LocalDateTime.now());
    }

    /**
     * @param startTime
     * @param endTime
     * @param timeFormat 开始时间和结束时间的时间格式
     * @param axisFormat 横坐标的时间格式
     * @return
     */
    public static Map<String, Long> getBetweenDates(
            String startTime,
            String endTime,
            String timeFormat,
            String axisFormat
    ) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(timeFormat);
        Map<String, Long> result = new TreeMap<>();
        Calendar startCalendar = Calendar.getInstance();
        Calendar endCalendar = Calendar.getInstance();
        try {
            startCalendar.setTime(simpleDateFormat.parse(startTime));
            endCalendar.setTime(simpleDateFormat.parse(endTime));
        } catch (ParseException e) {
            log.warn("时间解析错误，startTime: {}, endTime: {}", new Object[]{startTime, endTime, e});
            throw new RuntimeException("时间转换错误");
        }
        SimpleDateFormat axisDateFormat = new SimpleDateFormat(axisFormat);
        while (startCalendar.before(endCalendar) || startCalendar.equals(endCalendar)) {
            result.put(axisDateFormat.format(startCalendar.getTime()), 0L);
            startCalendar.add(Calendar.DAY_OF_YEAR, 1);
        }
        return result;
    }

    public static Map<String, Integer> getBetweenDates(String startDate, String endDate) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        Map<String, Integer> result = new TreeMap<>();
        Calendar startCalendar = Calendar.getInstance();
        try {
            startCalendar.setTime(simpleDateFormat.parse(startDate));
        } catch (Exception e) {
            log.warn("startTime格式不正确，startDate: {}", startDate, e);
            throw new ServiceException("startDate invalid");
        }
        Calendar endCalendar = Calendar.getInstance();
        try {
            endCalendar.setTime(simpleDateFormat.parse(endDate));
        } catch (Exception e) {
            log.warn("endTime格式不正确，endDate: {}", endDate, e);
            throw new ServiceException("endDate invalid");
        }
        while (startCalendar.before(endCalendar) || startCalendar.equals(endCalendar)) {
            result.put(simpleDateFormat.format(startCalendar.getTime()), 0);
            startCalendar.add(Calendar.DAY_OF_YEAR, 1);
        }
        return result;
    }

    /**
     * 获取两个时间戳相隔的天数
     *
     * @param startTimestamp
     * @param endTimestamp
     * @return
     */
    public static int getBetweenDay(Long startTimestamp, Long endTimestamp) {
        int day = (int) ((endTimestamp - startTimestamp) / (24 * 3600 * 1000));
        return day;
    }

    public static List<String> getBetweenDateList(String startDate, String endDate) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        Calendar startCalendar = Calendar.getInstance();
        try {
            startCalendar.setTime(simpleDateFormat.parse(startDate));
        } catch (ParseException e) {
            log.warn("startTime格式不正确，startDate: {}", startDate, e);
            throw new ServiceException("startDate invalid");
        }
        Calendar endCalendar = Calendar.getInstance();
        try {
            endCalendar.setTime(simpleDateFormat.parse(endDate));
        } catch (ParseException e) {
            log.warn("endTime格式不正确，endDate: {}", endDate, e);
            throw new ServiceException("endDate invalid");
        }

        List<String> dates = new ArrayList<>();
        while (startCalendar.before(endCalendar) || startCalendar.equals(endCalendar)) {
            dates.add(simpleDateFormat.format(startCalendar.getTime()));
            startCalendar.add(Calendar.DAY_OF_YEAR, 1);
        }
        return dates;
    }

    /**
     * 获取当前日期时间
     *
     * @return 当前日期时间字符串 yyyy-MM-dd HH:mm:ss
     */
    public static String currentDateTime() {
        return LocalDateTime.now().format(DateFormat.yyyy_MM_dd_HH_mm_ss.getFormatter());
    }

    /**
     * 获取当前日期
     *
     * @return 当前日期字符串 yyyyMMdd
     */
    public static String currentDate() {
        return LocalDate.now().format(DateFormat.yyyyMMdd.getFormatter());
    }

    /**
     * 获取当前时间戳
     *
     * @return 当前时间戳 精确到毫秒
     */
    public static long currentTimestamp() {
        return Instant.now().toEpochMilli();
    }

    /**
     * @param mss
     * @return 该毫秒数转换为 * days * hours * minutes * seconds 后的格式
     * <AUTHOR>
     */
    public static String formatDuring(long mss) {
        long days = mss / (1000 * 60 * 60 * 24);
        long hours = (mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
        long minutes = (mss % (1000 * 60 * 60)) / (1000 * 60);
        long seconds = (mss % (1000 * 60)) / 1000;
        if (days == 0 && hours == 0) {
            return minutes + "分钟";
        } else if (days == 0) {
            return hours + "小时" + minutes + "分钟";
        }
        return days + "天" + hours + "小时" + minutes + "分钟";
    }

    /**
     * 获取今天星期几
     */
    public static Integer getWeek() {

        Integer[] weekDays = {7, 1, 2, 3, 4, 5, 6};
        Calendar calendar = Calendar.getInstance();
        return weekDays[calendar.get(Calendar.DAY_OF_WEEK) - 1];
    }

    /**
     * 获取当前小时
     */
    public static Integer getHour() {
        Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));
        return cal.get(Calendar.HOUR);
    }

    /**
     * 获取当天是几号
     */
    public static Integer getDayOfMonth() {
        LocalDate nowDate = LocalDate.now();
        return nowDate.getDayOfMonth();
    }

    /**
     * 获取是几月
     */
    public static Integer getMonthOfYear() {
        LocalDate nowDate = LocalDate.now();
        return nowDate.getMonthValue();
    }

    /**
     * 获取当天时间戳的最小最大范围
     *
     * @return
     */
    public static long[] getTodayTimeStampBetween() {
        long current = System.currentTimeMillis();
        long zero = current - (current + TimeZone.getDefault().getRawOffset()) % (1000 * 3600 * 24);
        // 今天23点59分59秒的毫秒数
        long end = zero + 24 * 60 * 60 * 1000 - 1;
        return new long[]{zero, end};
    }

    /**
     * 获取当天零点时间戳
     *
     * @return
     */
    public static long getTodayZeroTimeStamp() {
        long current = System.currentTimeMillis();
        long zero = current - (current + TimeZone.getDefault().getRawOffset()) % (1000 * 3600 * 24);
        return zero;
    }

    /**
     * 获取昨天最后的时间戳  23:59:59
     *
     * @return
     */
    public static long getYesterdayLastTimestamp() {
        long todayZero = getTodayTimeStampBetween()[0];
        return todayZero - 1000;
    }

    /**
     * 获取对应日期的零点时间戳
     *
     * @param date
     * @return
     */
    public static long getDateZeroTimestamp(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    /**
     * 获取对应日期最后的时间戳
     *
     * @param date
     * @return
     */
    public static long getDateLastTimestamp(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    /**
     * 获取date往前/往后推几个月的日期
     *
     * @param n
     * @return
     */
    public static Date addMonth(Date date, int n) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, n);
        return cal.getTime();
    }

    /**
     * 获取date往前/往后推几天的日期
     *
     * @param n
     * @return
     */
    public static Date addDay(Date date, int n) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, n);
        return cal.getTime();
    }

    public static Long startOfMinute(Long timestamp) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        TimeZone gmt = TimeZone.getTimeZone("GMT+8");
        sdf.setTimeZone(gmt);
        sdf.setLenient(true);
        String string = sdf.format(timestamp);

        return Timestamp.valueOf(string).getTime();
    }

    public static Long startOfDate(String date) {

        try {

            Date dateString = new SimpleDateFormat("yyyyMMdd").parse(date);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
            TimeZone gmt = TimeZone.getTimeZone("GMT+8");
            sdf.setTimeZone(gmt);
            sdf.setLenient(true);
            String string = sdf.format(dateString);

            return Timestamp.valueOf(string).getTime();

        } catch (ParseException e) {

            return null;
        }
    }

    public static Long endOfDate(String date) {
        try {

            Date dateString = new SimpleDateFormat("yyyyMMdd").parse(date);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
            TimeZone gmt = TimeZone.getTimeZone("GMT+8");
            sdf.setTimeZone(gmt);
            sdf.setLenient(true);
            String string = sdf.format(dateString);

            return Timestamp.valueOf(string).getTime();

        } catch (ParseException e) {

            return null;
        }
    }

    public static long getTimeStampBeforeDays(int days) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime targetDateTime = now.minusDays(days);
        return targetDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
    }

    public static void main(String[] args) {
        long current = 1636995600000L;
        long zero = current / (1000 * 3600 * 24) * (1000 * 3600 * 24) - TimeZone.getDefault().getRawOffset();
        System.out.println(zero);

        long zero1 = current - (current + TimeZone.getDefault().getRawOffset()) % (1000 * 3600 * 24);
        System.out.println(zero1);
        System.out.println(zero1 - 1000);

        System.out.println(DateUtil.date2str(new Date(), DatePattern.YYYY_MM_DD));

        System.out.println(DateUtil.stringToDate("2025-01-06"));
        System.out.println(DateUtil.stringToDate("2025-01-06"));
        System.out.println(DateUtil.stringToDate("2025-01-06").getTime());
        System.out.println(DateUtil.getDateLastTimestamp(DateUtil.stringToDate("2025-01-06")));

    }

}