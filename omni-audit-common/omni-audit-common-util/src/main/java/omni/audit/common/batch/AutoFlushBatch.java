package omni.audit.common.batch;

import java.io.Closeable;
import java.util.List;

/**
 * @Author: Hao<PERSON>un
 * @Date: 2021/1/6 12:06 下午
 */
public interface AutoFlushBatch<T> extends Closeable {

    /**
     * 添加成员
     *
     * @param item
     * @return
     */
    default void add(T item) {
        throw new UnsupportedOperationException();
    }

    default void add(String key, T item) {
        throw new UnsupportedOperationException();
    }

    default T get(String key) {
        throw new UnsupportedOperationException();
    }

    /**
     * 执行手动flush
     *
     * @return 当前线程是否执行了 flush
     */
    boolean flush();

    /**
     * 设置单次flush批量上限
     *
     * @param size
     */
    void setBatchSize(int size);

    /**
     * 检测是否需要flush
     *
     * @return
     */
    boolean checkFlush();

    /**
     * 刷新执行器
     *
     * @param executor
     */
    void setFlushExecutor(FlushExecutor<T> executor);

    int size();

    boolean isEmpty();

    /**
     * 记录数
     *
     * @return
     */
    int records();

    /**
     * 关闭
     */
    @Override
    void close();

    @FunctionalInterface
    interface FlushExecutor<T> {

        void doFlush(List<T> list);

        default void beforeAdd(T item) {
        }

        default void onClosed() {
        }

    }

}
