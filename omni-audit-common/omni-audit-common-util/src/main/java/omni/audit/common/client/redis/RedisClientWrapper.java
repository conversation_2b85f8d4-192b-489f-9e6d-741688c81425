package omni.audit.common.client.redis;

import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * create at 2025/6/1
 * @description: Redis 客户端封装，提供常用操作方法
 **/
public class RedisClientWrapper {

    private final RedissonClient redissonClient;

    public RedisClientWrapper() {
        this.redissonClient = RedissonClientFactory.getRedissonClient();
    }

    public RedissonClient getRedissonClient() {
        return redissonClient;
    }

    /**
     * 获取字符串值
     */
    public String get(String key) {
        RBucket<String> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }

    /**
     * 设置字符串值
     */
    public void set(String key, String value) {
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.set(value);
    }

    /**
     * 判断 key 是否存在
     */
    public boolean exists(String key) {
        return redissonClient.getBucket(key).isExists();
    }

    /**
     * 删除 key
     */
    public void del(String key) {
        redissonClient.getKeys().delete(key);
    }

    /**
     * 自增并返回新值
     */
    public Long incr(String key) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        return atomicLong.incrementAndGet();
    }

    public Long incrBy(String key, long delta) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        return atomicLong.addAndGet(delta);
    }

    /**
     * 清空所有数据
     */
    public void flushAll() {
        redissonClient.getKeys().flushdb();
    }

    public void expire(String key, int timeout, TimeUnit timeUnit) {
        redissonClient.getBucket(key).expire(timeout, timeUnit);
    }

}