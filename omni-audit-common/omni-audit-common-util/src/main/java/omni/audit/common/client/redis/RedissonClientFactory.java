package omni.audit.common.client.redis;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;

/**
 * <AUTHOR>
 * create at 2025/6/1
 * @description: Redisson 客户端工厂
 **/
public class RedissonClientFactory {

    private static volatile RedissonClient INSTANCE;

    private RedissonClientFactory() {
    }

    /**
     * 使用指定配置初始化 Redisson 客户端
     */
    public static synchronized void init(RedisConfig config) {
        if (INSTANCE != null) {
            INSTANCE.shutdown();
        }

        Config redissonConfig = new Config();
        redissonConfig.useSingleServer()
                      .setAddress("redis://" + config.getHost() + ":" + config.getPort())
                      .setTimeout(config.getTimeout())
                      .setPassword(config.getPassword())
                      .setDatabase(config.getDatabase());

        INSTANCE = Redisson.create(redissonConfig);
    }

    /**
     * 获取 Redisson 客户端实例（需先调用 init）
     */
    public static RedissonClient getRedissonClient() {
        if (INSTANCE == null) {
            throw new IllegalStateException("RedissonClientFactory is not initialized. Call init() first.");
        }
        return INSTANCE;
    }

}