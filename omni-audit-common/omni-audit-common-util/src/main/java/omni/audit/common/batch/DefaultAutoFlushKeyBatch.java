package omni.audit.common.batch;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: HaoJun
 * @Date: 2021/1/6 12:22 下午
 */
public class DefaultAutoFlushKeyBatch<T> extends AbstractSafeFlushBatch<T> {

    private final Map<String, T> empty = Collections.emptyMap();

    private int batchSie;

    private volatile Map<String, T> batchMap;

    /**
     * 正在提交中的批次，提交完成后该值会马上置为NONE
     */
    private volatile Map<String, T> flyBatchMap = empty;

    private FlushExecutor flushExecutor;

    public static final int DEFAULT_BATCH_SIE = 1000;

    public DefaultAutoFlushKeyBatch(FlushExecutor<T> flushExecutor) {
        this(flushExecutor, DEFAULT_BATCH_SIE);
    }

    public DefaultAutoFlushKeyBatch(FlushExecutor<T> flushExecutor, int batchSie) {
        this.batchSie = batchSie;
        init();
        this.flushExecutor = flushExecutor;
    }

    @Override
    public void add(String key, T item) {
        if (flushExecutor != null) {
            flushExecutor.beforeAdd(item);
        }
        batchMap.put(key, item);
        if (checkFlush()) {
            flush();
        }
    }

    @Override
    public T get(String key) {
        T item = batchMap.get(key);
        if (item != null) {
            return item;
        }
        return flyBatchMap.get(key);
    }

    @Override
    public void onFlush(List<T> items) {
        if (flushExecutor != null) {
            try {
                flushExecutor.doFlush(items);
            } finally {
                flyBatchMap = empty;
            }
        }
    }

    @Override
    protected List<T> poll() {
        flyBatchMap = batchMap;
        initBatchMap();
        return new ArrayList(flyBatchMap.values());
    }

    @Override
    public void setBatchSize(int size) {
        this.batchSie = size;
        init();
    }

    @Override
    public boolean checkFlush() {
        return batchMap.size() >= batchSie;
    }

    @Override
    public void setFlushExecutor(FlushExecutor<T> flushExecutor) {
        this.flushExecutor = flushExecutor;
    }

    @Override
    public int size() {
        return batchMap.size();
    }

    @Override
    public boolean isEmpty() {
        return batchMap.isEmpty();
    }

    @Override
    public int records() {
        return 0;
    }

    private void init() {
        initBatchMap();
    }

    private void initBatchMap() {
        batchMap = new ConcurrentHashMap<>(batchSie * 2);
    }

    @Override
    public void close() {
        flush();
        if (flushExecutor != null) {
            flushExecutor.onClosed();
        }
    }

}
