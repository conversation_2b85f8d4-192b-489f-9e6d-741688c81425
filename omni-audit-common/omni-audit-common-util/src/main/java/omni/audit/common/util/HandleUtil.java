package omni.audit.common.util;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class HandleUtil {

    public static void putMapField(Map<String, Object> map, String path, Object newValue) {
        if (map == null || path == null || path.isEmpty()) {
            return;
        }
        String[] keys = path.split("\\.");
        updateValue(map, keys, 0, newValue);
    }

    private static void updateValue(Map<String, Object> currentMap, String[] keys, int index, Object newValue) {
        if (currentMap == null) {
            return;
        }
        if (index >= keys.length) {
            return;
        }

        String currentKey = keys[index];
        if (currentKey.contains("[") && currentKey.contains("]")) {
            String indexStr = currentKey.substring(currentKey.indexOf("[") + 1, currentKey.indexOf("]"));
            int endIndex = Integer.parseInt(indexStr);
            currentKey = currentKey.substring(0, currentKey.indexOf("["));
            Map<String, Object> objectMap = ((List<Map<String, Object>>) currentMap.get(currentKey)).get(endIndex);
            updateValue(objectMap, keys, index + 1, newValue);
        } else {
            if (index == keys.length - 1) {
                // 最后一个键，直接赋值
                currentMap.put(currentKey, newValue);
            } else {
                // 继续深入下一层
                Object next = currentMap.get(currentKey);
                if (next instanceof Map) {
                    updateValue((Map<String, Object>) next, keys, index + 1, newValue);
                } else if (next instanceof List) {
                    for (Object o : (List<?>) next) {
                        if (o instanceof Map) {
                            updateValue((Map<String, Object>) o, keys, index + 1, newValue);
                        }
                    }
                }
            }
        }
    }

    public static Object getMapField(Map<String, Object> currentMap, String path) {
        // path 可能包含.  例如a.b.c  也有可能不包含，例如为a
        String[] split = path.split("\\.");
        Map<String, Object> tempMap = currentMap;
        for (int i = 0; i < split.length; i++) {
            String string = split[i];
            if (string.contains("[") && string.contains("]")) {
                String indexStr = string.substring(string.indexOf("[") + 1, string.indexOf("]"));
                int index = Integer.parseInt(indexStr);
                string = string.substring(0, string.indexOf("["));
                tempMap = ((List<Map<String, Object>>) tempMap.get(string)).get(index);
            } else {
                if (i == split.length - 1) {
                    return tempMap.get(string);
                }
                tempMap = (Map<String, Object>) tempMap.get(string);
            }
        }
        return null;
    }

    public static Object getFieldFromEvent(
            Map<String, Object> eventMap,
            String valuePath,
            String resPath,
            Object compareValue
    ) {
        if (valuePath.contains(".?.")) {
            int bracketPos = valuePath.indexOf(".?.");
            // 获取 ? 前的路径
            String mapPath = valuePath.substring(0, bracketPos);
            String key = valuePath.substring(bracketPos + 3);
            // 获取目标映射（可能是嵌套的多层map）
            Object target = getNestedValue(eventMap, mapPath);
            List<?> list = (List<?>) target;
            for (Object value : list) {
                if (value instanceof Map) {
                    Map<String, Object> map1 = (Map<String, Object>) value;
                    Object object = map1.get(key);
                    if (Objects.equals(object, compareValue)) {
                        return map1.get(resPath);
                    }
                }
            }
        }
        return null;
    }

    public static Object getNestedValue(Map<String, Object> rootMap, String path) {
        String[] keys = path.split("\\.");
        Object current = rootMap;

        for (String key : keys) {
            if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(key);
                if (current == null) {
                    // 中途遇到null直接返回
                    return null;
                }
            } else {
                // 路径中断
                return null;
            }
        }
        return current;
    }

}
