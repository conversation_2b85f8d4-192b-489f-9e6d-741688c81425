package omni.audit.network.segment.handle.utils;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.maxmind.db.CHMCache;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.exception.AddressNotFoundException;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.City;
import com.maxmind.geoip2.record.Country;
import com.maxmind.geoip2.record.Subdivision;
import lombok.extern.slf4j.Slf4j;
import omni.audit.network.segment.handle.entity.Position;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.Charset;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class IpRegionalUtils {

    private static int offset;

    private static int[] index = new int[256];

    private static ByteBuffer dataBuffer;

    private static ByteBuffer indexBuffer;

    private static Long lastModifyTime = 0L;

    private static File ipFile;

    private static ReentrantLock lock = new ReentrantLock();

    private static Cache<String, Position> cache = CacheBuilder.newBuilder().maximumSize(200000L).build();

    private static DatabaseReader reader;

    public static final String UNKNOWN_POSITION_REPLACE = "其他";

    public static final Position UNKNOWN_POSITION = new Position("其他", "其他", "其他", "其他");

    static {
        loadGeoLite();
    }

    public static Position find(String ip) {
        try {
            return cache.get(ip, () -> findByGeoLite(ip));
        } catch (ExecutionException e) {
            return null;
        }
    }

    public static Position findByGeoLite(String ip) {
        Position position = new Position();
        try {
            InetAddress ipAddress = InetAddress.getByName(ip);
            CityResponse response = reader.city(ipAddress);
            Country country = response.getCountry();
            String countryName = country.getNames().get("zh-CN");
            if (StringUtils.isEmpty(countryName)) {
                return UNKNOWN_POSITION;
            }
            if ("香港".equals(countryName) || "澳门".equals(countryName) || "台湾".equals(countryName)) {
                position.setCountry("中国");
                position.setProvince(countryName);
                position.setCity(countryName);
                position.setLocation("中国." + countryName);
                return position;
            }
            position.setCountry(countryName);

            if (response.getSubdivisions() != null && response.getSubdivisions().size() > 0) {
                Subdivision subdivision = response.getSubdivisions().get(0);
                if (subdivision.getNames().containsKey("zh-CN")) {
                    position.setProvince(subdivision.getNames().get("zh-CN"));
                } else if (subdivision.getNames().containsKey("en")) {
                    position.setProvince(subdivision.getNames().get("en"));
                }
            }
            if (StringUtils.isEmpty(position.getProvince())) {
                position.setProvince(position.getCountry());
            }

            City city = response.getCity();
            if (city != null) {
                if (city.getNames().containsKey("zh-CN")) {
                    position.setCity(city.getNames().get("zh-CN"));
                } else if (city.getNames().containsKey("en")) {
                    position.setCity(city.getNames().get("en"));
                }
            }
            if (StringUtils.isEmpty(position.getCity())) {
                position.setCity(position.getProvince());
            }
        } catch (AddressNotFoundException e) {
            // 没有找到ip对应地理位置信息的话，默认为局域网
            position = UNKNOWN_POSITION;
        } catch (Exception e) {
            log.warn("parse ip position failed, ip: {}", ip, e);
            return UNKNOWN_POSITION;
        }
        return position;
    }

    public static Position doFind(String ip) {
        int ip_prefix_value = Integer.valueOf(ip.substring(0, ip.indexOf(".")));
        long ip2long_value = ip2long(ip);
        int start = index[ip_prefix_value];
        int max_comp_len = offset - 1028;
        long index_offset = -1;
        int index_length = -1;
        byte b = 0;
        for (start = start * 8 + 1024; start < max_comp_len; start += 8) {
            if (int2long(indexBuffer.getInt(start)) >= ip2long_value) {
                index_offset = bytesToLong(b,
                                           indexBuffer.get(start + 6),
                                           indexBuffer.get(start + 5),
                                           indexBuffer.get(start + 4));
                index_length = 0xFF & indexBuffer.get(start + 7);
                break;
            }
        }

        byte[] areaBytes;

        lock.lock();
        try {
            dataBuffer.position(offset + (int) index_offset - 1024);
            areaBytes = new byte[index_length];
            dataBuffer.get(areaBytes, 0, index_length);
        } finally {
            lock.unlock();
        }

        String[] temp = new String(areaBytes, Charset.forName("UTF-8")).split("\t", -1);
        String[] value = new String[3];
        for (int i = 0; i < value.length; i++) {
            if (i == 2 && StringUtils.isEmpty(temp[2].trim())) {
                value[i] = temp[1];
            } else {
                value[i] = temp[i];
            }
        }
        if (value[0].equals(value[1])) {
            return new Position(value[0], value[1], value[2], value[0]);
        } else if (!value[0].equals(value[1]) && value[1].equals(value[2])) {
            return new Position(value[0], value[1], value[2], value[0] + "." + value[1]);
        } else {
            return new Position(value[0], value[1], value[2], value[0] + "." + value[1] + "." + value[2]);
        }
    }

    private static void watch() {
        Executors.newScheduledThreadPool(1).scheduleAtFixedRate(new Runnable() {

            @Override
            public void run() {
                long time = ipFile.lastModified();
                if (time > lastModifyTime) {
                    lastModifyTime = time;
                    load();
                }
            }
        }, 1000L, 5000L, TimeUnit.MILLISECONDS);
    }

    public static void loadGeoLite() {
        InputStream inputStream = IpRegionalUtils.class.getResourceAsStream("/GeoLite2-City.mmdb");
        try {
            reader = new DatabaseReader.Builder(inputStream).withCache(new CHMCache()).build();
        } catch (IOException e) {
            log.warn("load geolite failed", e);
        }
    }

    public static void load() {
        // lastModifyTime = ipFile.lastModified();
        InputStream fin = null;
        lock.lock();
        try {
            dataBuffer = ByteBuffer.allocate(23002367);
            // System.out.println(Long.valueOf(ipFile.length()).intValue());
            fin = IpRegionalUtils.class.getResourceAsStream("/17monipdb.dat");
            int readBytesLength;
            byte[] chunk = new byte[4096];
            while (fin.available() > 0) {
                readBytesLength = fin.read(chunk);
                dataBuffer.put(chunk, 0, readBytesLength);
            }
            dataBuffer.position(0);
            int indexLength = dataBuffer.getInt();
            byte[] indexBytes = new byte[indexLength];
            dataBuffer.get(indexBytes, 0, indexLength - 4);
            indexBuffer = ByteBuffer.wrap(indexBytes);
            indexBuffer.order(ByteOrder.LITTLE_ENDIAN);
            offset = indexLength;

            int loop = 0;
            while (loop++ < 256) {
                index[loop - 1] = indexBuffer.getInt();
            }
            indexBuffer.order(ByteOrder.BIG_ENDIAN);
        } catch (IOException ioe) {
            log.warn("", ioe);
        } finally {
            try {
                if (fin != null) {
                    fin.close();
                }
            } catch (IOException e) {
                log.warn("", e);
            }
            lock.unlock();
        }
    }

    private static long bytesToLong(byte a, byte b, byte c, byte d) {
        return int2long((((a & 0xff) << 24) | ((b & 0xff) << 16) | ((c & 0xff) << 8) | (d & 0xff)));
    }

    private static int str2Ip(String ip) {
        String[] ss = ip.split("\\.");
        int a = Integer.parseInt(ss[0]);
        int b = Integer.parseInt(ss[1]);
        int c = Integer.parseInt(ss[2]);
        int d = Integer.parseInt(ss[3]);
        return (a << 24) | (b << 16) | (c << 8) | d;
    }

    private static long ip2long(String ip) {
        return int2long(str2Ip(ip));
    }

    private static long int2long(int i) {
        long l = i & 0x7fffffffL;
        if (i < 0) {
            l |= 0x080000000L;
        }
        return l;
    }

    private static final String STR_1 = "局域网";

    private static final String STR_2 = "其他";

    private static final String STR_3 = "--";

    private static final String STR_4 = "未知";

    /**
     * 获取地域信息
     *
     * @param position 地域
     * @return {@link String}
     */
    public static String getLocation(Position position) {
        String country = position.getCountry();
        String province = position.getProvince();
        String city = position.getCity();
        if (StringUtils.isEmpty(country) || STR_1.equals(country) || STR_3.equals(country) || STR_4.equals(country)) {
            country = STR_2;
        }
        if (StringUtils.isEmpty(province) || STR_1.equals(province) || STR_3.equals(province) || STR_4.equals(province)) {
            province = STR_2;
        }
        if (StringUtils.isEmpty(city) || STR_1.equals(city) || STR_3.equals(city) || STR_4.equals(city)) {
            city = STR_2;
        }
        if (country.equals(STR_2)) {
            return STR_2;
        }
        if (province.equals(STR_2)) {
            return country;
        }
        if (city.equals(STR_2)) {
            return country + "." + province;
        }
        return country + "." + province + "." + city;
    }

}
