package omni.audit.network.segment.handle.processors;

import com.quanzhi.awdb_core.IpCity;
import com.quanzhi.awdb_core.PositionDto;
import omni.audit.network.segment.handle.entity.IpPositionInfo;
import omni.audit.network.segment.handle.entity.Position;
import omni.audit.network.segment.handle.utils.IpRegionalUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Properties;

public class IpCityResolve {

    protected static List<IpPositionInfo> ipPositionConfigs;

    private static final String ENABLE_IP_REGIONAL_KEY = "ip.regional.enable";

    public static Properties handlerProperties = new Properties();

    protected static IpCity ipCity = IpCity.getInstance();

    public static Long lastTime = null;

    static {
        // 调用初始化方法
        initializeParameter();
    }

    private static void initializeParameter() {
        new Thread(() -> {
            String awdbPath = "/home/<USER>";
            String wryPath = "/home/<USER>";
            ipCity.init(awdbPath, wryPath);
        }).start();
    }

    public static Position resolveIpCity(String ip, List<IpPositionInfo> ipPositionInfos) {
        ipPositionConfigs = ipPositionInfos;
        return getIpCity(ip, true);
    }

    protected static Position getIpCity(String ip, boolean isMatchConfigIpCity) {
        Position position = null;
        // 离线库解析ip地域信息
        if (position == null) {
            position = offlineIpCity(ip);
        }
        // 解析不了的，用自定义方法再获取一次
        if (handlerProperties.getProperty(ENABLE_IP_REGIONAL_KEY) != null
            && Boolean.valueOf(handlerProperties.getProperty(ENABLE_IP_REGIONAL_KEY))) {
            if (position == null && !ip.contains(":")) {
                position = IpRegionalUtils.find(ip);
            }
        }
        if (position == null) {
            position = IpRegionalUtils.UNKNOWN_POSITION;
        }
        if (position != null) {
            position.setCountry((StringUtils.isEmpty(position.getCountry()) || "局域网".equals(position.getCountry())) ? IpRegionalUtils.UNKNOWN_POSITION_REPLACE : position.getCountry());
            position.setProvince((StringUtils.isEmpty(position.getProvince()) || "局域网".equals(position.getProvince())) ? IpRegionalUtils.UNKNOWN_POSITION_REPLACE : position.getProvince());
            position.setCity((StringUtils.isEmpty(position.getCity()) || "局域网".equals(position.getCity())) ? IpRegionalUtils.UNKNOWN_POSITION_REPLACE : position.getCity());
            position.setLocation(IpRegionalUtils.getLocation(position));
        }
        return position;
    }

    protected static Position offlineIpCity(String ip) {
        Position position = null;
        // initIpCity();

        PositionDto positionDto = ipCity.resolveIpCity(ip);
        if (positionDto != null) {
            position = new Position(positionDto.getCountry(), positionDto.getProvince(), positionDto.getCity(), "");
        }
        return position;
    }

}
