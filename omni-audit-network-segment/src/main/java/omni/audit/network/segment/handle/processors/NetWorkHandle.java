package omni.audit.network.segment.handle.processors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import lombok.extern.slf4j.Slf4j;
import omni.audit.network.segment.handle.entity.*;
import omni.audit.network.segment.handle.enums.RegionEnum;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.BeanListHandler;

import java.sql.Array;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class NetWorkHandle {

    // TODO 查询数据库填充
    public List<NetworkSegmentIpInfo> networkSegments = new ArrayList<>();

    private List<IpPositionInfo> ipPositionInfos;

    ObjectMapper objectMapper = new ObjectMapper();

    private static Cache<String, Optional<Map<String, Object>>> ipCache;

    private static final Long IP_CACHE_EXPIRE_MS = 120L;

    private static final Long IP_CACHE_MAXIMUM_SIZE = 500000L;

    private final Map<String, Integer> netMap = new HashMap<>();

    public NetWorkHandle(Map<String, Object> paramMap) {
        ipCache = CacheBuilder.newBuilder()
                              .maximumSize(IP_CACHE_MAXIMUM_SIZE)
                              .expireAfterWrite(Duration.ofMinutes(IP_CACHE_EXPIRE_MS))
                              .recordStats()
                              .build();
        init(paramMap);
    }

    private void init(Map<String, Object> paramMap) {
        JdbcConfig jdbcConfig = JdbcConfig.builder().username((String) paramMap.get("jdbcUser"))
                                          .jdbcUrl((String) paramMap.get("jdbcUrl"))
                                          .password((String) paramMap.get("jdbcPassword"))
                                          .schema(paramMap.get("jdbcSchema") == null ? "" : paramMap.get("jdbcSchema")
                                                                                                    .toString())
                                          .driverClassName("org.postgresql.Driver")
                                          .build();
        QueryRunner queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            List<NetEntity> netEntities = queryRunner.query(
                    "SELECT id,name,first_level AS firstLevel,second_level AS secondLevel,third_level AS thirdLevel,city ,country , province,location ,type ,network_segment AS networkSegment  from " + paramMap.get(
                            "jdbcSchema") + "." + paramMap.get("labelTableName"),
                    new BeanListHandler<>(NetEntity.class));
            netEntities.forEach(s -> {
                netMap.put(s.getName(), s.getId());
                NetworkSegmentIpInfo networkSegmentIpInfo = new NetworkSegmentIpInfo();
                networkSegmentIpInfo.setId(s.getName());
                networkSegmentIpInfo.setType(s.getType());
                Array networkSegment = s.getNetworkSegment();
                try {
                    List<NetworkSegment.NetworkSegmentIp> networkSegmentList =
                            Arrays.stream((String[]) networkSegment.getArray())
                                  .map(segment -> {
                                      try {
                                          return objectMapper.readValue(
                                                  segment,
                                                  NetworkSegment.NetworkSegmentIp.class);
                                      } catch (JsonProcessingException e) {
                                          throw new RuntimeException(e);
                                      }
                                  })
                                  .collect(Collectors.toList());
                    networkSegmentIpInfo.setNetworkSegmentList(networkSegmentList);
                } catch (Exception e) {
                    throw new RuntimeException("Failed to parse one or more network segments", e);
                }
                networkSegmentIpInfo.setDomain(new NetworkSegment.Domain(s.getFirstLevel(),
                                                                         s.getSecondLevel(),
                                                                         s.getThirdLevel()));
                networkSegmentIpInfo.setPosition(new Position(s.getCountry(),
                                                              s.getProvince(),
                                                              s.getCity(),
                                                              s.getLocation()));
                networkSegments.add(networkSegmentIpInfo);
            });
        } catch (Exception e) {
            log.error("query label rules error:", e);
        }
    }

    public Integer[] handle(String ip) {
        Map<String, Object> ipRes = getNetworkDomainsAndArea(ip);
        if (ipRes != null && ipRes.get("network") != null) {
            List<NetworkDomain> network = (List<NetworkDomain>) ipRes.get("network");
            Integer[] array = network.stream().map(s -> {
                String id = s.getId();
                Integer netId = netMap.get(id);
                return netId != null ? netId : 0;
            }).toArray(Integer[]::new);
            return array;
        }
        return new Integer[]{};
    }

    protected Map<String, Object> getNetworkDomainsAndArea(String ip) {
        return NetworkDomainResolve.getNetworkDomainsAndArea(ip, networkSegments);
    }

    private Map<String, Object> loadIpInfo(String ip) {
        IpInfo ipInfo = new IpInfo();
        // 1、获取国家、省份、城市
        Position position = IpCityResolve.resolveIpCity(ip, ipPositionInfos);
        ipInfo.setCountry(position.getCountry());
        ipInfo.setProvince(position.getProvince());
        ipInfo.setCity(position.getCity());
        // 2、获取地域
        RegionEnum regionEnum = resolveRegion(position);
        ipInfo.setRegional(regionEnum.name());
        // 3、获取访问域
        List<NetworkDomain> networkDomains = NetworkDomainResolve.getNetworkDomains(ip, networkSegments);
        List<String> networkDomainIds = new ArrayList<>();
        for (NetworkDomain networkDomain : networkDomains) {
            networkDomainIds.add(networkDomain.getId());
        }
        ipInfo.setVisitDomains(networkDomainIds);
        ipInfo.setNetworkDomains(networkDomainIds);

        Map<String, Object> ipInfoMap = (Map<String, Object>) objectMapper.convertValue(ipInfo, Map.class);
        return ipInfoMap;
    }

    public RegionEnum resolveRegion(Position position) {
        if (position.getCountry().equals("局域网")
            || position.getCountry().equals("其他")
            || (position.getCountry().equals("中国") && (!position.getProvince()
                                                                  .equals("香港") || !position.getProvince()
                                                                                              .equals("澳门") || !position.getProvince()
                                                                                                                          .equals("台湾")))) {
            return RegionEnum.IN_COUNTRY;
        } else {
            return RegionEnum.OVERSEAS;
        }
    }

}
