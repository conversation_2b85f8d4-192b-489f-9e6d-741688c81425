package omni.audit.network.segment.handle.entity;

import lombok.Data;

import java.util.List;

@Data
public class IpInfo {

    /**
     * 该ip对应的网段id集合
     */
    private List<String> networkDomains;

    /**
     * 访问域
     */
    @Deprecated
    private List<String> visitDomains;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 地域
     */
    private String regional;

}