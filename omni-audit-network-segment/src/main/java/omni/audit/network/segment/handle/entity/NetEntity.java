package omni.audit.network.segment.handle.entity;

import lombok.Data;

import java.sql.Array;

@Data
public class NetEntity {

    private Integer id;

    private String name;

    private String firstLevel;

    private String secondLevel;

    private String thirdLevel;

    private String city;

    private String country;

    private String province;

    private String location;

    private Integer type;

    private Array networkSegment;

}
