package omni.audit.network.segment.handle.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigInteger;
import java.util.List;

@Data
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class NetworkSegmentIpInfo extends NetworkSegment {

    private List<IpInfo> segmentIpInfos;

    @Data
    public static class IpInfo {

        /**
         * 标识原来的IP配置是否IPv4形式的，识别要用，不然会有问题，true-是IPv4，false-IPv6
         */
        private Boolean ipv4Flag;

        private String startIp;

        private String endIp;

        private Integer ipType;

        private BigInteger ips;

        private BigInteger ipe;

    }

}
