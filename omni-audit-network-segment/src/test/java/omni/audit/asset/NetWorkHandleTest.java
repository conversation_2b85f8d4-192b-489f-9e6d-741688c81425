//package omni.audit.asset;
//
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.core.type.TypeReference;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import omni.audit.network.segment.handle.entity.NetworkSegmentIpInfo;
//import omni.audit.network.segment.handle.processors.NetWorkHandle;
//import org.junit.Test;
//
//import java.util.List;
//import java.util.Map;
//
//import static org.junit.Assert.*;
//
//public class NetWorkHandleTest {
//
//    @Test
//    public void testHandleWithValidIp() {
//        String text = "[\n" +
//                "  {\n" +
//                "    \"id\": \"局域网-其他\",\n" +
//                "    \"networkSegmentList\": [],\n" +
//                "    \"domain\": {\n" +
//                "      \"firstLevel\": \"局域网\",\n" +
//                "      \"secondLevel\": \"其他\"\n" +
//                "    },\n" +
//                "    \"position\": {\n" +
//                "      \"country\": \"\"\n" +
//                "    },\n" +
//                "    \"type\": 1\n" +
//                "  },\n" +
//                "  {\n" +
//                "    \"id\": \"互联网-境内\",\n" +
//                "    \"networkSegmentList\": [],\n" +
//                "    \"domain\": {\n" +
//                "      \"firstLevel\": \"互联网\",\n" +
//                "      \"secondLevel\": \"境内\"\n" +
//                "    },\n" +
//                "    \"position\": {\n" +
//                "      \"country\": \"\"\n" +
//                "    },\n" +
//                "    \"type\": 1\n" +
//                "  },\n" +
//                "  {\n" +
//                "    \"id\": \"互联网-境外\",\n" +
//                "    \"networkSegmentList\": [],\n" +
//                "    \"domain\": {\n" +
//                "      \"firstLevel\": \"互联网\",\n" +
//                "      \"secondLevel\": \"境外\"\n" +
//                "    },\n" +
//                "    \"position\": {\n" +
//                "      \"country\": \"\"\n" +
//                "    },\n" +
//                "    \"type\": 1\n" +
//                "  }\n" +
//                "]";
//        ObjectMapper objectMapper = new ObjectMapper();
//        NetWorkHandle netWorkHandle = new NetWorkHandle();
//        try {
//            List<NetworkSegmentIpInfo> networkSegments = objectMapper.readValue(
//                    text,
//                    new TypeReference<List<NetworkSegmentIpInfo>>() {}
//            );
//            netWorkHandle.networkSegments = networkSegments;
//        } catch (JsonProcessingException e) {
//            throw new RuntimeException(e);
//        }
//        String validIp = "***********";
//
//        Map<String, Object> result = netWorkHandle.handle(validIp);
//        System.out.println(result);
//        assertNotNull(result);
//        assertFalse(result.isEmpty());
//        // Add more specific assertions based on expected return values
//    }
//
//    @Test
//    public void testHandleWithNullIp() {
//        NetWorkHandle netWorkHandle = new NetWorkHandle();
//
//        assertThrows(NullPointerException.class, () -> {
//            netWorkHandle.handle(null);
//        });
//    }
//
//    @Test
//    public void testHandleWithEmptyIp() {
//        NetWorkHandle netWorkHandle = new NetWorkHandle();
//        String emptyIp = "";
//
//        Map<String, Object> result = netWorkHandle.handle(emptyIp);
//
//        // Assert based on expected behavior - either empty map or exception
//        assertNotNull(result);
//    }
//
//    @Test
//    public void testHandleWithInvalidIpFormat() {
//        NetWorkHandle netWorkHandle = new NetWorkHandle();
//        String invalidIp = "300.400.500.600";
//
//        Map<String, Object> result = netWorkHandle.handle(invalidIp);
//
//        // Assert based on expected behavior for invalid IPs
//        assertNotNull(result);
//    }
//
//    @Test
//    public void testHandleWithLocalhostIp() {
//        NetWorkHandle netWorkHandle = new NetWorkHandle();
//        String localhostIp = "127.0.0.1";
//
//        Map<String, Object> result = netWorkHandle.handle(localhostIp);
//
//        assertNotNull(result);
//        // Add specific assertions for localhost IP handling
//    }
//
//    @Test
//    public void testHandleWithIpv6Address() {
//        NetWorkHandle netWorkHandle = new NetWorkHandle();
//        String ipv6Address = "2001:0db8:85a3:0000:0000:8a2e:0370:7334";
//
//        Map<String, Object> result = netWorkHandle.handle(ipv6Address);
//
//        assertNotNull(result);
//        // Add assertions for IPv6 handling if supported
//    }
//}
