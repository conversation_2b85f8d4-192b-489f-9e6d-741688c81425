package com.quanzhi.omni.audit.parse;

import com.alibaba.fastjson.JSON;
import org.junit.Test;

import static org.junit.Assert.*;

public class SqlParseProcessorTest {

    private SqlParseProcessor sqlParseProcessor = new SqlParseProcessor();

    @Test
    public void process() {
        String sql = "select * from t_user";
        System.out.println(JSON.toJSONString(sqlParseProcessor.process(sql)));
    }

    @Test
    public void testProcess() {
        String sql = "select * from t_user";
        System.out.println(JSON.toJSONString(sqlParseProcessor.process(sql, "PostgreSQL")));
    }

    @Test
    public void testDDLProcess() {
        String sql = "DROP TABLE test_fetch CASCADE";
        System.out.println(JSON.toJSONString(sqlParseProcessor.process(sql, "PostgreSQL")));
    }

    @Test
    public void testAlterProcess() {
        String sql = "CREATE TABLE IF NOT EXISTS public.filter (\n" +
                "                        id VARCHAR(255) PRIMARY KEY,\n" +
                "                        \"type\" VARCHAR(50) NOT NULL,\n" +
                "                        title VARCHAR(255) NOT NULL,\n" +
                "                        priority INTEGER NOT NULL,\n" +
                "                        keep_enable BOOLEAN,\n" +
                "                        enabled BOOLEAN DEFAULT TRUE,\n" +
                "                        create_time BIGINT,\n" +
                "                        update_time BIGINT,\n" +
                "                        del_flag BOOLEAN DEFAULT FALSE,\n" +
                "                        match_rule JSONB\n" +
                ");\n";
        System.out.println(JSON.toJSONString(sqlParseProcessor.process(sql, "postgresql")));
    }
}
