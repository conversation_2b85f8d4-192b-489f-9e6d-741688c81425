create table if not exists oad_db_event
(
    timestamp      UInt64  DEFAULT 883587661999 CODEC(Delta, ZSTD(1)),
    id             String  DEFAULT 'Xiaobao is the most handsome' CODEC(ZSTD(1)),
    eventTypes     Array(UInt8)  DEFAULT [] CODEC(T64, ZSTD(1)),
    net_dstIp_v4   IPv4 DEFAULT '0.0.0.0',
    net_dstIp_v6   IPv6 DEFAULT '::',
    net_dstPort    UInt16  DEFAULT 8888 CODEC(T64, ZSTD(1)),
    net_srcIp_v4   IPv4 DEFAULT '0.0.0.0',
    net_srcIp_v6   IPv6 DEFAULT '::',
    net_srcPort    UInt16  DEFAULT 8888 CODEC(T64, ZSTD(1)),
    accessDomains  Array(UInt8) DEFAULT [] CODEC(T64, ZSTD(1)),
    account        LowCardinality(String) DEFAULT '',
    accountType    LowCardinality(String) DEFAULT '',
    srvAddress     LowCardinality(String) DEFAULT '',
    srvName        LowCardinality(String) DEFAULT '',
    srvType        LowCardinality(String) DEFAULT '',
    srvLevel       UInt8  DEFAULT 0 CODEC(T64, ZSTD(1)),
    dbName         LowCardinality(String) DEFAULT '',
    dbType         LowCardinality(String) DEFAULT '',
    dbLevel        UInt8  DEFAULT 0 CODEC(T64, ZSTD(1)),
    tableName      LowCardinality(String) DEFAULT '',
    tableLevel     LowCardinality(String) DEFAULT '',
    optionMethod   LowCardinality(String) DEFAULT '',
    deployDomains  Array(UInt8) DEFAULT [] CODEC(T64, ZSTD(1)),
    reqDataLabelIds Array(UInt16) DEFAULT [] CODEC(T64, ZSTD(1)),
    rspDataLabelIds Array(UInt16) DEFAULT [] CODEC(T64, ZSTD(1)),
    rspTime        UInt64 DEFAULT 0 CODEC(T64, ZSTD(1)),
    rspStatus      UInt32 DEFAULT 0 CODEC(T64, ZSTD(1))
    -- INDEX dateIndex date TYPE set(0) GRANULARITY 10000,
    -- INDEX apiUrlIndex apiUrl TYPE tokenbf_v1(20480, 2, 0) GRANULARITY 4,
    -- INDEX idx_rspContentLength rspContentLength TYPE minmax GRANULARITY 4,
    -- INDEX classificationsBfIndex classifications TYPE bloom_filter(0.008) GRANULARITY 4,
    -- PROJECTION apiUrl_proj ( SELECT apiUrl,timestamp,id ORDER BY apiUrl,timestamp )
    )
    engine = MergeTree()
    PARTITION BY toDate(timestamp)
    ORDER BY (timestamp, id, sipHash64(id))
    SAMPLE BY sipHash64(id)
    SETTINGS index_granularity = 8192,max_suspicious_broken_parts=1000;
