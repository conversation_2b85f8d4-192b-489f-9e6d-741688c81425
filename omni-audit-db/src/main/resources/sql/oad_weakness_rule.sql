-- 创建 WeaknessRule 表
CREATE TABLE IF NOT EXISTS oad_weakness_rule (
    -- 自增主键
    id         bigint generated by default as identity  primary key,
    -- 规则名称
    name VARCHAR(255),
    -- 规则类型
    type VARCHAR(255),
    -- 主体类型
    entity_type VARCHAR(255),
    -- 规则级别
    level INT,
    --  匹配规则
    match_rule TEXT,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
    );