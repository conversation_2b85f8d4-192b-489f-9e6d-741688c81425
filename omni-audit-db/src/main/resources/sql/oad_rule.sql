CREATE TABLE if not exists oad_rule (
    id VARCHAR(255) PRIMARY KEY,
    category VARCHAR(50),
    match_rule JSONB,
    enabled BOOLEAN DEFAULT TRUE,
    related_left_var_order JSONB,
    related_right_var_order JSONB,
    create_time BIGINT,
    update_time BIGINT,
    del_flag BOOLEAN DEFAULT FALSE,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
    );
