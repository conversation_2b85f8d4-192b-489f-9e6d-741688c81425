CREATE TABLE IF NOT EXISTS oad_db_srv
(
    id         bigint generated by default as identity  primary key,
    srv_uid  VARCHAR UNIQUE NOT NULL,
    addr           VARCHAR,
    name           VARCHAR,
    type           VARCHAR,
    sensi_level    INT,
    risk_level     VARCHAR,
    access_domains INT[],
    deploy_domains INT[],
    req_labels     INT[],
    rsp_labels     INT[],
    status         INT,
    db_cnt         INT          DEFAULT 0,
    table_cnt      INT          DEFAULT 0,
    weakness_cnt   INT          DEFAULT 0,
    weakness_ids   INT[],
    risk_cnt       INT,
    risk_ids       INT[],
    user_cnt       INT          DEFAULT 0,
    flow_sources   VARCHAR,
    biz_system     VARCHAR,
    operate        INT,
    first_at       BIGINT,
    last_at        BIGINT,
    created_at     TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at     TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);