CREATE TABLE IF NOT EXISTS db_assets (
   id             SERIAL PRIMARY KEY,
   db_unique_id   VARCHAR,
   name           VARCHA<PERSON>,
   version        VARCHA<PERSON>,
   type           VARCHAR,
   sensi_level    VARCHAR,
   risk_level     VARCHAR,
   srv_id         INT,
   req_labels     INT[],
   rsp_labels     INT[],
   status         INT,
   table_cnt      INT  DEFAULT 0,
   weakness_cnt   INT DEFAULT 0,
   weakness_names TEXT[],
   risk_cnt INT   DEFAULT 0,
   risk_names     TEXT[],
   user_cnt       INT  DEFAULT 0,
   biz_system     VARCHAR,
   operate        INT,
   first_at       BIGINT,
   last_at        BIGINT ,
   created_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
   updated_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);