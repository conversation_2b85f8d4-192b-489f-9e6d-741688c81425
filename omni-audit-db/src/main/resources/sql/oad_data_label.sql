CREATE TABLE IF NOT EXISTS oad_data_label (
    id          BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    "type"      VARCHAR(50)  NOT NULL,
    "name"       VARCHAR(255) NOT NULL,
    "level"      INTEGER      NOT NULL,
    locations TEXT[],
    discover_enable     BOOLEAN       DEFAULT FALSE,
    description VARCHAR(255),
    del_flag    BOOLEAN               DEFAULT FALSE,
    dlp_policy  JSONB,
    create_time BIGINT,
    update_time  BIGINT,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);
