CREATE TABLE if not exists oad_function (
    id VARCHAR(255) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    title VA<PERSON>HAR(255),
    category VARCHAR(50),
    "desc" VARCHAR(50),
    constraint_event JSONB,
    constraint_features JSON<PERSON>,
    output_type VARCHAR(50),
    args JSON<PERSON>,
    definition JSONB,
    type VARCHAR(50),
    support_main_objects JSONB,
    support_cumulative_objects JSONB,
    create_time BIGINT,
    update_time BIGINT,
    del_flag BOOLEAN DEFAULT FALSE,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
    );
