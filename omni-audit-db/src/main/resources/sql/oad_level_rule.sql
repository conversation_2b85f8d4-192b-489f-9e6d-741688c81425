-- 创建 label_rule 表
CREATE TABLE IF NOT EXISTS oad_level_rule (
    -- 自增主键
    id   int generated by default as identity  primary key,
    -- 类型
    level_type VARCHAR(255),
    name VARCHAR(255),
    sort INT,
    -- 规则
    match_rule TEXT,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
    );