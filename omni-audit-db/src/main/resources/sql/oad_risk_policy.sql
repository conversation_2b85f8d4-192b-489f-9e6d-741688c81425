CREATE TABLE IF NOT EXISTS oad_risk_policy
(
    id               VARCHAR     NOT NULL PRIMARY KEY,
    builtin          BOOLEAN,
    type             VARCHAR,
    name             VARCHAR,
    "group"          VARCHAR,
    event_rule       JSONB,
    quota_rule       JSONB,
    level            INT,
    threat_label     TEXT,
    enable           BOOLEAN,
    allow_list_types TEXT[],
    del_flag         BOOLEAN,
    sample_config    JSONB,
    "desc"           TEXT,
    agg_config       JSONB,
    period           JSONB,
    entities         JSONB,
    created_at       TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at       TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);


insert into oad_risk_policy (id, builtin, type, name, "group", event_rule, quota_rule, level, threat_label, enable, allow_list_types, del_flag, sample_config, "desc", agg_config, period, entities)
values  ('1007', true, 'ip', 'DBA执行高危操作', '高危操作', '{}', '{"decision": {"logic": "1"}, "ruleConditions": [{"seq": "1", "type": "EVENT_FEATURE", "value": "select *", "feature": "reqSql", "operator": "STR_CONTAINS_IGNORE_CASE", "valueType": "PRIMITIVE", "featureType": "FEATURE"}]}', 3, null, true, '{IP}', false, '{"limit": 0}', null, 'null', '{"time": 1, "unit": "TIMES"}', '[{"name": "IP", "type": "IP", "value": "netSrcIp"}]');