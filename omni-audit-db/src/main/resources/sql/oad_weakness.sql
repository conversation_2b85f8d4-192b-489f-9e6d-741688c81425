-- Create the main table for vulnerabilities
CREATE TABLE IF NOT EXISTS oad_weakness
(
    id             bigint generated by default as identity  primary key,
    level          INT         NOT NULL,
    state          VARCHAR,
    operation_id   VARCHAR UNIQUE,
    weakness_id    INT,
    srv_id         INT,
    db_id          INT,
    table_id       INT,
    first_at       BIGINT      NOT NULL,
    last_at        BIGINT      NOT NULL,
    created_at     TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at     TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);
CREATE INDEX idx_oad_weakness_srv_id ON oad_weakness (srv_id);
CREATE INDEX idx_oad_weakness_db_id ON oad_weakness (db_id);
CREATE INDEX idx_oad_weakness_table_id ON oad_weakness (table_id);
