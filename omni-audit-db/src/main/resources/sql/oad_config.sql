create table if not exists oad_config
(
    id         bigint generated by default as identity  primary key,
    data_id    varchar(255) not null,
    group_id   varchar(128) not null,
    content    text,
    md5        varchar(32),
    created_at TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);

create unique index if not exists oad_config_data_id_group_uindex
    on oad_config (data_id, group_id);
create index if not exists oad_config_group_id_index
    on oad_config (group_id);
create index if not exists oad_config_data_id_index
    on oad_config (data_id);


insert into oad_config(data_id, group_id, content)
values ('entity.relation.json', 'risk', '{
  "entities": [
    {
      "type": "IP",
      "name": "IP",
      "value": "netScrIp",
      "featureType": "FEATURE",
      "ruleConditions": [
        {
          "name": "ip",
          "type": "MESSAGE"
        }
      ]
    }，
    {
      "type": "ACCOUNT",
      "name": "ACCOUNT",
      "value": "reqDbUser",
      "featureType": "FEATURE",
      "ruleConditions": [
        {
          "name": "account",
          "type": "MESSAGE"
        }
      ]
    }
  ],
  "entityRelated": [
    {
      "related": [
        {
          "type": "IP",
          "value": "ip"
        }
      ],
      "name": "IP",
      "periods": [
        {
          "time": 1,
          "unit": "TIMES"
        }
      ]
    },
    {
      "related": [
        {
          "type": "ACCOUNT",
          "value": "account"
        }
      ],
      "name": "ACCOUNT",
      "periods": [
        {
          "time": 1,
          "unit": "TIMES"
        }
      ]
    }
  ]
}');
