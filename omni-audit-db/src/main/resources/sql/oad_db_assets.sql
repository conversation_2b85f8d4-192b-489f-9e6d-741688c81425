CREATE TABLE IF NOT EXISTS oad_db_assets (
   id         bigint generated by default as identity  primary key,
   db_uid   VA<PERSON>HAR UNIQUE NOT NULL,
   name           <PERSON><PERSON><PERSON><PERSON>,
   version        VARCHAR,
   type           VARCHAR,
   sensi_level    INT,
   risk_level     VARCHAR,
   srv_id         BIGINT NOT NULL,
   req_labels     INT[],
   rsp_labels     INT[],
   status         INT,
   table_cnt      INT  DEFAULT 0,
   weakness_cnt   INT DEFAULT 0,
   weakness_ids   INT[],
   risk_cnt INT   DEFAULT 0,
   risk_ids       INT[],
   user_cnt       INT  DEFAULT 0,
   operate        INT,
   first_at       BIGINT,
   last_at        BIGINT ,
   created_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
   updated_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);
CREATE INDEX idx_oad_db_assets_id ON oad_db_assets (srv_id);
