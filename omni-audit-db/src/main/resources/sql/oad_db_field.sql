-- 数据库字段表
CREATE TABLE IF NOT EXISTS oad_db_field(
    id         bigint generated by default as identity  primary key,
    field_uid  VARCHAR UNIQUE NOT NULL,
    name             VARCHAR     NOT NULL,
    type             VA<PERSON>HAR,
    table_id         INT NOT NULL,
    db_id            INT NOT NULL,
    srv_id           INT NOT NULL,
    sensi_level      INT NOT NULL,
    risk_level       VARCHAR,
    req_labels       INT[],
    rsp_labels       INT[],
    status           VARCHAR,
    operate          INT,
    first_at         BIGINT,
    last_at          BIGINT,
    created_at       TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at       TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);
