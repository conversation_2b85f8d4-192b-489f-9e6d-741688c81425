-- 创建 label_rule 表
CREATE TABLE IF NOT EXISTS oad_net (
    -- 自增主键
    id  int generated by default as identity  primary key,
    name VARCHAR(255),
    first_level VARCHAR(255),
    second_level VARCHAR(255),
    third_level VARCHAR(255),
    city VARCHAR(255),
    country VARCHAR(255),
    province VARCHAR(255),
    location VARCHAR(255),
    type INT,
    network_segment TEXT[],
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
    );