CREATE TABLE if not exists oad_feature (
    id VARCHAR(255) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    title VA<PERSON>HA<PERSON>(255),
    type VA<PERSON><PERSON><PERSON>(50),
    category VARCHAR(50),
    subject_obj VARCHAR(255),
    kind VARCHAR(50),
    description VARCHAR(50),
    related_event_name VARCHAR(255),
    option_enable BOOLEAN,
    use_enable BOOLEAN,
    options JSONB,
    disable_options JSONB,
    support_categories JSONB,
    right_constraints JSONB,
    create_time BIGINT,
    update_time BIGINT,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
    );
