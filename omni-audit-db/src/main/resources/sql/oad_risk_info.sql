create table if not exists oad_risk_info
(
    id              bigint generated by default as identity primary key,
    risk_id         varchar unique,
    agg_risk_id     varchar unique,
    operation_id    varchar unique,
    policy_id       int,
    db_id           bigint,
    srv_id          bigint,
    table_id        bigint,
    ip              varchar,
    account         varchar,
    name            varchar,
    type            varchar,
    "desc"          varchar,
    level           int,
    access_domains  int[],
    deploy_domains  int[],
    first_at        bigint,
    last_at         bigint,
    update_time     bigint,
    state           int,
    version         int,
    sample_count    int,
    entities        jsonb,
    policy_snapshot jsonb,
    date            varchar,
    created_at      timestamptz not null default (now() at time zone 'Asia/Shanghai'),
    updated_at      timestamptz not null default (now() at time zone 'Asia/Shanghai')
);
CREATE INDEX idx_oad_risk_info_srv_id ON oad_risk_info (srv_id);
CREATE INDEX idx_oad_risk_info_db_id ON oad_risk_info (db_id);
CREATE INDEX idx_oad_risk_info_table_id ON oad_risk_info (table_id);
