CREATE TABLE IF NOT EXISTS oad_filter
(
    id          BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    "type"      VARCHAR(50)  NOT NULL,
    title       VARCHAR(255) NOT NULL,
    priority    INTEGER      NOT NULL,
    keep_enable BOOLEAN,
    enabled     BOOLEAN               DEFAULT TRUE,
    del_flag    BOOLEAN               DEFAULT FALSE,
    match_rule  JSONB,
    create_time BIGINT,
    update_time  BIGINT,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);
