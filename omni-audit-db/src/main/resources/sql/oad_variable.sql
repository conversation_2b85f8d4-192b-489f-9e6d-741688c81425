CREATE TABLE if not exists oad_variable(
    id VARCHAR(255) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    title VA<PERSON>HAR(255),
    description VARCHAR(50),
    category VARCHAR(50),
    kind VARCHAR(50),
    type VA<PERSON><PERSON><PERSON>(50),
    pre_rule JSONB,
    enabled BOOLEAN DEFAULT TRUE,
    function_name VA<PERSON><PERSON><PERSON>(255),
    args JSON<PERSON>,
    has_baseline BOOLEAN DEFAULT FALSE,
    baseline_name VARCHAR(255),
    "period" JSONB,
    suggestion TEXT,
    subject_obj VARCHAR(255),
    entities JSONB,
    algorithm VARCHAR(255),
    business_properties JSONB,
    related_left_var_order JSONB,
    related_right_var_order JSONB,
    mode VARCHAR(50),
    create_time BIGINT,
    update_time BIGINT,
    del_flag BOOLEAN DEFAULT FALSE,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
    );
