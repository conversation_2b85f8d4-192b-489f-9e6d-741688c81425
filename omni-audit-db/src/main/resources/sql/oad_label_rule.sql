-- 创建 label_rule 表
CREATE TABLE IF NOT EXISTS oad_label_rule (
    -- 自增主键
    id   int generated by default as identity  primary key,
    -- 标签名称
    name VARCHAR(255),
    -- 标签类型
    first_class VARCHAR(255),
    -- 是否开启
    enabled BOOLEAN,
    -- 标签描述
    description TEXT,
    -- 标签类型
     label_type VARCHAR(255),
    -- 规则
    match_rule TEXT,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
    );