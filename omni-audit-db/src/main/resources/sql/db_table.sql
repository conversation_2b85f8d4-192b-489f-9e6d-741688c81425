CREATE TABLE IF NOT EXISTS db_table(
    id               SERIAL PRIMARY KEY,
    table_unique_id  VARCHAR,
    name             VARCHAR     NOT NULL,
    db_id            INT,
    srv_id           INT,
    sensi_level      VARCHAR,
    risk_level       VARCHAR,
    column_cnt       INT         DEFAULT 0,
    sensi_column_cnt INT         DEFAULT 0,
    req_labels       INT[],
    rsp_labels       INT[],
    status           VARCHAR,
    risk_cnt         INT                  DEFAULT 0,
    risk_names       TEXT[],
    biz_system       VARCHAR,
    operate          INT,
    first_at         BIGINT,
    last_at          BIGINT,
    created_at       TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at       TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')

);