CREATE TABLE IF NOT EXISTS oad_db_table(
    id         bigint generated by default as identity  primary key,
    table_uid  VARCHAR UNIQUE NOT NULL,
    name             <PERSON><PERSON><PERSON>R     NOT NULL,
    db_id            BIGINT NOT NULL,
    srv_id           BIGINT NOT NULL,
    sensi_level      INT,
    risk_level       VARCHAR,
    column_cnt       INT         DEFAULT 0,
    sensi_column_cnt INT         DEFAULT 0,
    req_labels       INT[],
    rsp_labels       INT[],
    status           VARCHAR,
    risk_cnt         INT                  DEFAULT 0,
    risk_ids         INT[],
    operate          INT,
    first_at         BIGINT,
    last_at          BIGINT,
    created_at       TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at       TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')

);
CREATE INDEX idx_oad_db_srv_id ON oad_db_table (srv_id);
CREATE INDEX idx_oad_db_assets_id ON oad_db_table (db_id);
