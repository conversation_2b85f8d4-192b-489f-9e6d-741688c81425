CREATE TABLE IF NOT EXISTS db_srv
(
    id             SERIAL PRIMARY KEY,
    srv_unique_id  VARCHAR,
    addr           VARCHAR,
    name           VARCHAR,
    type           VARCHAR,
    sensi_level    VARCHAR,
    risk_level     VARCHAR,
    access_domains INT[],
    deploy_domains INT[],
    req_labels     INT[],
    rsp_labels     INT[],
    status         INT,
    db_cnt         INT          DEFAULT 0,
    table_cnt      INT          DEFAULT 0,
    weakness_cnt   INT          DEFAULT 0,
    weakness_names TEXT[],
    risk_cnt       INT,
    risk_names     TEXT[],
    user_cnt       INT          DEFAULT 0,
    flow_sources   VARCHAR,
    biz_system     VARCHAR,
    operate        INT,
    first_at       BIGINT,
    last_at        BIGINT,
    created_at     TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at     TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);