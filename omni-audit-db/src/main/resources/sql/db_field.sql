-- 数据库字段表
CREATE TABLE IF NOT EXISTS db_field(
    id               SERIAL PRIMARY KEY,
    field_unique_id  VARCHAR,
    name             VARCHAR     NOT NULL,
    type             INT,
    table_id         INT,
    db_id            INT,
    srv_id           INT,
    sensi_level      VARCHAR,
    risk_level       VARCHAR,
    req_labels       INT[],
    rsp_labels       INT[],
    status           VARCHAR,
    operate          INT,
    first_at         BIGINT,
    last_at          BIGINT,
    created_at       TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at       TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);
