package com.quanzhi.audit;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit.weakness.entity.WeaknessMetadata;
import com.quanzhi.audit.weakness.entity.WeaknessRule;
import com.quanzhi.audit.weakness.service.WeaknessService;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.quanzhi.re.core.RuleEngine;
import com.quanzhi.re.core.defaults.FeatureDefault;
import com.quanzhi.re.core.domain.engine.CompiledRule;
import com.quanzhi.re.core.domain.entity.Decision;
import com.quanzhi.re.core.domain.entity.DecisionResult;
import com.quanzhi.re.core.domain.entity.EventContext;
import com.quanzhi.re.core.domain.entity.ValueType;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import com.quanzhi.re.core.domain.entity.po.RuleCondition;
import omni.audit.common.client.redis.RedisConfig;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.MapListHandler;
import org.apache.commons.dbutils.handlers.ScalarHandler;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 * create at 2025/5/12 14:21
 * @description:
 **/
public class WeaknessServiceTest {


    private WeaknessService weaknessService;
    private QueryRunner queryRunner;


    @Before
    public void setUp() {
        JdbcConfig jdbcConfig = JdbcConfig.builder().username("postgres").jdbcUrl("*********************************************")
                .password("password123")
                .driverClassName("org.postgresql.Driver")
                .build();
        queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        RedisConfig redisConfig = RedisConfig.builder()
                .host("*************")
                .port(6380)
                .password("qz@123#pwd")
                .build();
        weaknessService = new WeaknessService(jdbcConfig, new Properties(), redisConfig);
    }

    /**
     * 测试插入一条弱点规则
     */
    @Test
    public void testInsertWeaknessRule() throws SQLException {
        WeaknessRule weaknessRule = createWeaknessRule();
        String sql = "INSERT INTO weakness_rule (name, type, level, matchRule) " +
                "VALUES (?, ?, ?, ?)";
        Object[] params = {weaknessRule.getName(), weaknessRule.getType(), weaknessRule.getLevel(), JSONObject.toJSONString(weaknessRule.getMatchRule())};
        Integer insertCnt = queryRunner.insert(sql, new ScalarHandler<Integer>(), params);
        System.out.println(insertCnt);
    }


    @Test
    public void insertDefaultWeaknessRule() throws SQLException {
        List<WeaknessRule> weaknessRules = createDefaultWeaknessRule();
        String sql = "INSERT INTO weakness_rule (id,name, type, level, matchRule) " +
                "VALUES (?,?, ?, ?, ?)";

        for (int i = 0; i < weaknessRules.size(); i++) {
            WeaknessRule weaknessRule = weaknessRules.get(i);
            Object[] params = {weaknessRule.getId(),weaknessRule.getName(), weaknessRule.getType(), weaknessRule.getLevel(), JSONObject.toJSONString(weaknessRule.getMatchRule())};
            Integer insertCnt = queryRunner.insert(sql, new ScalarHandler<Integer>(), params);
            System.out.println("weakness rule insert:"+insertCnt);
        }
    }

    private List<WeaknessRule> createDefaultWeaknessRule() {
        List<WeaknessRule> weaknessRules = new ArrayList<>();
        weaknessRules.add(createWeaknessOverlyLargePermissions());
        weaknessRules.add(createWeaknessOverRelianceDefaultConfiguration());
        return weaknessRules;
    }


    private WeaknessRule createWeaknessOverlyLargePermissions() {
        WeaknessRule weaknessRule = new WeaknessRule();
        weaknessRule.setId(1);
        weaknessRule.setType("权限类");
        weaknessRule.setName("权限过大");
        weaknessRule.setLevel(1);
        MatchRule matchRule = new MatchRule();
        matchRule.setId("matchId1");

        RuleCondition ruleCondition = new RuleCondition();
        ruleCondition.setSeq("1");
        ruleCondition.setFeature("reqSql");
        ruleCondition.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition.setOperator("REGEX");
        ruleCondition.setValue("(?i)\\b(ALTER\\s+SYSTEM|CREATE\\s+USER|DROP\\s+USER|GRANT\\s+(ALL\\s+PRIVILEGES|DBA|SYSDBA)|CREATE\\s+ANY\\s+\\w+|DROP\\s+ANY\\s+\\w+|EXECUTE\\s+ANY\\s+PROCEDURE)\\b");
        ruleCondition.setValueType(ValueType.PRIMITIVE.name());

        matchRule.setRuleConditions(Collections.singletonList(ruleCondition));
        Decision decision = new Decision();
        decision.setLogic("1");
        matchRule.setDecision(decision);

        weaknessRule.setMatchRule(matchRule);
        return weaknessRule;
    }

    private WeaknessRule createWeaknessOverRelianceDefaultConfiguration() {
        WeaknessRule weaknessRule = new WeaknessRule();
        weaknessRule.setId(2);
        weaknessRule.setType("架构设计类");
        weaknessRule.setName("过度依赖默认配置");
        weaknessRule.setLevel(1);
        MatchRule matchRule = new MatchRule();
        matchRule.setId("matchId1");

        RuleCondition ruleCondition1 = new RuleCondition();
        ruleCondition1.setSeq("1");
        ruleCondition1.setFeature("type");
        ruleCondition1.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition1.setOperator("EQ");
        ruleCondition1.setValue("MySQL");
        ruleCondition1.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition2 = new RuleCondition();
        ruleCondition2.setSeq("2");
        ruleCondition2.setFeature("netDstPort");
        ruleCondition2.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition2.setOperator("EQ");
        ruleCondition2.setValue(3306);
        ruleCondition2.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition3 = new RuleCondition();
        ruleCondition3.setSeq("3");
        ruleCondition3.setFeature("type");
        ruleCondition3.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition3.setOperator("EQ");
        ruleCondition3.setValue("PostgreSQL");
        ruleCondition3.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition4 = new RuleCondition();
        ruleCondition4.setSeq("4");
        ruleCondition4.setFeature("netDstPort");
        ruleCondition4.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition4.setOperator("EQ");
        ruleCondition4.setValue(5432);
        ruleCondition4.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition5 = new RuleCondition();
        ruleCondition5.setSeq("5");
        ruleCondition5.setFeature("type");
        ruleCondition5.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition5.setOperator("EQ");
        ruleCondition5.setValue("Oracle Database");
        ruleCondition5.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition6 = new RuleCondition();
        ruleCondition6.setSeq("6");
        ruleCondition6.setFeature("netDstPort");
        ruleCondition6.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition6.setOperator("EQ");
        ruleCondition6.setValue(1521);
        ruleCondition6.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition7 = new RuleCondition();
        ruleCondition7.setSeq("7");
        ruleCondition7.setFeature("type");
        ruleCondition7.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition7.setOperator("EQ");
        ruleCondition7.setValue("Microsoft SQL Server");
        ruleCondition7.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition8 = new RuleCondition();
        ruleCondition8.setSeq("8");
        ruleCondition8.setFeature("netDstPort");
        ruleCondition8.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition8.setOperator("EQ");
        ruleCondition8.setValue(1433);
        ruleCondition8.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition9 = new RuleCondition();
        ruleCondition9.setSeq("9");
        ruleCondition9.setFeature("type");
        ruleCondition9.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition9.setOperator("EQ");
        ruleCondition9.setValue("MongoDB");
        ruleCondition9.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition10 = new RuleCondition();
        ruleCondition10.setSeq("10");
        ruleCondition10.setFeature("netDstPort");
        ruleCondition10.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition10.setOperator("EQ");
        ruleCondition10.setValue(27017);
        ruleCondition10.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition11 = new RuleCondition();
        ruleCondition11.setSeq("11");
        ruleCondition11.setFeature("type");
        ruleCondition11.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition11.setOperator("EQ");
        ruleCondition11.setValue("Redis");
        ruleCondition11.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition12 = new RuleCondition();
        ruleCondition12.setSeq("12");
        ruleCondition12.setFeature("netDstPort");
        ruleCondition12.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition12.setOperator("EQ");
        ruleCondition12.setValue(6379);
        ruleCondition12.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition13 = new RuleCondition();
        ruleCondition13.setSeq("13");
        ruleCondition13.setFeature("type");
        ruleCondition13.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition13.setOperator("EQ");
        ruleCondition13.setValue("MariaDB");
        ruleCondition13.setValueType(ValueType.PRIMITIVE.name());

        RuleCondition ruleCondition14 = new RuleCondition();
        ruleCondition14.setSeq("14");
        ruleCondition14.setFeature("netDstPort");
        ruleCondition14.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition14.setOperator("EQ");
        ruleCondition14.setValue(3306);
        ruleCondition14.setValueType(ValueType.PRIMITIVE.name());


        List<RuleCondition> ruleConditions = new ArrayList<>();
        ruleConditions.add(ruleCondition1);
        ruleConditions.add(ruleCondition2);
        ruleConditions.add(ruleCondition3);
        ruleConditions.add(ruleCondition4);
        ruleConditions.add(ruleCondition5);
        ruleConditions.add(ruleCondition6);
        ruleConditions.add(ruleCondition7);
        ruleConditions.add(ruleCondition8);
        ruleConditions.add(ruleCondition9);
        ruleConditions.add(ruleCondition10);
        ruleConditions.add(ruleCondition11);
        ruleConditions.add(ruleCondition12);
        ruleConditions.add(ruleCondition13);
        ruleConditions.add(ruleCondition14);


        matchRule.setRuleConditions(ruleConditions);
        Decision decision = new Decision();
        decision.setLogic("((1&2)|(3&4)|(5&6)|(7&8)|(9&10)|(11&12)|(13&14))");
        matchRule.setDecision(decision);

        weaknessRule.setMatchRule(matchRule);
        return weaknessRule;
    }


    private WeaknessRule createWeaknessRule() {
        WeaknessRule weaknessRule = new WeaknessRule();
        weaknessRule.setId(1);
        weaknessRule.setType("测试弱点类型");
        weaknessRule.setName("select 全部字段弱点");
        weaknessRule.setLevel(1);
        MatchRule matchRule = new MatchRule();
        matchRule.setId("matchId1");

        RuleCondition ruleCondition = new RuleCondition();
        ruleCondition.setSeq("1");
        ruleCondition.setFeature("reqSql");
        ruleCondition.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition.setOperator("STR_CONTAINS_IGNORE_CASE");
        ruleCondition.setValue("select *");
        ruleCondition.setValueType(ValueType.PRIMITIVE.name());

        matchRule.setRuleConditions(Collections.singletonList(ruleCondition));
        Decision decision = new Decision();
        decision.setLogic("1");
        matchRule.setDecision(decision);

        weaknessRule.setMatchRule(matchRule);
        return weaknessRule;
    }

    @Test
    public void queryWeaknessRule() throws SQLException {
        String sql = "SELECT * FROM weakness_rule ";
        List<Map<String, Object>> maps = queryRunner.query(sql, new MapListHandler());
        System.out.println(maps);
    }


    @Test
    public void testMatchRule() throws Exception {
        WeaknessRule weaknessRule = new WeaknessRule();
        weaknessRule.setId(1);
        weaknessRule.setType("testType");
        weaknessRule.setName("testWeakness");
        weaknessRule.setLevel(1);

        MatchRule matchRule = new MatchRule();
        matchRule.setId("matchId");

        RuleCondition ruleCondition = new RuleCondition();
        ruleCondition.setSeq("1");
        ruleCondition.setType("");
        ruleCondition.setFeature("dbVersion");
        ruleCondition.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition.setOperator("EQ");
        ruleCondition.setValue("5.7");
        ruleCondition.setValueType(ValueType.PRIMITIVE.name());

        matchRule.setRuleConditions(Collections.singletonList(ruleCondition));
        Decision decision = new Decision();
        decision.setLogic("1");
        matchRule.setDecision(decision);

        weaknessRule.setMatchRule(matchRule);

        RuleEngine ruleEngine = new RuleEngine();
        CompiledRule compile = ruleEngine.compile(matchRule);

        Map<String, Object> map = new HashMap<>();
        map.put("dbVersion", "5.7");
        map.put("type", "MySQL");
        map.put("netDstPort", 3306);
        EventContext context = new EventContext(map);

        DecisionResult eval = ruleEngine.eval(compile, context);
        System.out.println(eval.isSuccess());


        WeaknessRule weaknessOverRelianceDefaultConfiguration = createWeaknessOverRelianceDefaultConfiguration();
        CompiledRule compile1 = ruleEngine.compile(weaknessOverRelianceDefaultConfiguration.getMatchRule());
        eval = ruleEngine.eval(compile1, context);
        System.out.println(eval.isSuccess());
    }

    @Test
    public void testRecognitionWeakness() {
        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("dbVersion", "5.7");
        sourceMap.put("dbName", "testDb");
        sourceMap.put("dbType", "mysql");
        sourceMap.put("svcName", "testSvcName");
        sourceMap.put("svcAddress", "testSvcAddress");
        sourceMap.put("visitDomains", Arrays.asList("互联网"));
        sourceMap.put("deployDomains", Arrays.asList("局域网"));
        sourceMap.put("reqSql", "select * from tablexx where ");
        List<WeaknessMetadata> maps = weaknessService.recognitionWeakness(sourceMap);
        System.out.println(maps);
        Assert.assertTrue(maps.size() == 1);
    }

    @Test
    public void testProcess() {
        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("timestamp", System.currentTimeMillis());
        sourceMap.put("assetId", "testAssetId");
        sourceMap.put("dbVersion", "5.7");
        sourceMap.put("dbName", "testDb");
        sourceMap.put("dbType", "mysql");
        sourceMap.put("svcName", "testSvcName");
        sourceMap.put("svcAddress", "testSvcAddress");
        sourceMap.put("visitDomains", Arrays.asList("互联网", "局域网"));
        sourceMap.put("deployDomains", Arrays.asList("局域网"));
        List<Map<String, Object>> maps = weaknessService.process(sourceMap);
        Assert.assertTrue(maps.size() == 1);
    }


}