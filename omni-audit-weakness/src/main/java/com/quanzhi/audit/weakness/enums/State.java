package com.quanzhi.audit.weakness.enums;

public enum State {
    /**
     * 首次发现弱点，待确认
     */
    NEW("待确认"),
    /**
     * 已挂起
     */
    SUSPEND("已挂起"),
    /**
     * 已忽略
     */
    IGNORED("已忽略"),
    /**
     * 待修复
     */
    REPAIRING("待修复"),
    /**
     * 待验证
     */
    VERIFYING("待验证"),
    /**
     * 该弱点已经被修复
     */
    FIXED("已修复"),
    /**
     * 该弱点是可疑的
     */
    SUSPECT("可疑"),
    /**
     * 该弱点修复后，再复现
     */
    REOPEN("再复现");

    private String value;

    State(String value) {
        this.value = value;
    }

    public String value() {
        return this.value;
    }
}
