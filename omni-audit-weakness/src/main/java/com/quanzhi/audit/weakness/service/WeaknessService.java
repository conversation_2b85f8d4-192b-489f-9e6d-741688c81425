package com.quanzhi.audit.weakness.service;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.weakness.constant.WeaknessConstant;
import com.quanzhi.audit.weakness.entity.Weakness;
import com.quanzhi.audit.weakness.entity.WeaknessMetadata;
import com.quanzhi.audit.weakness.entity.WeaknessRule;
import com.quanzhi.audit.weakness.enums.State;
import com.quanzhi.omni.audit.query.handler.CamelCaseMapHandler;
import com.quanzhi.omni.audit.query.handler.CamelCaseMapListHandler;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.quanzhi.re.core.RuleEngine;
import com.quanzhi.re.core.defaults.FeatureDefault;
import com.quanzhi.re.core.domain.engine.CompiledRule;
import com.quanzhi.re.core.domain.entity.Decision;
import com.quanzhi.re.core.domain.entity.DecisionResult;
import com.quanzhi.re.core.domain.entity.EventContext;
import com.quanzhi.re.core.domain.entity.ValueType;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import com.quanzhi.re.core.domain.entity.po.RuleCondition;
import lombok.extern.slf4j.Slf4j;
import omni.audit.common.client.redis.RedisClientWrapper;
import omni.audit.common.client.redis.RedisConfig;
import omni.audit.common.client.redis.RedissonClientFactory;
import omni.audit.common.util.DateUtil;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.MapHandler;
import org.apache.commons.dbutils.handlers.MapListHandler;
import org.apache.commons.dbutils.handlers.ScalarHandler;
import org.apache.commons.lang3.StringUtils;

import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.sql.Array;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * create at 2025/5/8 17:10
 * @description:
 **/
@Slf4j
public class WeaknessService {

    // 常量定义
    private static final String OPERATION_ID_PREFIX = "W";
    private static final String OPERATION_ID_SEPARATOR = "-";
    private static final int COUNTER_FORMAT_LENGTH = 5;
    private static final String REDIS_COUNTER_KEY_PREFIX = "weaknessCounter:";
    private static final String DATE_FORMAT_PATTERN = "yyMMdd";
    private static final int RANDOM_STRING_LENGTH = 5;

    private RuleEngine ruleEngine = new RuleEngine();

    private List<WeaknessRule> weaknessRules;

    private final QueryRunner queryRunner;

    private final Properties weaknessConfig;

    private RedisClientWrapper redisClientWrapper;

    public WeaknessService(JdbcConfig jdbcConfig, Properties weaknessConfig, RedisConfig redisConfig) {
        this.queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        this.weaknessConfig = weaknessConfig;
        init(redisConfig);
    }

    private void init(RedisConfig redisConfig) {
        initRedisClient(redisConfig);
        initWeaknessRules();
    }

    private void initRedisClient(RedisConfig redisConfig) {
        try {
            RedissonClientFactory.init(redisConfig);
            redisClientWrapper = new RedisClientWrapper();
        } catch (Exception e) {
            log.error("Failed to initialize Redis client", e);
        }
    }

    private void initWeaknessRules() {
        this.weaknessRules = getWeaknessRules();
        for (WeaknessRule weaknessRule : weaknessRules) {
            weaknessRule.setCompiledRule(ruleEngine.compile(weaknessRule.getMatchRule()));
        }
    }

    public List<Map<String, Object>> process(Object rowData) {
        try {
            // 弱点识别
            List<WeaknessMetadata> weaknessList = recognitionWeakness(rowData);
            // 弱点处理
            List<Map<String, Object>> result = processWeakness(weaknessList, rowData);
            // 聚合更新
            aggregateUpdates(result);
            return result;
        } catch (Exception e) {
            log.error("Failed to process weakness data", e);
            return Collections.emptyList();
        }
    }

    private void aggregateUpdates(List<Map<String, Object>> result) {
        Map<String, String> entityFieldMap = (Map<String, String>) weaknessConfig.getOrDefault(WeaknessConstant.ENTITY_FIELD_MAP, new HashMap<>());
        if (entityFieldMap.isEmpty()) {
            return;
        }
        String weaknessTable = weaknessConfig.getProperty(WeaknessConstant.WEAKNESS_TABLE, "weakness");
        for (Map<String, Object> map : result) {
            String weaknessId = (String) map.get("weaknessId");
            String entityType = (String) map.get("entityType");
            String primaryKey = entityFieldMap.get(entityType);
            String primaryKeyValue = (String) map.get(primaryKey);
            Map<String, Object> existWeakness = queryExistWeakness(weaknessTable, weaknessId, primaryKey, primaryKeyValue);
            if(existWeakness==null){
                continue;
            }
            map.put("operationId", existWeakness.get("operationId"));
            mergeFields(map, existWeakness);
        }
    }


    private Map<String, Object> queryExistWeakness(String weaknessTable, String weakknessId, String primaryKey, String primaryKeyValue) {
        StringBuilder sql = new StringBuilder("select * from ").append(weaknessTable).append(" where 1=1");
        sql.append(" AND weakness_id = ?").append(" ").append(primaryKey).append(" = ?");
        Object[] params = new Object[]{weakknessId, primaryKeyValue};
        try {
            return queryRunner.query(sql.toString(), new CamelCaseMapHandler(), params);
        } catch (SQLException e) {
            log.error("Failed to query existing record with SQL: {}", sql, e);
            return null;
        }
    }

    private String createOperationId() {
        try {
            if (redisClientWrapper != null) {
                return createOperationIdFromRedis();
            }
        } catch (Exception e) {
            log.error("Failed to create operationId from Redis", e);
        }
        return createOperationIdDefault();
    }

    private String createOperationIdFromRedis() {
        String formattedDate = getCurrentDateString();
        String counterKey = REDIS_COUNTER_KEY_PREFIX + formattedDate;

        initializeCounterIfNotExists(counterKey);
        Long riskCount = redisClientWrapper.incr(counterKey);
        String formattedCounter = String.format("%0" + COUNTER_FORMAT_LENGTH + "d", riskCount);

        return buildOperationId(formattedDate, formattedCounter);
    }

    private String getCurrentDateString() {
        long now = Instant.now().toEpochMilli();
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT_PATTERN);
        return dateFormat.format(now);
    }

    private void initializeCounterIfNotExists(String counterKey) {
        if (!redisClientWrapper.exists(counterKey)) {
            redisClientWrapper.incrBy(counterKey, 0L);
            redisClientWrapper.expire(counterKey, 1, TimeUnit.DAYS);
        }
    }

    private String createOperationIdDefault() {
        String formattedDate = DateUtil.getDateTime(DateUtil.DatePattern.YYYYMMDD);
        String randomString = RandomUtil.randomString(RANDOM_STRING_LENGTH);
        return buildOperationId(formattedDate, randomString);
    }

    private String buildOperationId(String date, String suffix) {
        return OPERATION_ID_PREFIX + OPERATION_ID_SEPARATOR + date + OPERATION_ID_SEPARATOR + suffix;
    }

    private void mergeFields(Map<String, Object> targetMap, Map<String, Object> sourceMap) {
        for (Map.Entry<String, Object> entry : sourceMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value instanceof Collection) {
                Collection<?> sourceCollection = (Collection<?>) value;
                Object targetValue = targetMap.get(key);

                if (targetValue == null) {
                    // 如果 targetMap 中没有该键，则直接放入一个新的不可变集合
                    targetMap.put(key, new HashSet<>(sourceCollection));
                } else if (targetValue instanceof Collection) {
                    // 如果 targetMap 中的值也是集合，则合并并去重
                    Collection<Object> mergedCollection = new HashSet<>();
                    mergedCollection.addAll((Collection<?>) targetValue);
                    mergedCollection.addAll(sourceCollection);
                    targetMap.put(key, mergedCollection);
                }
            }
        }
    }

    private List<Map<String, Object>> processWeakness(List<WeaknessMetadata> weaknessList, Object rowData) {
        Map<String, String> outputFieldMap = (Map<String, String>) weaknessConfig.get(WeaknessConstant.OUTPUT_FIELD_MAP);
        Map<String, Object> outputMap = new HashMap<>();
        outputFieldMap.forEach((targetField, sourceField) -> {
            outputMap.put(targetField, getFieldValue(rowData, sourceField));
        });
        Object timestamp = getFieldValue(rowData, "timestamp");
        List<Map<String, Object>> result = new ArrayList<>();
        for (WeaknessMetadata weaknessMetadata : weaknessList) {
            Map<String, Object> map = new HashMap<>();
            map.putAll(outputMap);
            map.put("weaknessId", weaknessMetadata.getWeaknessId());
            map.put("level", weaknessMetadata.getLevel());
            map.put("state", weaknessMetadata.getState());
            map.put("entityType", weaknessMetadata.getEntityType());
            map.put("discoverTime", timestamp);
            map.put("activeTime", timestamp);
            map.put("operationId", createOperationId());
            result.add(map);
        }
        return result;
    }

    private Object getFieldValue(Object rowData, String fieldName) {
        if (rowData instanceof Map) {
            return ((Map<String, Object>) rowData).get(fieldName);
        } else {
            try {
                Field field = rowData.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(rowData);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                log.debug("Failed to get field value: {}", fieldName, e);
                return null;
            }
        }
    }

    public List<WeaknessMetadata> recognitionWeakness(Object sourceEvent) {
        List<WeaknessMetadata> weaknessList = new ArrayList<>();
        EventContext context = new EventContext(sourceEvent);
        for (WeaknessRule weaknessRule : weaknessRules) {
            CompiledRule compiledRule = weaknessRule.getCompiledRule();
            try {
                DecisionResult decisionResult = ruleEngine.eval(compiledRule, context);
                if (decisionResult.isSuccess()) {
                    WeaknessMetadata weaknessMetadata = new WeaknessMetadata();
                    weaknessMetadata.setWeaknessId(weaknessRule.getId());
                    weaknessMetadata.setLevel(weaknessRule.getLevel());
                    weaknessMetadata.setState(State.NEW.value());
                    weaknessMetadata.setEntityType(weaknessRule.getEntityType());
                    weaknessList.add(weaknessMetadata);
                }
            } catch (Exception e) {
                log.error("recognitionWeakness {} error", weaknessRule.getName(), e);
            }
        }
        return weaknessList;
    }

    private List<WeaknessRule> getWeaknessRules() {
        List<WeaknessRule> weaknessRuleList = new ArrayList<>();
        try {
            List<Map<String, Object>> querys = queryRunner.query("select * from " + weaknessConfig.getProperty(
                    WeaknessConstant.WEAKNESS_RULE_TABLE,
                    "weakness_rule"), new CamelCaseMapListHandler());
            for (Map<String, Object> query : querys) {
                WeaknessRule weaknessRule = new WeaknessRule();
                weaknessRule.setId((Integer) query.get("id"));
                weaknessRule.setName((String) query.get("name"));
                weaknessRule.setType((String) query.get("type"));
                weaknessRule.setEntityType((String) query.get("entityType"));
                weaknessRule.setLevel((Integer) query.get("level"));
                weaknessRule.setMatchRule(JSON.parseObject(String.valueOf(query.get("matchRule")), MatchRule.class));
                weaknessRuleList.add(weaknessRule);
            }
        } catch (SQLException e) {
            log.error("query WeaknessRules error", e);
        }
        return weaknessRuleList;
    }
}